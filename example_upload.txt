curl --location 'localhost:8000/graphql' \
--header 'Authorization: JWT eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************.SrC0bSvhemLYxgSOd7ROaYDpPYre3-wPCeOUOzTfdDE' \
--form '0=@"/mnt/d/Downloaded/Data examples.xlsx"' \
--form 'map="{ \"0\": [\"variables.file\"] }"' \
--form 'operations="{	\"query\": \"mutation MyMutation($file: Upload!) {  uploadDistributionPatternData(fileIn: $file) {    result  }}\",	\"variables\": {		\"file\": null	}}"'
