#!/bin/bash

echo "check logging directory..."
if [ ! -d /var/log/dcs-app ]; then
  sudo mkdir -p /var/log/dcs-app
  sudo touch /var/log/dcs-app/gunicorn-access.log
  # sudo chown $USER:$USER /var/log/dcs-app/gunicorn-access.log
  sudo touch /var/log/dcs-app/gunicorn-error.log
  # sudo chown $USER:$USER /var/log/dcs-app/gunicorn-error.log
  sudo chmod -R 777 /var/log/dcs-app/
fi

echo "check virtualenv directory..."
if [ ! -d ~/dcs-app/venv ]; then
  ~/dcs-app/venv/bin/python manage.py migrate --settings=config.settings.prod
  sudo ~/dcs-app/venv/bin/python manage.py collectstatic --settings=config.settings.prod --noinput
fi

echo "check gunicorn process..."
if ps aux | grep '~/dcs-app/venv/bin/gunicorn' | grep -v grep | awk '{print $2}' > /dev/null
then
  echo "restart gunicorn..."
  kill -9 $(ps aux | grep '~/dcs-app/venv/bin/gunicorn' | awk '{print $2}')
	DJANGO_SETTINGS_MODULE=config.settings.prod ~/dcs-app/venv/bin/gunicorn --config gunicorn_cfg.py config.wsgi --daemon --reload
  echo "restart gunicorn finish..."
else
  echo "start gunicorn..."
  DJANGO_SETTINGS_MODULE=config.settings.prod ~/dcs-app/venv/bin/gunicorn --config gunicorn_cfg.py config.wsgi --daemon --reload
  echo "start gunicorn finish..."
fi


echo "deployment finish..."