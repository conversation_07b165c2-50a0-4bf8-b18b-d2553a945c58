# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-05-28 11:37+0700\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
#: app/common/utils.py:35
#, python-format
msgid "Password must contain uppercase at least %(min_length)d digit."
msgstr ""

#: app/common/utils.py:37
#, python-format
msgid "Password must contain at least %(min_length)d digit."
msgstr ""

#: app/common/utils.py:39
#, python-format
msgid "Password must contain at least %(min_length)d letter."
msgstr ""

#: app/common/utils.py:41
#, python-format
msgid "Password must contain at least %(min_length)d special character."
msgstr ""

#: app/master_data/apps.py:8
msgid "Master Data Management"
msgstr ""

#: app/master_data/forms.py:19 app/master_data/forms.py:49
#: app/master_data/forms.py:94 app/master_data/forms.py:115
msgid "This field is required."
msgstr ""

#: app/master_data/forms.py:22 app/master_data/forms.py:53
#: app/master_data/forms.py:86 app/master_data/forms.py:109
#: app/master_data/forms.py:139
msgid "Data already deleted"
msgstr ""

#: app/master_data/forms.py:90
msgid "Registered Number already exists."
msgstr ""

#: app/master_data/forms.py:92
#, python-format
msgid "Registered Number must contain at least %(min_length)d digit or letter."
msgstr ""

#: app/master_data/forms.py:113
msgid "Route Name already exists."
msgstr ""

#: app/master_data/models.py:33 app/user/models.py:24
msgid "User"
msgstr ""

#: app/master_data/models.py:39
msgid "Payment Recipient"
msgstr ""

#: app/master_data/models.py:40
msgid "Payment Recipients"
msgstr ""

#: app/user/models.py:13
msgid "full name"
msgstr ""

#: app/user/models.py:15
msgid "email address"
msgstr ""

#: app/user/models.py:25
msgid "Users"
msgstr ""
