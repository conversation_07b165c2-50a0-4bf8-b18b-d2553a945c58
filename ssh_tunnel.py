from sshtunnel import SSHTunnelForwarder
import environ
env = environ.Env()
environ.Env.read_env(env_file=".env")

def create_ssh_tunnel():
    server = SSHTunnelForwarder(
        (env('PROD_HOST'), 22),  # SSH server and port
        ssh_username=env('PROD_USERNAME'),
        ssh_pkey=env('PROD_PASSWORD'),  # or use ssh_pkey='path/to/private/key'
        remote_bind_address=('127.0.0.1', 5432),  # Remote database server and port
        local_bind_address=('127.0.0.1', 5432)  # Local port to forward to
    )
    server.start()
    return server

if __name__ == '__main__':
    server = create_ssh_tunnel()
    print(f"SSH tunnel established. Local bind address: {server.local_bind_address}")
    input("Press Enter to stop the tunnel...")
    server.stop()