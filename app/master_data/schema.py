import graphene
from graphene_django import DjangoConnectionField
from graphene_django.filter import DjangoFilterConnectionField
from graphql import GraphQLError
from graphql_jwt.decorators import login_required
from django.db.models import Q

from app.master_data.graphql.mutations import * # type: ignore
from app.master_data.graphql.types import * # type: ignore
from app.master_data.models import MstCompany, MstDistributionPattern, MstDistributionPatternGroup, MstPaymentRecipient, MstSaleRoute, MstSegment
from app.user.schema import PaymentRecipientNode


class Query(graphene.ObjectType):
    # This class will inherit from multiple Queries
    # as we begin to add more apps to our project
    all_recipient = DjangoFilterConnectionField(PaymentRecipientNode)
    all_segment = DjangoFilterConnectionField(SegmentNode)
    all_company = DjangoFilterConnectionField(CompanyNode)
    all_sale_route = DjangoConnectionField(SaleRouteNode)
    all_distribution_pattern = DjangoFilterConnectionField(DistributionPatternNode, search=graphene.String(required=False))
    all_distribution_pattern_group = DjangoFilterConnectionField(DistributionPatternGroupNode)
    all_artist = DjangoFilterConnectionField(ArtistNode)
    all_media_title = DjangoConnectionField(MediaTitleNode)
    all_product_category_group = DjangoFilterConnectionField(ProductCategoryGroupNode)
    all_artist_category_group = DjangoFilterConnectionField(ArtistCategoryGroupNode)

    @login_required
    def resolve_all_recipient(self, info, **kwargs):
        return MstPaymentRecipient.objects.order_by('-created_at').all()

    @login_required
    def resolve_all_segment(self, info, **kwargs):
        return MstSegment.objects.order_by('-created_at').all()

    @login_required
    def resolve_all_company(self, info, **kwargs):
        return MstCompany.objects.order_by('-created_at').all()

    @login_required
    def resolve_all_sale_route(self, info, **kwargs):
        return MstSaleRoute.objects.order_by('-created_at').all()

    @login_required
    def resolve_all_distribution_pattern(self, info, **kwargs):
        if 'search' in kwargs:
            search = kwargs.get('search')
            if search:
                return MstDistributionPattern.objects.filter(
                    Q(pattern_code__icontains=search) | Q(pattern_title__icontains=search)
                ).order_by('-created_at').all()
            else:
                raise GraphQLError("Search term is empty")
        return MstDistributionPattern.objects.order_by('-created_at').all()

    @login_required
    def resolve_all_distribution_pattern_group(self, info, **kwargs):
        return MstDistributionPatternGroup.objects.order_by('-created_at').all()

    @login_required
    def resolve_all_artist(self, info, **kwargs):
        return MstArtist.objects.order_by('-created_at').all()

    @login_required
    def resolve_all_media_title(self, info, **kwargs):
        return MediaTitle.objects.order_by('-created_at').all()

    @login_required
    def resolve_all_product_category_group(self, info, **kwargs):
        return ProductCategoryGroup.objects.order_by('-created_at').all()

    @login_required
    def resolve_all_artist_category_group(self, info, **kwargs):
        return ArtistCategoryGroup.objects.order_by('-created_at').all()


class Mutation(graphene.ObjectType):
    # This class will inherit from multiple Mutations
    # as we begin to add more apps to our project
    create_segment = CreateSegment.Field()
    create_company = CreateCompany.Field()
    create_payment_recipient = CreatePaymentRecipient.Field()
    create_sale_route = CreateSaleRoute.Field()
    delete_segment = DeleteSegment.Field()
    delete_company = DeleteCompany.Field()
    delete_payment_recipient = DeletePaymentRecipient.Field()
    delete_sale_route = DeleteSaleRoute.Field()
    upload_distribution_pattern_data = UploadDistributionPatternDataMutation.Field()
    create_distribution_pattern = CreateDistributionPattern.Field()
    delete_distribution_pattern = DeleteDistributionPattern.Field()
    create_distribution_pattern_group = CreateDistributionPatternGroup.Field()
    delete_distribution_pattern_group = DeleteDistributionPatternGroup.Field()
    create_artist = CreateArtist.Field()
    delete_artist = DeleteArtist.Field()
    create_media_title = CreateMediaTitle.Field()
    delete_media_title = DeleteMediaTitle.Field()
    export_distribution_pattern_data = ExportDistPatternMutation.Field()
    create_product_category_group = CreateProductCategoryGroup.Field()
    delete_product_category_group = DeleteProductCategoryGroup.Field()
    create_artist_category_group = CreateArtistCategoryGroup.Field()
    delete_artist_category_group = DeleteArtistCategoryGroup.Field()
    upload_product_category_group = UploadProductCategoryGroupMutation.Field()
    upload_artist = UploadArtistMutation.Field()
    upload_artist_category_group = UploadArtistCategoryGroupMutation.Field()
