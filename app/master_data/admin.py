from django.contrib import admin

from app.master_data.models import MstCom<PERSON><PERSON>, MstDistributionPattern, MstDistributionPatternDetail, MstDistributionPatternGroup, MstPaymentRecipient, MstSaleRoute, MstSegment

# Register your models here.
@admin.register(MstPaymentRecipient)
class MstPaymentRecipientAdmin(admin.ModelAdmin):
    list_display = ('recipient_name', 'registered_number',)
    list_per_page = 50
    filter_horizontal = ()

@admin.register(MstSegment)
class MstSegmentAdmin(admin.ModelAdmin):
    list_display = ('created_at', 'segment_name',)
    list_per_page = 50
    filter_horizontal = ()

@admin.register(MstCompany)
class MstCompanyAdmin(admin.ModelAdmin):
    list_display = ('created_at', 'company_name',)
    list_per_page = 50
    filter_horizontal = ()

@admin.register(MstSaleRoute)
class MstSaleRouteAdmin(admin.ModelAdmin):
    list_display = ('created_at', 'route_name', 'route_code')
    list_per_page = 50
    filter_horizontal = ()

class MstDistributionPatternInlineAdmin(admin.StackedInline):
    model = MstDistributionPatternDetail
    # extra = 0
    # fields = ['pattern_detail__name']

    # def distribution_pattern_detail_name(self, instance):
    #     return instance.pattern_detail.name
    # distribution_pattern_detail_name.short_description = 'distribution pattern detail name'

@admin.register(MstDistributionPattern)
class MstDistributionPatternAdmin(admin.ModelAdmin):
    list_display = ('created_at', 'pattern_title')
    list_per_page = 50
    filter_horizontal = ('pattern_detail',)
    # inlines = [MstDistributionPatternInlineAdmin]

@admin.register(MstDistributionPatternDetail)
class MstDistributionPatternDetailAdmin(admin.ModelAdmin):
    list_display = ('created_at', 'recipient', 'percentage')
    list_per_page = 50
    filter_horizontal = ()
    # inlines = [MstDistributionPatternInlineAdmin]

@admin.register(MstDistributionPatternGroup)
class MstDistributionPatternGroupAdmin(admin.ModelAdmin):
    list_display = ('created_at', 'group_name')
    list_per_page = 50
    filter_horizontal = ()
