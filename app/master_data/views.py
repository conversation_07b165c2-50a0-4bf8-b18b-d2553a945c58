from django.http import HttpResponse
from django.shortcuts import render

from app.master_data import usecase

# Create your views here.
# Create view to receive post request file and register distribution data usecase
def register_distribution(request):
    if request.method == 'POST':
        file = request.FILES['file']
        usecase.register_distribution_data(file)
        return HttpResponse('Distribution data has been registered successfully')
    return HttpResponse('Register Distribution Data')

# Create view to receive post request file and register recipient usecase
def register_recipient(request):
    if request.method == 'POST':
        file = request.FILES['file']
        usecase.register_recipient(file)
        return HttpResponse('Recipient data has been registered successfully')
    return HttpResponse('Register Recipient Data')

def register_media_title(request):
    if request.method == 'POST':
        file = request.FILES['file']
        usecase.register_media_title(file)
        return HttpResponse('Media title data has been registered successfully')
    return HttpResponse('Register Media Title Data')