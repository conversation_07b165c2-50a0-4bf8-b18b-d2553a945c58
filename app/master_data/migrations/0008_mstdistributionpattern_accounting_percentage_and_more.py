# Generated by Django 4.2.13 on 2024-05-25 13:34

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('master_data', '0007_alter_mstpaymentrecipient_options_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='mstdistributionpattern',
            name='accounting_percentage',
            field=models.FloatField(blank=True, default=100, null=True),
        ),
        migrations.AddField(
            model_name='mstdistributionpattern',
            name='distributor_company',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='dist_distribution_pattern', to='master_data.mstcompany'),
        ),
        migrations.AddField(
            model_name='mstdistributionpattern',
            name='distributor_percentage',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='mstdistributionpattern',
            name='accounting_company',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='acc_distribution_pattern', to='master_data.mstcompany'),
        ),
        migrations.AlterField(
            model_name='mstdistributionpatterndetail',
            name='percentage',
            field=models.FloatField(blank=True, null=True),
        ),
    ]
