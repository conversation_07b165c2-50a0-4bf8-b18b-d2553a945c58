# Generated by Django 4.2.13 on 2025-02-18 12:04

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('master_data', '0022_mstartist_artist_name_abbv'),
    ]

    operations = [
        migrations.CreateModel(
            name='ArtistCategoryGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('name', models.CharField(max_length=255, unique=True, verbose_name='Name')),
            ],
            options={
                'verbose_name': 'Artist Category Group',
                'verbose_name_plural': 'Artist Category Groups',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProductCategoryGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('name', models.CharField(max_length=255, unique=True, verbose_name='Name')),
            ],
            options={
                'verbose_name': 'Product Category Group',
                'verbose_name_plural': 'Product Category Groups',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='mstartist',
            name='artist_category_group',
            field=models.ForeignKey(blank=True, db_constraint=False, error_messages={'protected': 'すでにアーティストと紐づいているため削除できません'}, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='artist', to='master_data.artistcategorygroup'),
        ),
    ]
