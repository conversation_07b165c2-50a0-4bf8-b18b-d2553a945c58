# Generated by Django 4.2.13 on 2024-05-22 13:04

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('master_data', '0005_alter_mstdistributionpattern_pattern_detail_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='mstcompany',
            options={'ordering': ['-created_at'], 'verbose_name': 'Data Company', 'verbose_name_plural': 'Data Companies'},
        ),
        migrations.AlterModelOptions(
            name='mstdistributionpattern',
            options={'ordering': ['-created_at'], 'verbose_name': 'Data Distribution Pattern', 'verbose_name_plural': 'Data Distribution Patterns'},
        ),
        migrations.AlterModelOptions(
            name='mstdistributionpatterndetail',
            options={'ordering': ['-created_at'], 'verbose_name': 'Data Distribution Pattern Detail', 'verbose_name_plural': 'Data Distribution Pattern Details'},
        ),
        migrations.AlterModelOptions(
            name='mstdistributionpatterngroup',
            options={'ordering': ['-created_at'], 'verbose_name': 'Data Distribution Pattern Group', 'verbose_name_plural': 'Data Distribution Pattern Groups'},
        ),
        migrations.AlterModelOptions(
            name='mstsaleroute',
            options={'ordering': ['-created_at'], 'verbose_name': 'Data Sale Route', 'verbose_name_plural': 'Data Sale Routes'},
        ),
        migrations.AlterModelOptions(
            name='mstsegment',
            options={'ordering': ['-created_at'], 'verbose_name': 'Data Segment', 'verbose_name_plural': 'Data Segments'},
        ),
        migrations.AddField(
            model_name='mstcompany',
            name='is_delete',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='mstdistributionpattern',
            name='is_delete',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='mstdistributionpatterndetail',
            name='is_delete',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='mstdistributionpatterngroup',
            name='is_delete',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='mstpaymentrecipient',
            name='is_delete',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='mstsaleroute',
            name='is_delete',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='mstsegment',
            name='is_delete',
            field=models.BooleanField(default=False),
        ),
    ]
