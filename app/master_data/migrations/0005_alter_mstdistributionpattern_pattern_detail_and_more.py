# Generated by Django 4.2.13 on 2024-05-22 12:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('master_data', '0004_mstdistributionpattern_accounting_company_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='mstdistributionpattern',
            name='pattern_detail',
            field=models.ManyToManyField(blank=True, related_name='distribution_pattern', to='master_data.mstdistributionpatterndetail'),
        ),
        migrations.AlterField(
            model_name='mstdistributionpatterndetail',
            name='position',
            field=models.CharField(blank=True, choices=[('2nd_recipient', '2nd Recipient Company (Percentage)'), ('3rd_recipient', 'Recipient (Percentage)')], null=True, verbose_name='Position'),
        ),
    ]
