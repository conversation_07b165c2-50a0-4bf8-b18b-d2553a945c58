# Generated by Django 4.2.13 on 2024-05-21 23:20

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('master_data', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='MstDistributionPattern',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('pattern_title', models.CharField(blank=True, max_length=100, null=True)),
            ],
            options={
                'verbose_name': 'Data Distribution Pattern',
                'verbose_name_plural': 'Data Distribution Patterns',
            },
        ),
        migrations.CreateModel(
            name='MstSaleRoute',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('route_code', models.CharField(blank=True, max_length=100, null=True, unique=True)),
                ('route_name', models.CharField(blank=True, max_length=100, null=True)),
            ],
            options={
                'verbose_name': 'Data Sale Route',
                'verbose_name_plural': 'Data Sale Routes',
            },
        ),
        migrations.CreateModel(
            name='MstDistributionPatternGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('group_name', models.CharField(blank=True, max_length=100, null=True)),
                ('pattern', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='distribution_pattern_group', to='master_data.mstdistributionpattern')),
                ('sale_route', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='distribution_pattern_group', to='master_data.mstsaleroute')),
            ],
            options={
                'verbose_name': 'Data Distribution Pattern Group',
                'verbose_name_plural': 'Data Distribution Pattern Groups',
            },
        ),
        migrations.CreateModel(
            name='MstDistributionPatternDetail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(blank=True, max_length=100, null=True)),
                ('percentage', models.TextField(blank=True, max_length=255, null=True)),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='distribution_pattern_detail', to='master_data.mstcompany')),
            ],
            options={
                'verbose_name': 'Data Distribution Pattern Detail',
                'verbose_name_plural': 'Data Distribution Pattern Details',
            },
        ),
        migrations.AddField(
            model_name='mstdistributionpattern',
            name='pattern_detail',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='distribution_pattern', to='master_data.mstdistributionpatterndetail'),
        ),
    ]
