# Generated by Django 4.2.13 on 2024-05-21 23:07

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='MstCompany',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('company_code', models.CharField(blank=True, max_length=100, null=True, unique=True)),
                ('company_name', models.CharField(blank=True, max_length=100, null=True)),
            ],
            options={
                'verbose_name': 'Data Company',
                'verbose_name_plural': 'Data Companies',
            },
        ),
        migrations.CreateModel(
            name='MstSegment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('segment_code', models.CharField(blank=True, max_length=100, null=True, unique=True)),
                ('segment_name', models.CharField(blank=True, max_length=100, null=True)),
            ],
            options={
                'verbose_name': 'Data Segment',
                'verbose_name_plural': 'Data Segments',
            },
        ),
        migrations.CreateModel(
            name='MstPaymentRecipient',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('recipient_name', models.CharField(blank=True, max_length=255, null=True)),
                ('billing_name', models.CharField(blank=True, max_length=255, null=True)),
                ('registered_number', models.CharField(blank=True, max_length=255, null=True)),
                ('registered_date', models.DateField(blank=True, null=True)),
                ('user', models.ManyToManyField(blank=True, related_name='payment_recipient', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'Payment Recipient',
                'verbose_name_plural': 'Payment Recipients',
                'ordering': ['registered_date'],
            },
        ),
    ]
