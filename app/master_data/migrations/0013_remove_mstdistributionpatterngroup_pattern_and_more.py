# Generated by Django 4.2.13 on 2024-05-30 12:11

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('master_data', '0012_rename_user_mstpaymentrecipient_manager_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='mstdistributionpatterngroup',
            name='pattern',
        ),
        migrations.RemoveField(
            model_name='mstdistributionpatterngroup',
            name='sale_route',
        ),
        migrations.CreateModel(
            name='MstDistributionPatternGroupDetail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('pattern', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='distribution_pattern_group_detail', to='master_data.mstdistributionpattern')),
                ('sale_route', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='distribution_pattern_group_detail', to='master_data.mstsaleroute')),
            ],
            options={
                'verbose_name': 'Data Distribution Pattern Group Detail',
                'verbose_name_plural': 'Data Distribution Pattern Group Details',
                'ordering': ['-created_at'],
            },
        ),
    ]
