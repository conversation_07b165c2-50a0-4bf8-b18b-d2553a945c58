# Generated by Django 4.2.13 on 2024-05-28 10:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('master_data', '0011_remove_mstdistributionpatterndetail_name_and_more'),
    ]

    operations = [
        migrations.RenameField(
            model_name='mstpaymentrecipient',
            old_name='user',
            new_name='manager',
        ),
        migrations.AlterField(
            model_name='mstcompany',
            name='company_code',
            field=models.CharField(blank=True, max_length=100, null=True, unique=True, verbose_name='Code'),
        ),
        migrations.AlterField(
            model_name='mstcompany',
            name='company_name',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name'),
        ),
        migrations.AlterField(
            model_name='mstdistributionpattern',
            name='accounting_percentage',
            field=models.FloatField(blank=True, default=100, null=True, verbose_name='Accounting Percentage'),
        ),
        migrations.<PERSON><PERSON><PERSON>ield(
            model_name='mstdistributionpattern',
            name='distributor_percentage',
            field=models.FloatField(blank=True, null=True, verbose_name='Distribution Percentage'),
        ),
        migrations.AlterField(
            model_name='mstdistributionpattern',
            name='pattern_code',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Code'),
        ),
        migrations.AlterField(
            model_name='mstdistributionpattern',
            name='pattern_title',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Title'),
        ),
        migrations.AlterField(
            model_name='mstdistributionpatterndetail',
            name='percentage',
            field=models.FloatField(blank=True, null=True, verbose_name='Recipient Percentage'),
        ),
        migrations.AlterField(
            model_name='mstdistributionpatterngroup',
            name='group_code',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Code'),
        ),
        migrations.AlterField(
            model_name='mstdistributionpatterngroup',
            name='group_name',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name'),
        ),
        migrations.AlterField(
            model_name='mstpaymentrecipient',
            name='billing_name',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Billing Name'),
        ),
        migrations.AlterField(
            model_name='mstpaymentrecipient',
            name='recipient_name',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Recipient Name'),
        ),
        migrations.AlterField(
            model_name='mstpaymentrecipient',
            name='registered_number',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Registered Number'),
        ),
        migrations.AlterField(
            model_name='mstsaleroute',
            name='route_code',
            field=models.CharField(blank=True, max_length=100, null=True, unique=True, verbose_name='Code'),
        ),
        migrations.AlterField(
            model_name='mstsaleroute',
            name='route_name',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name'),
        ),
        migrations.AlterField(
            model_name='mstsegment',
            name='segment_code',
            field=models.CharField(blank=True, max_length=100, null=True, unique=True, verbose_name='Code'),
        ),
        migrations.AlterField(
            model_name='mstsegment',
            name='segment_name',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Name'),
        ),
    ]
