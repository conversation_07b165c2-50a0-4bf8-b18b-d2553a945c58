# Generated by Django 4.2.13 on 2024-08-10 05:52

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('master_data', '0018_mstdistributionpattern_third_recipient_company_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='MediaTitle',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('title_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='Name')),
                ('title_code', models.CharField(blank=True, max_length=100, null=True, unique=True, verbose_name='Code')),
                ('starter_royalty', models.FloatField(blank=True, null=True, verbose_name='Starter Royalty')),
                ('advance_royalty', models.FloatField(blank=True, null=True, verbose_name='Advance Royalty')),
                ('switch_sale_amount', models.FloatField(blank=True, null=True, verbose_name='Switch Sale Amount')),
            ],
            options={
                'verbose_name': 'Data Media Title',
                'verbose_name_plural': 'Data Media Titles',
                'ordering': ['-created_at'],
            },
        ),
    ]
