# Generated by Django 4.2.13 on 2024-06-20 00:30

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('master_data', '0015_mstsegment_segment_host'),
    ]

    operations = [
        migrations.CreateModel(
            name='MstArtist',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('artist_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='Name')),
            ],
            options={
                'verbose_name': 'Data Artist',
                'verbose_name_plural': 'Data Artists',
                'ordering': ['-created_at'],
            },
        ),
    ]
