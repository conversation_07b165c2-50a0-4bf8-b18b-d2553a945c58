import re
from typing import Any
from django import forms
from django.db.models import Q
from django.utils.translation import gettext as _
from graphql_relay import from_global_id

from app.common import utils
from app.goods.models import ProductDetail
from app.master_data.models import ArtistCategoryGroup, MediaTitle, MstArtist, MstCompany, MstDistributionPatternDetail, MstDistributionPatternGroup, MstDistributionPatternGroupDetail, MstPaymentRecipient, MstSaleRoute, MstSegment, MstDistributionPattern, ProductCategoryGroup
from app.project.models import Project


class MstSegmentForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        for field in self.Meta.required:
            self.fields[field].required = True

    class Meta:
        model = MstSegment
        fields = ('segment_name', 'segment_host', 'id')
        required = ['segment_name', 'segment_host']

    def clean(self):
        clean_data = super().clean()
        if self.instance.pk and self.instance.is_delete:
            raise forms.ValidationError(_("Data already deleted"))

        if not clean_data.get('segment_name'):
            raise forms.ValidationError({'segment_name': [_("This field is required."),]})

        if 'segment_host' in clean_data:
            if clean_data.get('segment_host') < 0:
                raise forms.ValidationError({'segment_host': [_("Host must be greater than 0."),]})

        return clean_data

    def save(self, commit=True):
        obj = super().save(commit=False)
        if not 'segment_code' in self.cleaned_data and not obj.segment_code:
            segment_code = f"{obj.segment_name.lower().replace(' ', '-')}-{utils.generate_random_string(6)}"
            obj.segment_code = segment_code
        if commit:
            obj.save()
        return obj

class MstCompanyForm(forms.ModelForm):
    # def __init__(self, *args, **kwargs):
    #     super().__init__(*args, **kwargs)

    #     for field in self.Meta.required:
    #         self.fields[field].required = True

    def clean(self):
        clean_data = super().clean()
        if self.instance.pk and self.instance.is_delete:
            raise forms.ValidationError(_("Data already deleted"))

        if not clean_data.get('company_name'):
            raise forms.ValidationError({'company_name': [_("This field is required."),]})

        return clean_data

    class Meta:
        model = MstCompany
        fields = ('company_name', 'id')
        # required = ('company_name')

    def save(self, commit=True):
        obj = super().save(commit=False)
        if not 'company_code' in self.cleaned_data and not obj.company_code:
            company_code = f"{obj.company_name.lower().replace(' ', '-')}-{utils.generate_random_string(6)}"
            obj.company_code = company_code
        if commit:
            obj.save()
        return obj

class MstPaymentRecipientForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        for field in self.Meta.required:
            self.fields[field].required = True

    class Meta:
        model = MstPaymentRecipient
        fields = ('recipient_name', 'billing_name', 'registered_number', 'id')
        required = ('recipient_name', 'billing_name')

    def clean(self):
        cleaned_data = super().clean()
        if self.instance.pk and self.instance.is_delete:
            raise forms.ValidationError(_("Data already deleted"))

        registered_number = cleaned_data.get('registered_number')
        if registered_number:
            if not self.instance.pk and MstPaymentRecipient.objects.filter(registered_number=registered_number).exists():
                raise forms.ValidationError({'registered_number': [_('Registered Number already exists.'),]})
            if self.instance.pk and MstPaymentRecipient.objects.filter(registered_number=registered_number).exclude(pk=self.instance.pk).exists():
                raise forms.ValidationError({'registered_number': [_('Registered Number already exists.'),]})
            if not bool(re.search('[a-zA-Z0-9]', registered_number)):
                raise forms.ValidationError({'registered_number': [_('Registered Number must contain at least %(min_length)d digit or letter.') % {'min_length': 1},]})
        else:
            raise forms.ValidationError({'registered_number': [_("This field is required."),]})

        return cleaned_data

class MstSaleRouteForm(forms.ModelForm):
    # def __init__(self, *args, **kwargs):
    #     super().__init__(*args, **kwargs)

    #     for field in self.Meta.required:
    #         self.fields[field].required = True

    def clean(self):
        clean_data = super().clean()
        if self.instance.pk and self.instance.is_delete:
            raise forms.ValidationError(_("Data already deleted"))

        if clean_data.get('route_name'):
            if MstSaleRoute.objects.filter(route_name=clean_data.get('route_name')).exists():
                raise forms.ValidationError({'route_name': [_('Route Name already exists.'),]})
        else:
            raise forms.ValidationError({'route_name': [_("This field is required."),]})

        return clean_data

    def save(self, commit=True):
        obj = super().save(commit=False)
        if not 'route_code' in self.cleaned_data or not obj.route_code:
            route_code = f"{obj.route_name.lower().replace(' ', '-')}-{utils.generate_random_string(6)}"
            obj.route_code = route_code
        if commit:
            obj.save()
        return obj

    class Meta:
        model = MstSaleRoute
        fields = ('route_name', 'id')
        # required = ('route_name')

class MstDistributionPatternForm(forms.ModelForm):
    pattern_detail = forms.JSONField(required=True)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        for field in self.Meta.required:
            self.fields[field].required = True

    def clean(self):
        clean_data = super().clean()
        if self.instance.pk and self.instance.is_delete:
            raise forms.ValidationError(_("Data already deleted"))

        if 'accounting_percentage' in clean_data:
            if clean_data.get('accounting_percentage') < 0 or clean_data.get('accounting_percentage' ) > 100:
                raise forms.ValidationError({'accounting_percentage': [_('Accounting Percentage must be greater than 0 or less than 100.'),]})

        if 'distributor_percentage' in clean_data:
            if clean_data.get('distributor_percentage') < 0 or clean_data.get('distributor_percentage') > 100:
                raise forms.ValidationError({'distributor_percentage': [_('Distributor Percentage must be greater than 0 or less than 100.'),]})

        if 'third_recipient_percentage' in clean_data:
            if clean_data.get('third_recipient_percentage') < 0 or clean_data.get('third_recipient_percentage') > 100:
                raise forms.ValidationError({'third_recipient_percentage': [_('3rd Distributor Percentage must be greater than 0 or less than 100.'),]})

        if 'pattern_detail' in clean_data:
            is_id_exist = utils.check_key_in_objects('id', clean_data.get('pattern_detail'))
            is_percentage_exist = utils.check_key_in_objects('percentage', clean_data.get('pattern_detail'))
            if not is_id_exist:
                raise forms.ValidationError({'pattern_detail': [_('Recipient ID is required.'),]})
            if not is_percentage_exist:
                raise forms.ValidationError({'pattern_detail': [_('Recipient Percentage is required.'),]})

            total_percentage = 0
            for data in clean_data.get('pattern_detail'):
                if not MstPaymentRecipient.objects.filter(id=data['id']).exists():
                    raise forms.ValidationError({'pattern_detail': [_('Recipient ID not found.'),]})
                if data['percentage'] < 0 or data['percentage'] > 100:
                    raise forms.ValidationError({'pattern_detail': [_('Recipient Percentage must be greater than 0 or more than 100.'),]})
                total_percentage += data['percentage']

            if total_percentage < 0 or total_percentage > 100:
                raise forms.ValidationError({'pattern_detail': [_('Total percentage must be 100 or less.'),]})

        if self.instance.pk and Project.objects.filter(Q(pattern=self.instance.pk) | Q(overrided_pattern=self.instance.pk)).exists():
            raise forms.ValidationError({'pattern_title': [_('Pattern Title already registered to the project.'),]})

        return clean_data

    class Meta:
        model = MstDistributionPattern
        fields = "__all__"
        required = ['pattern_title','accounting_company','accounting_percentage','distributor_company','distributor_percentage', 'third_recipient_company', 'third_recipient_percentage','pattern_detail']

    def save(self, commit=True):
        model = super().save(commit=False)
        model.pattern_code = f"DIST{utils.generate_random_string(5)}" if not self.instance.pattern_code else self.instance.pattern_code
        pattern_detail_clean = self.cleaned_data.get('pattern_detail')
        pattern_list = []
        for data in pattern_detail_clean:
            pattern_detail, created = MstDistributionPatternDetail.objects.get_or_create(
                # recipient_id=from_global_id(data['id'])[0],
                recipient_id=data['id'],
                percentage=data['percentage'],
            )
            pattern_list.append(pattern_detail)
        if commit:
            model.save()
        if pattern_list:
            model.pattern_detail.set(pattern_list, clear=True)
        return model

class MstDistributionPatternGroupForm(forms.ModelForm):
    pattern_group = forms.JSONField(required=True)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        for field in self.Meta.required:
            self.fields[field].required = True

    def clean(self):
        clean_data = super().clean()
        if self.instance.pk and self.instance.is_delete:
            raise forms.ValidationError(_("Data already deleted"))

        if 'pattern_group' in clean_data:
            is_id_exist = utils.check_key_in_objects('code_id', clean_data.get('pattern_group'))
            is_percentage_exist = utils.check_key_in_objects('pattern_id', clean_data.get('pattern_group'))
            if not is_id_exist:
                raise forms.ValidationError({'pattern_group': [_('Code ID is required.'),]})
            if not is_percentage_exist:
                raise forms.ValidationError({'pattern_group': [_('Pattern ID is required.'),]})
            # if utils.has_duplicate_elements(clean_data.get('pattern_group')):
            #     raise forms.ValidationError({'pattern_group': [_('Duplicate elements found.'),]})

            for data in clean_data.get('pattern_group'):
                if not MstSaleRoute.objects.filter(id=data['code_id']).exists():
                    raise forms.ValidationError({'pattern_group': [_('Sale route not found.'),]})
                if not MstDistributionPattern.objects.filter(id=data['pattern_id']).exists():
                    raise forms.ValidationError({'pattern_group': [_('Distribution Pattern not found.'),]})
                if not self.instance.pk and MstDistributionPatternGroupDetail.objects.filter(
                    pattern_group__group_name=clean_data.get('group_name'),
                    sale_route_id=data['code_id'],
                    pattern_id=data['pattern_id']
                ).exists():
                    raise forms.ValidationError({'pattern_group': [_(f"Data {data['code_id']} with {data['pattern_id']} already exists."),]})

        return clean_data

    class Meta:
        model = MstDistributionPatternGroup
        fields = "__all__"
        required = ['group_name']

    def save(self, commit=True):
        model = super().save(commit=False)
        if not self.cleaned_data['group_code'] or not model.group_code:
            group_code = f"{model.group_name.lower().replace(' ', '-')}-{utils.generate_random_string(6)}"
            model.group_code = group_code
        if commit:
            model.save()
        pattern_clean = self.cleaned_data['pattern_group']
        for data in pattern_clean:
            sale_route = MstSaleRoute.objects.filter(id=data['code_id']).first()
            pattern = MstDistributionPattern.objects.filter(id=data['pattern_id']).first()
            pattern_group, created = MstDistributionPatternGroupDetail.objects.get_or_create(
                pattern_group=model,
                sale_route=sale_route,
                pattern=pattern,
            )
        return model

class MstArtistForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        for field in self.Meta.required:
            self.fields[field].required = True

    class Meta:
        model = MstArtist
        fields = '__all__'
        required = ['artist_name', 'artist_category_group']

    def clean(self):
        clean_data = super().clean()
        if self.instance.pk and self.instance.is_delete:
            raise forms.ValidationError(_("Data already deleted"))

        if MstArtist.objects.filter(artist_name__iexact=clean_data.get('artist_name')).exclude(pk=self.instance.pk).exists():
            raise forms.ValidationError({'artist_name': [_('Artist Name already exists.'),]})

        return clean_data

    def save(self, commit=True):
        obj = super().save(commit=False)
        if not obj.artist_name_abbv and not self.cleaned_data.get('artist_name_abbv'):
            obj.artist_name_abbv = utils.abbreviate(obj.artist_name)

        if commit:
            obj.save()
        return obj

class MediaTitleForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        for field in self.Meta.required:
            self.fields[field].required = True

    class Meta:
        model = MediaTitle
        fields = '__all__'
        required = ['name','code','starter_royalty','advance_royalty','switch_sale_amount']

    def clean(self):
        clean_data = super().clean()
        if self.instance.pk and self.instance.is_delete:
            raise forms.ValidationError(_("Data already deleted"))

        if MediaTitle.objects.filter(name__iexact=clean_data.get('name')).exclude(pk=self.instance.pk).exists():
            raise forms.ValidationError({'name': [_('Title already exists.'),]})

        if MediaTitle.objects.filter(code=clean_data.get('code')).exclude(pk=self.instance.pk).exists():
            raise forms.ValidationError({'code': [_('Code already exists.'),]})

        if clean_data.get('switch_sale_amount') <= 0.0:
            raise forms.ValidationError({'switch_sale_amount': [_('Switch Sale Amount must be greater than 0.'),]})

        if clean_data.get('starter_royalty') < 0.0:
            raise forms.ValidationError({'starter_royalty': [_('Starter Royalty must be greater than 0.'),]})

        if clean_data.get('advance_royalty') < 0.0:
            raise forms.ValidationError({'advance_royalty': [_('Advance Royalty must be greater than 0.'),]})

        return clean_data

    def save(self, commit=True):
        obj = super().save(commit=False)

        if commit:
            obj.save()
        return obj

class ProductCategoryGroupForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        for field in self.Meta.required:
            self.fields[field].required = True

    class Meta:
        model = ProductCategoryGroup
        fields = '__all__'
        required = ['name']

    def clean(self):
        clean_data = super().clean()

        if ProductCategoryGroup.objects.filter(name__iexact=clean_data.get('name')).exclude(pk=self.instance.pk).exists():
            raise forms.ValidationError({'name': [_('Name already exists.'),]})

        return clean_data

    def save(self, commit=True):
        obj = super().save(commit=False)
        if commit:
            obj.save()
        return obj

class ArtistCategoryGroupForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        for field in self.Meta.required:
            self.fields[field].required = True

    class Meta:
        model = ArtistCategoryGroup
        fields = '__all__'
        required = ['name']

    def clean(self):
        clean_data = super().clean()

        if ArtistCategoryGroup.objects.filter(name__iexact=clean_data.get('name')).exclude(pk=self.instance.pk).exists():
            raise forms.ValidationError({'name': [_('Name already exists.'),]})

        return clean_data

    def save(self, commit=True):
        obj = super().save(commit=False)
        if commit:
            obj.save()
        return obj

