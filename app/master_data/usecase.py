from datetime import datetime
from io import BytesIO
import logging
import re
from django.forms import model_to_dict
from django.db import transaction
import numpy as np
import pandas as pd
from django.core.files.base import ContentFile

from app.common import usecase, utils
from app.master_data.models import MediaTitle, MstCompany, MstDistributionPattern, MstDistributionPatternDetail, MstDistributionPatternGroup, MstDistributionPatternGroupDetail, MstPaymentRecipient, MstSaleRoute
from app.user.models import User

logger = logging.getLogger(__name__)

def register_distribution_data(file_in):
    excel = pd.ExcelFile(file_in)
    distribution_company_data = excel.parse(0).dropna(how='all').replace(np.nan, None, regex=True)
    with transaction.atomic():
        for data in distribution_company_data.to_dict(orient="records"):
            print(f"distribution_company_data: {data}")
            logging.info(f"distribution_company_data: {data}")

            if not data.get('Distribution percentage'):
                print(f"Distribution percentage {data.get('Distribution percentage')} is required")
                logging.info(f"Distribution percentage {data.get('Distribution percentage')} is required")
                continue

            accounting_company = MstCompany.objects.filter(company_name=data.get('1st recipient company')).first()
            if not accounting_company:
                print(f"Accounting company {data.get('1st recipient company')} not found")
                logging.info(f"Accounting company {data.get('1st recipient company')} not found")
                continue

            distributor_company = MstCompany.objects.filter(company_name=data.get('1st recipient company')).first()
            if not distributor_company:
                print(f"Distributor company {data.get('1st recipient company')} not found")
                logging.info(f"Distributor company {data.get('1st recipient company')} not found")
                continue

            third_distributor_company = MstCompany.objects.filter(company_name=data.get('3rd recipient company')).first()
            if not third_distributor_company:
                print(f"3rd recipient company {data.get('3rd recipient company')} not found")
                logging.info(f"3rd recipient company {data.get('3rd recipient company')} not found")
                continue

            dist_pattern, created = MstDistributionPattern.objects.update_or_create(
                pattern_code=data.get('Distribution pattern code'),
                defaults={
                    'pattern_title': data.get('Title'),
                    'accounting_company': accounting_company,
                    'distributor_company': distributor_company,
                    'distributor_percentage': data.get('Distribution percentage'),
                    'third_recipient_company': third_distributor_company,
                    'third_recipient_percentage': data.get('3rd recipient percentage'),
                }
            )

            distribution_recipient_data = excel.parse(1).dropna(how='all').replace(np.nan, None, regex=True)
            for data in distribution_recipient_data.to_dict(orient="records"):
                print(f"distribution_recipient_data: {data}")
                logging.info(f"distribution_recipient_data: {data}")
                if not data.get('Distribution percentage'):
                    print(f"Distribution percentage {data.get('Payment recipient')} is required")
                    logging.info(f"Distribution percentage {data.get('Payment recipient')} is required")
                    continue

                recipient = MstPaymentRecipient.objects.filter(recipient_name=data.get('Payment recipient')).first()
                if not recipient:
                    print(f"Recipient {data.get('Payment recipient')} not found")
                    logging.info(f"Recipient {data.get('Payment recipient')} not found")
                    continue

                try:
                    percentage = float(data.get('Distribution percentage'))
                except ValueError as e:
                    print(f"Error converting distribution percentage to float: {e}")
                    logging.info(f"Error converting distribution percentage to float: {e}")
                    continue  # Skip this iteration and proceed with the next loop iteration

                dist_recipient, created = MstDistributionPatternDetail.objects.update_or_create(
                    recipient=recipient,
                    percentage=percentage,
                )

                # check if recipient exists in pattern_detail and pattern_code is the same but the percentage is different
                pattern_detail_data = dist_pattern.pattern_detail.filter(recipient=dist_recipient.recipient)
                if pattern_detail_data.exists() and not pattern_detail_data.filter(percentage=percentage).exists() and dist_pattern.pattern_code == data.get('Distribution pattern code'):
                    # dist_pattern.pattern_detail.clear()
                    print(f"remove {dist_recipient.recipient.recipient_name} from {dist_pattern.pattern_code}")
                    logging.info(f"remove {dist_recipient.recipient.recipient_name} from {dist_pattern.pattern_code}")
                    dist_pattern.pattern_detail.remove(dist_recipient)

                # check if recipient not exists in pattern_detail and pattern_code is the same
                if not dist_pattern.filter(pattern_detail=dist_recipient).exists() and dist_pattern.pattern_code == data.get('Distribution pattern code'):
                    # dist_pattern.pattern_detail.clear()
                    print(f"add {dist_recipient.recipient.recipient_name} to {dist_pattern.pattern_code}")
                    logging.info(f"add {dist_recipient.recipient.recipient_name} to {dist_pattern.pattern_code}")
                    dist_pattern.pattern_detail.add(dist_recipient)

            distribution_group_data = excel.parse(2).dropna(how='all').replace(np.nan, None, regex=True)
            # if utils.has_duplicate_elements(distribution_group_data.get('Distribution pattern group code')):
            #     print("Duplicate data found. Please check distribution_group sheet.")
            #     continue

            for data in distribution_group_data.to_dict(orient="records"):
                print(f"distribution_group_data: {data}")
                logging.info(f"distribution_group_data: {data}")
                sale_route = MstSaleRoute.objects.filter(route_name=data.get('Code')).first()
                if not sale_route:
                    print(f"Sale route {data.get('Code')} not found")
                    logging.info(f"Sale route {data.get('Code')} not found")
                    continue

                pattern_data = MstDistributionPattern.objects.filter(pattern_code=data.get('Distribution pattern')).first()
                if not pattern_data:
                    print(f"Pattern data {data.get('Distribution pattern')} not found")
                    logging.info(f"Pattern data {data.get('Distribution pattern')} not found")
                    continue

                dist_group, created = MstDistributionPatternGroup.objects.update_or_create(
                    group_code=data.get('Distribution pattern group code'),
                    defaults={
                        'group_name': data.get('Group name')
                    }
                )

                # if MstDistributionPatternGroupDetail.objects.filter(pattern_group=dist_group, sale_route=sale_route, pattern=dist_pattern).exists():
                #     continue
                MstDistributionPatternGroupDetail.objects.update_or_create(
                    pattern_group=dist_group,
                    sale_route=sale_route,
                    pattern=pattern_data,
                )

def export_distribution_data():
    distribution_company_data = MstDistributionPattern.objects.all()
    # convert (ID	Disribution pattern code	Title	1st recipient company	Distributor company	Distribution percentage	3rd recipient company	3rd recipient percentage) to dictionary
    distribution_company_data_obj = {
        'ID': [data.pattern_code for data in distribution_company_data],
        'Distribution pattern code': [],
        'Title': [],
        '1st recipient company': [],
        'Distributor company': [],
        'Distribution percentage': [],
        '3rd recipient company': [],
        '3rd recipient percentage': [],
    }

    distribution_recipient_data_obj = {
        'Distribution pattern code': [],
        'Payment recipient': [],
        'Distribution percentage': [],
    }

    for data in distribution_company_data:
        distribution_company_data_obj['Distribution pattern code'].append(data.pattern_code)
        distribution_company_data_obj['Title'].append(data.pattern_title)

        if data.accounting_company:
            distribution_company_data_obj['1st recipient company'].append(data.accounting_company.company_name)
        else:
            distribution_company_data_obj['1st recipient company'].append(None)

        if data.distributor_company:
            distribution_company_data_obj['Distributor company'].append(data.distributor_company.company_name)
        else:
            distribution_company_data_obj['Distributor company'].append(None)
        distribution_company_data_obj['Distribution percentage'].append(data.distributor_percentage)

        if data.third_recipient_company:
            distribution_company_data_obj['3rd recipient company'].append(data.third_recipient_company.company_name)
        else:
            distribution_company_data_obj['3rd recipient company'].append(None)
        distribution_company_data_obj['3rd recipient percentage'].append(data.third_recipient_percentage)

        for detail in data.pattern_detail.all():
            distribution_recipient_data_obj['Distribution pattern code'].append(data.pattern_code)
            distribution_recipient_data_obj['Payment recipient'].append(detail.recipient.recipient_name)
            distribution_recipient_data_obj['Distribution percentage'].append(detail.percentage)

    distribution_company_df = pd.DataFrame(distribution_company_data_obj)
    distribution_recipient_df = pd.DataFrame(distribution_recipient_data_obj)

    distribution_group_data = MstDistributionPatternGroupDetail.objects.all()
    distribution_group_data_obj = {
        'Distribution pattern group code': [],
        'Distribution pattern': [],
        'Code': [],
        'Group name': [],
    }

    for detail in distribution_group_data:
        distribution_group_data_obj['Distribution pattern group code'].append(detail.pattern_group.group_code)
        if detail.pattern:
            distribution_group_data_obj['Distribution pattern'].append(detail.pattern.pattern_code)
        else:
            distribution_group_data_obj['Distribution pattern'].append(None)

        if detail.sale_route:
            distribution_group_data_obj['Code'].append(detail.sale_route.route_name)
        else:
            distribution_group_data_obj['Code'].append(None)

        distribution_group_data_obj['Group name'].append(detail.pattern_group.group_name)

    distribution_group_df = pd.DataFrame(distribution_group_data_obj)

    buffer = BytesIO()
    with pd.ExcelWriter(buffer, engine='xlsxwriter', engine_kwargs={'options': {'strings_to_numbers': True}}) as writer:
        distribution_company_df.T.reset_index().T.to_excel(writer, sheet_name='分配パターン', header=False, index=False)
        distribution_recipient_df.T.reset_index().T.to_excel(writer, sheet_name='支払先', header=False, index=False)
        distribution_group_df.T.reset_index().T.to_excel(writer, sheet_name='分配パターングループ', header=False, index=False)

        writer.close()

    return buffer

def export_and_send_distribution_data(email):
    buffer = export_distribution_data()
    usecase.send_email_with_attachment(
            to=[email],
            subject=f"【分配計算システム | 分配パターン/支払先/分配グループ出力】",
            message=f"最新の分配パターンを含むExcelファイルが添付してあります。",
            attachment=ContentFile(buffer.getvalue(), f"dist_pattern_{datetime.now().strftime('%Y-%m-%d')}.xlsx"),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

def register_recipient(file_in):
    excel = pd.ExcelFile(file_in)
    recipient_data = excel.parse(0).dropna(how='all').replace(np.nan, None, regex=True)
    for data in recipient_data.to_dict(orient="records"):
        print(f"recipient_data: {data}")
        if data.get('registered_number'):
            if MstPaymentRecipient.objects.filter(registered_number=data.get('registered_number')).exists():
                print('Registered Number already exists.')
                continue
            if not bool(re.search('[a-zA-Z0-9]', data.get('registered_number'))):
                print('Registered Number must contain at least %(min_length)d digit or letter.') % {'min_length': 1}
                continue
        else:
            print("registered_number is required.")
            continue

        if data.get('manager_email'):
            manager = User.objects.filter(email=data.get('manager_email')).first()
            if not manager:
                print('Manager not found.')
                continue
        else:
            print("manager_email is required.")
            continue
        data, created = MstPaymentRecipient.objects.update_or_create(
            recipient_name=data.get('recipient_name'),
            billing_name=data.get('billing_name'),
            registered_number=data.get('registered_number')
        )
        data.manager.add(manager)

        print(f"registered recipient: {model_to_dict(instance=data, fields=[field.name for field in data._meta.fields])}")

def register_media_title(file_in):
    excel = pd.ExcelFile(file_in)
    media_title_data = excel.parse(0).dropna(how='all').replace(np.nan, None, regex=True)
    for data in media_title_data.to_dict(orient="records"):
        print(f"media_title_data: {data}")
        if data.get('Code'):
            if MediaTitle.objects.filter(code=data.get('Code')).exists():
                print('Media Code already exists.')
                continue
        else:
            print("Code is required.")
            break

        if data.get('title'):
            if MediaTitle.objects.filter(name=data.get('title')).exists():
                print('Media Title already exists.')
                continue
        else:
            print("Title is required.")
            break

        if data.get('starter royalty'):
            # if not bool(re.search('[0-9]', data.get('starter royalty'))):
            #     print('Starter Royalty must contain at least %(min_length)d digit.') % {'min_length': 1}
            #     continue
            if data.get('starter royalty') < 0:
                print('Starter Royalty must be greater than or equal to %(min_length)d.') % {'min_length': 0}
                continue

        if data.get('advanced royalty'):
            # if not bool(re.search('[0-9]', data.get('advanced royalty'))):
            #     print('Advanced Royalty must contain at least %(min_length)d digit.') % {'min_length': 1}
            #     continue
            if data.get('advanced royalty') < 0:
                print('Advanced Royalty must be greater than or equal to %(min_length)d.') % {'min_length': 0}
                continue

        if data.get('switch sale amount'):
            # if not bool(re.search('[0-9]', data.get('switch sale amount'))):
            #     print('Switch Sale Amount must contain at least %(min_length)d digit.') % {'min_length': 1}
            #     continue
            if data.get('switch sale amount') < 0:
                print('Switch Sale Amount must be greater than or equal to %(min_length)d.') % {'min_length': 0}
                continue

        media_title, created = MediaTitle.objects.update_or_create(
            name=data.get('title'),
            code=data.get('Code'),
            defaults= {
                'starter_royalty': data.get('starter royalty'),
                'advance_royalty': data.get('advanced royalty'),
                'switch_sale_amount': data.get('switch sale amount'),
            }
        )

        print(f"registered media title: {model_to_dict(instance=media_title, fields=[field.name for field in media_title._meta.fields])}")