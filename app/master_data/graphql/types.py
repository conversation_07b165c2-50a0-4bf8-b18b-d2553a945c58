import graphene
from graphene_django import DjangoConnectionField, DjangoObjectType
from graphql_relay import to_global_id

from app.master_data.models import (ArtistCategoryGroup, MediaTitle, MstArtist, MstCompany,
                                    MstDistributionPattern,
                                    MstDistributionPatternDetail,
                                    MstDistributionPatternGroup,
                                    MstDistributionPatternGroupDetail,
                                    MstSaleRoute, MstSegment, ProductCategoryGroup)
from app.project.models import Project


class SegmentNode(DjangoObjectType):
    pk = graphene.Int(source='pk')
    segment_label = graphene.String()

    class Meta:
        model = MstSegment
        fields = "__all__"
        filter_fields = {
            'segment_code': ['exact'],
            'segment_name': ['exact', 'icontains'],
        }
        interfaces = (graphene.relay.Node, )

    def resolve_segment_label(self, info):
        return f"{self.segment_name} ({self.segment_host} 人)"

class CompanyNode(DjangoObjectType):
    pk = graphene.Int(source='pk')

    class Meta:
        model = MstCompany
        fields = "__all__"
        filter_fields = {
            'company_code': ['exact'],
            'company_name': ['exact', 'icontains'],
        }
        interfaces = (graphene.relay.Node, )

class SaleRouteNode(DjangoObjectType):
    pk = graphene.Int(source='pk')

    class Meta:
        model = MstSaleRoute
        fields = "__all__"
        filter_fields = {
            'route_code': ['exact'],
            'route_name': ['exact', 'icontains'],
        }
        interfaces = (graphene.relay.Node, )

class DistributionPatternDetailNode(DjangoObjectType):
    pk = graphene.Int(source='pk')

    class Meta:
        model = MstDistributionPatternDetail
        fields = "__all__"
        filter_fields = {}
        interfaces = (graphene.relay.Node, )

class DistributionPatternNode(DjangoObjectType):
    pk = graphene.Int(source='pk')
    is_editable = graphene.Boolean()
    accounting_company = graphene.Field(CompanyNode)
    distributor_company = graphene.Field(CompanyNode)
    pattern_detail = DjangoConnectionField(DistributionPatternDetailNode)

    class Meta:
        model = MstDistributionPattern
        fields = "__all__"
        filter_fields = {
            'pattern_code': ['exact', 'icontains'],
            'pattern_title': ['exact', 'icontains'],
        }
        interfaces = (graphene.relay.Node, )

    def resolve_is_editable(self, info):
        return not Project.objects.filter(pattern__id=self.pk).exists()

class PatternGroupDataNode(graphene.ObjectType):
    route_id = graphene.String()
    route_pk = graphene.Int()
    route_name = graphene.String()
    pattern_id = graphene.String()
    pattern_pk = graphene.Int()
    pattern_title = graphene.String()

class DistributionPatternGroupNode(DjangoObjectType):
    pk = graphene.Int(source='pk')
    pattern_detail = DjangoConnectionField(DistributionPatternDetailNode)
    sale_route = DjangoConnectionField(SaleRouteNode)
    pattern_data = graphene.List(graphene.String)
    pattern_group_list = graphene.List(PatternGroupDataNode)

    class Meta:
        model = MstDistributionPatternGroup
        fields = "__all__"
        filter_fields = {
            'group_code': ['exact', 'icontains'],
            'group_name': ['exact', 'icontains'],
        }
        interfaces = (graphene.relay.Node, )

    def resolve_pattern_data(self, info):
        detail = MstDistributionPatternGroupDetail.objects.filter(pattern_group=self)
        detail_list = []
        for data in detail:
            detail_list.append(f"{data.sale_route.route_name if data.sale_route else '-'}({data.pattern.pattern_title if data.pattern else '-'})")
        return detail_list

    def resolve_pattern_group_list(self, info):
        detail = MstDistributionPatternGroupDetail.objects.filter(pattern_group=self)
        detail_list = []
        for data in detail:
            if not data.sale_route or not data.pattern:
                continue
            detail_list.append(PatternGroupDataNode(
                route_id=to_global_id('MstSaleRoute', data.sale_route.id),
                route_pk=data.sale_route.pk,
                route_name=data.sale_route.route_name,
                pattern_id=to_global_id('MstDistributionPattern', data.pattern.id),
                pattern_pk=data.pattern.pk,
                pattern_title=data.pattern.pattern_title
            ))
        return detail_list

class ArtistNode(DjangoObjectType):
	pk = graphene.Int(source='pk')

	class Meta:
		model = MstArtist
		fields = "__all__"
		filter_fields = {
			'artist_name': ['exact', 'icontains'],
		}
		interfaces = (graphene.relay.Node, )

class MediaTitleNode(DjangoObjectType):
    pk = graphene.Int(source='pk')

    class Meta:
        model = MediaTitle
        fields = "__all__"
        filter_fields = {
            'title_name': ['exact', 'icontains'],
        }
        interfaces = (graphene.relay.Node, )

class ProductCategoryGroupNode(DjangoObjectType):
    pk = graphene.Int(source='pk')

    class Meta:
        model = ProductCategoryGroup
        fields = "__all__"
        filter_fields = {
            'name': ['exact', 'icontains'],
        }
        interfaces = (graphene.relay.Node, )

class ArtistCategoryGroupNode(DjangoObjectType):
    pk = graphene.Int(source='pk')

    class Meta:
        model = ArtistCategoryGroup
        fields = "__all__"
        filter_fields = {
            'name': ['exact', 'icontains'],
        }
        interfaces = (graphene.relay.Node, )