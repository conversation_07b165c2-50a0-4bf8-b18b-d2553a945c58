import logging
from django.forms import model_to_dict
import graphene
from graphql import GraphQLError
from graphql_jwt.decorators import login_required
import numpy as np
import pandas as pd
from django_q.tasks import async_task
from django.db import transaction
from django.utils.translation import gettext as _

from app.common import utils
from app.goods.models import ProductDetail
from app.master_data import usecase
from app.master_data.forms import ArtistCategoryGroupForm, MediaTitleForm, MstArtistForm, MstCompanyForm, MstDistributionPatternForm, MstDistributionPatternGroupForm, MstPaymentRecipientForm, MstSaleRouteForm, MstSegmentForm, ProductCategoryGroupForm # type: ignore
from app.master_data.graphql.types import * # type: ignore
from app.master_data.models import Mst<PERSON>rtist, MstCompany, MstDistributionPattern, MstDistributionPatternDetail, MstDistributionPatternGroup, MstPaymentRecipient, MstSaleRoute, MstSegment
from app.user.schema import PaymentRecipientNode
from graphene_django.forms.mutation import DjangoModelFormMutation
from graphene import relay
from graphene_file_upload.scalars import Upload

logger = logging.getLogger(__name__)

class CreateSegment(DjangoModelFormMutation):
    segment = graphene.Field(SegmentNode)
    class Meta:
        form_class = MstSegmentForm
        return_field_name = 'segment'

    @classmethod
    @login_required
    def perform_mutate(cls, form, info):
        return super().perform_mutate(form, info)

class CreateCompany(DjangoModelFormMutation):
    company = graphene.Field(CompanyNode)
    class Meta:
        form_class = MstCompanyForm
        return_field_name = 'company'

    @classmethod
    @login_required
    def perform_mutate(cls, form, info):
        if not info.context.user.is_admin():
            raise GraphQLError("You are not allowed to access this data")
        return super().perform_mutate(form, info)

class CreatePaymentRecipient(DjangoModelFormMutation):
    recipient = graphene.Field(PaymentRecipientNode)
    class Meta:
        form_class = MstPaymentRecipientForm
        return_field_name = 'recipient'

    @classmethod
    @login_required
    def perform_mutate(cls, form, info):
        if not info.context.user.is_admin():
            raise GraphQLError("You are not allowed to access this data")
        return super().perform_mutate(form, info)

class CreateSaleRoute(DjangoModelFormMutation):
    route = graphene.Field(SaleRouteNode)

    class Meta:
        form_class = MstSaleRouteForm
        return_field_name = 'route'

    @classmethod
    @login_required
    def perform_mutate(cls, form, info):
        if not info.context.user.is_admin():
            raise GraphQLError("You are not allowed to access this data")
        return super().perform_mutate(form, info)

class DeleteSegment(relay.ClientIDMutation):
    Output = utils.Responser

    class Input:
        id = graphene.ID(required=True)

    @login_required
    def mutate_and_get_payload(root, info, id):
        if not info.context.user.is_admin():
            raise GraphQLError("You are not allowed to access this data")

        segment = MstSegment.objects.get(pk=id)
        if not segment:
            raise GraphQLError("Data not found")

        if segment.is_delete:
            raise GraphQLError("Data already deleted")
        segment.is_delete = True
        segment.save()
        return utils.Responser(result="Success")

class DeleteCompany(relay.ClientIDMutation):
    Output = utils.Responser

    class Input:
        id = graphene.ID(required=True)

    @login_required
    def mutate_and_get_payload(root, info, id):
        if not info.context.user.is_admin():
            raise GraphQLError("You are not allowed to access this data")

        company = MstCompany.objects.get(pk=id)
        if not company:
            raise GraphQLError("Data not found")

        if company.is_delete:
            raise GraphQLError("Data already deleted")
        company.is_delete = True
        company.save()
        return utils.Responser(result="Success")

class DeletePaymentRecipient(relay.ClientIDMutation):
    Output = utils.Responser

    class Input:
        id = graphene.ID(required=True)

    @login_required
    def mutate_and_get_payload(root, info, id):
        if not info.context.user.is_admin():
            raise GraphQLError("You are not allowed to access this data")

        recipient = MstPaymentRecipient.objects.get(pk=id)
        if not recipient:
            raise GraphQLError("Data not found")

        if recipient.is_delete:
            raise GraphQLError("Data already deleted")
        recipient.is_delete = True
        recipient.save()
        return utils.Responser(result="Success")

class DeleteSaleRoute(relay.ClientIDMutation):
    Output = utils.Responser

    class Input:
        id = graphene.ID(required=True)

    @login_required
    def mutate_and_get_payload(root, info, id):
        if not info.context.user.is_admin():
            raise GraphQLError("You are not allowed to access this data")

        route = MstSaleRoute.objects.get(pk=id)
        if not route:
            raise GraphQLError("Data not found")

        if route.is_delete:
            raise GraphQLError("Data already deleted")
        route.is_delete = True
        route.save()
        return utils.Responser(result="Success")

class UploadDistributionPatternDataMutation(graphene.Mutation):
    class Arguments:
        file_in = Upload(required=True)

    Output = utils.Responser

    @login_required
    def mutate(self, info, file_in):
        if not info.context.user.is_admin():
            raise GraphQLError("You are not allowed to access this data")

        print(file_in.name)
        excel = pd.ExcelFile(file_in)
        distribution_company_data = excel.parse(0).dropna(how='all').replace(np.nan, None, regex=True)
        with transaction.atomic():
            for row_number, data in enumerate(distribution_company_data.to_dict(orient="records")):
                logger.info(f"distribution_company_data: {data}")

                if not data.get('Distribution pattern code'):
                    raise GraphQLError(f"{row_number} Distribution pattern code に情報が記載されていません")

                if not data.get('Distribution percentage'):
                    raise GraphQLError(f"{row_number} Distribution percentage 「{data.get('Distributor company')}」 に情報が記載されていません")

                accounting_company = MstCompany.objects.filter(company_name=data.get('1st recipient company')).first()
                if not accounting_company:
                    raise GraphQLError(f"{row_number}「{data.get('1st recipient company')}」の企業情報は登録されていません")

                distributor_company = MstCompany.objects.filter(company_name=data.get('Distributor company')).first()
                if not distributor_company:
                    raise GraphQLError(f"{row_number}「{data.get('Distributor company')}」の企業情報は登録されていません")

                third_distributor_company = MstCompany.objects.filter(company_name=data.get('3rd recipient company')).first()
                if not third_distributor_company:
                    print(f"3rd recipient company {data.get('3rd recipient company')} not found")
                    logging.info(f"3rd recipient company {data.get('3rd recipient company')} not found")
                    continue

                dist_pattern, created = MstDistributionPattern.objects.update_or_create(
                    pattern_code=data.get('Distribution pattern code'),
                    defaults={
                        'pattern_title': data.get('Title'),
                        'accounting_company': accounting_company,
                        'distributor_company': distributor_company,
                        'distributor_percentage': data.get('Distribution percentage'),
                        'third_recipient_company': third_distributor_company,
                        'third_recipient_percentage': data.get('3rd recipient percentage'),
                    }
                )

                logger.info(f"registered dist pattern: {model_to_dict(instance=dist_pattern, fields=[field.name for field in dist_pattern._meta.fields])}")

        distribution_recipient_data = excel.parse(1).dropna(how='all').replace(np.nan, None, regex=True)
        dist_recipient_dict = {}
        for row_number, data in enumerate(distribution_recipient_data.to_dict(orient="records")):
            logger.info(f"distribution_recipient_data: {data}")
            if not data.get('Distribution percentage'):
                raise GraphQLError(f"{row_number} Distribution percentage「{data.get('Payment recipient')}」に情報が記載されていません")

            pattern_data = MstDistributionPattern.objects.filter(pattern_code=data.get('Distribution pattern code')).first()
            if not pattern_data:
                raise GraphQLError(f"{row_number} Pattern data「{data.get('Distribution pattern code')}」に情報が記載されていません")

            recipient = MstPaymentRecipient.objects.filter(recipient_name=data.get('Payment recipient')).first()
            if not recipient:
                raise GraphQLError(f"Recipient「{data.get('Payment recipient')}」の支払先は登録されていません")

            pattern_code_source = data.get('Distribution pattern code')
            if pattern_data.pattern_code not in dist_recipient_dict:
                dist_recipient_dict[pattern_code_source] = []

            dist_recipient_dict_value_list = dist_recipient_dict[pattern_code_source]
            recipient_data = {'recipient': recipient, 'percentage': data.get('Distribution percentage')}
            if len(dist_recipient_dict_value_list) > 0:
                dist_recipient_dict[pattern_code_source].append(recipient_data)
            else:
                dist_recipient_dict[pattern_code_source] = [recipient_data]

        with transaction.atomic():
            for pattern_code, recipient_list in dist_recipient_dict.items():
                pattern_data = MstDistributionPattern.objects.filter(pattern_code=pattern_code).first()
                pattern_data.pattern_detail.clear()

                for recipient_data in recipient_list:
                    percentage = recipient_data.get('percentage')
                    recipient = recipient_data.get('recipient')
                    if not recipient:
                        continue

                    dist_recipient, created = MstDistributionPatternDetail.objects.update_or_create(
                        recipient=recipient,
                        percentage=percentage,
                    )

                    pattern_detail_data = pattern_data.pattern_detail.filter(id=dist_recipient.pk)
                    is_exist_recipient = pattern_detail_data.exists()
                    logger.info(f"pattern_code: {pattern_code}, recipient: {recipient.recipient_name}, percentage: {percentage}, is_exist_recipient: {is_exist_recipient}")
                    if not is_exist_recipient:
                        pattern_data.pattern_detail.add(dist_recipient)
                        logger.info(f"registered dist recipient: {model_to_dict(instance=dist_recipient, fields=[field.name for field in dist_recipient._meta.fields])}")

        with transaction.atomic():
            distribution_group_data = excel.parse(2).dropna(how='all').replace(np.nan, None, regex=True)
            for data in distribution_group_data.to_dict(orient="records"):
                logger.info(f"distribution_group_data: {data}")

                pattern_data = MstDistributionPattern.objects.filter(pattern_code=data.get('Distribution pattern')).first()
                if not pattern_data:
                    raise GraphQLError(f"Pattern data「{data.get('Distribution pattern')}」に情報が記載されていません")

                sale_route = MstSaleRoute.objects.filter(route_name=data.get('Code')).first()
                if not sale_route:
                    raise GraphQLError("Sale route に情報が記載されていません")

                dist_group, created = MstDistributionPatternGroup.objects.update_or_create(
                    group_code=data.get('Distribution pattern group code'),
                    defaults={
                        'group_name': data.get('Group name'),
                    }
                )
                logger.info(f"registered dist pattern group: {model_to_dict(instance=dist_group, fields=[field.name for field in dist_group._meta.fields])}")

                dist_group_detail, created = MstDistributionPatternGroupDetail.objects.update_or_create(
                    pattern_group=dist_group,
                    sale_route=sale_route,
                    pattern=pattern_data,
                )

                logger.info(f"registered dist group detail: {model_to_dict(instance=dist_group_detail, fields=[field.name for field in dist_group_detail._meta.fields])}")

        return utils.Responser(result="Success")

class CreateDistributionPattern(DjangoModelFormMutation):
    dstribution_pattern = graphene.Field(DistributionPatternNode)

    class Meta:
        form_class = MstDistributionPatternForm
        return_field_name = 'dstribution_pattern'

    @classmethod
    @login_required
    def perform_mutate(cls, form, info):
        if not info.context.user.is_admin():
            raise GraphQLError("You are not allowed to access this data")
        return super().perform_mutate(form, info)

class DeleteDistributionPattern(relay.ClientIDMutation):
    Output = utils.Responser

    class Input:
        id = graphene.ID(required=True)

    @login_required
    def mutate_and_get_payload(root, info, id):
        if not info.context.user.is_admin():
            raise GraphQLError("You are not allowed to access this data")

        distribution_pattern = MstDistributionPattern.objects.filter(pk=id).first()
        if not distribution_pattern:
            raise GraphQLError("Data not found")

        if distribution_pattern.is_delete:
            raise GraphQLError("Data already deleted")
        distribution_pattern.is_delete = True

        distribution_pattern_group = distribution_pattern.distribution_pattern_group_detail.all()
        for group in distribution_pattern_group:
            group.is_delete = True
            group.save()

        distribution_pattern.save()

        return utils.Responser(result="Success")

class CreateDistributionPatternGroup(DjangoModelFormMutation):
    distribution_pattern_group = graphene.Field(DistributionPatternGroupNode)

    class Meta:
        form_class = MstDistributionPatternGroupForm
        return_field_name = 'dstribution_pattern_group'

    @classmethod
    @login_required
    def perform_mutate(cls, form, info):
        if not info.context.user.is_admin():
            raise GraphQLError("You are not allowed to access this data")
        return super().perform_mutate(form, info)

class DeleteDistributionPatternGroup(relay.ClientIDMutation):
    Output = utils.Responser

    class Input:
        id = graphene.ID(required=True)

    @login_required
    def mutate_and_get_payload(root, info, id):
        if not info.context.user.is_admin():
            raise GraphQLError("You are not allowed to access this data")

        distribution_pattern_group = MstDistributionPatternGroup.objects.filter(pk=id).first()
        if not distribution_pattern_group:
            raise GraphQLError("Data not found")

        if distribution_pattern_group.is_delete:
            raise GraphQLError("Data already deleted")
        distribution_pattern_group.is_delete = True
        distribution_pattern_group.save()

        return utils.Responser(result="Success")

class CreateArtist(DjangoModelFormMutation):
    artist = graphene.Field(ArtistNode)
    class Meta:
        form_class = MstArtistForm
        return_field_name = 'artist'

    @classmethod
    @login_required
    def perform_mutate(cls, form, info):
        if not info.context.user.is_admin():
            raise GraphQLError("You are not allowed to access this data")
        return super().perform_mutate(form, info)

class DeleteArtist(relay.ClientIDMutation):
    Output = utils.Responser

    class Input:
        id = graphene.ID(required=True)

    @login_required
    def mutate_and_get_payload(root, info, id):
        if not info.context.user.is_admin():
            raise GraphQLError("You are not allowed to access this data")

        artist = MstArtist.objects.get(pk=id)
        if not artist:
            raise GraphQLError("Data not found")

        if artist.is_delete:
            raise GraphQLError("Data already deleted")

        if artist.product_detail.all():
            raise GraphQLError("This artist has product detail")

        artist.is_delete = True
        artist.save()
        return utils.Responser(result="Success")

class CreateMediaTitle(DjangoModelFormMutation):
    media_title = graphene.Field(MediaTitleNode)
    class Meta:
        form_class = MediaTitleForm
        return_field_name = 'media_title'

    @classmethod
    @login_required
    def perform_mutate(cls, form, info):
        if not info.context.user.is_admin():
            raise GraphQLError("You are not allowed to access this data")
        return super().perform_mutate(form, info)

class DeleteMediaTitle(relay.ClientIDMutation):
    Output = utils.Responser

    class Input:
        id = graphene.ID(required=True)

    @login_required
    def mutate_and_get_payload(root, info, id):
        if not info.context.user.is_admin():
            raise GraphQLError("You are not allowed to access this data")

        media_title = MediaTitle.objects.filter(pk=id).first()
        if not media_title:
            raise GraphQLError("Data not found")

        if media_title.is_delete:
            raise GraphQLError("Data already deleted")
        media_title.is_delete = True
        media_title.save()
        return utils.Responser(result="Success")

class ExportDistPatternMutation(graphene.Mutation):
    Output = utils.Responser

    @login_required
    def mutate(self, info, **kwargs):
        user = info.context.user
        if not user.is_admin():
            raise GraphQLError("Permission denied")

        logger.debug(f"current logged user: {user.email}")

        async_task(usecase.export_and_send_distribution_data, user.email)
        return utils.Responser(result="登録されている分配パターン、支払先、分配グループのExcelファイルを生成後、管理者へメール送付します。")

class CreateProductCategoryGroup(DjangoModelFormMutation):
    product_category_group = graphene.Field(ProductCategoryGroupNode)

    class Meta:
        form_class = ProductCategoryGroupForm
        return_field_name = 'product_category_group'

    @classmethod
    @login_required
    def perform_mutate(cls, form, info):
        if not info.context.user.is_admin():
            raise GraphQLError("You are not allowed to access this data")
        return super().perform_mutate(form, info)

class DeleteProductCategoryGroup(relay.ClientIDMutation):
    Output = utils.Responser

    class Input:
        id = graphene.ID(required=True)

    @login_required
    def mutate_and_get_payload(root, info, id):
        if not info.context.user.is_admin():
            raise GraphQLError("You are not allowed to access this data")

        product_category_group = ProductCategoryGroup.objects.filter(pk=id).first()
        if not product_category_group:
            raise GraphQLError("Data not found")

        is_linked_product = ProductDetail.objects.filter(product_category_group=product_category_group).exists()
        if is_linked_product:
            raise GraphQLError(_("すでに商品と紐づいているため削除できません"))

        product_category_group.delete()

        return utils.Responser(result="Success")

class CreateArtistCategoryGroup(DjangoModelFormMutation):
    artist_category_group = graphene.Field(ArtistCategoryGroupNode)

    class Meta:
        form_class = ArtistCategoryGroupForm
        return_field_name = 'artist_category_group'

    @classmethod
    @login_required
    def perform_mutate(cls, form, info):
        if not info.context.user.is_admin():
            raise GraphQLError("You are not allowed to access this data")
        return super().perform_mutate(form, info)

class DeleteArtistCategoryGroup(relay.ClientIDMutation):
    Output = utils.Responser

    class Input:
        id = graphene.ID(required=True)

    @login_required
    def mutate_and_get_payload(root, info, id):
        if not info.context.user.is_admin():
            raise GraphQLError("You are not allowed to access this data")

        artist_category_group = ArtistCategoryGroup.objects.filter(pk=id).first()
        if not artist_category_group:
            raise GraphQLError("Data not found")

        is_linked_artist = MstArtist.objects.filter(artist_category_group=artist_category_group).exists()
        if is_linked_artist:
            raise GraphQLError(_("すでにアーティストと紐づいているため削除できません"))

        artist_category_group.delete()

        return utils.Responser(result="Success")

class UploadProductCategoryGroupMutation(graphene.Mutation):
    class Arguments:
        file_in = Upload(required=True)

    Output = utils.Responser

    @login_required
    def mutate(self, info, file_in):
        if not info.context.user.is_admin():
            raise GraphQLError("You are not allowed to access this data")

        excel = pd.ExcelFile(file_in)
        product_category_data = excel.parse(0).dropna(how='all').replace(np.nan, None, regex=True)
        with transaction.atomic():
            for row_number, data in enumerate(product_category_data.to_dict(orient="records")):
                logger.info(f"product_category_data: {data}")

                if not data.get('name'):
                    raise GraphQLError(f"{row_number} Product category name に情報が記載されていません")

                product_category, created = ProductCategoryGroup.objects.update_or_create(
                    name=data.get('name')
                )

                logger.info(f"registered product category: {model_to_dict(instance=product_category, fields=[field.name for field in product_category._meta.fields])}")

        return utils.Responser(result="Success")

class UploadArtistMutation(graphene.Mutation):
    class Arguments:
        file_in = Upload(required=True)

    Output = utils.Responser

    @login_required
    def mutate(self, info, file_in):
        if not info.context.user.is_admin():
            raise GraphQLError("You are not allowed to access this data")

        excel = pd.ExcelFile(file_in)
        artist_data = excel.parse(0).dropna(how='all').replace(np.nan, None, regex=True)
        with transaction.atomic():
            for row_number, data in enumerate(artist_data.to_dict(orient="records")):
                logger.info(f"artist_data: {data}")

                if not data.get('artist_name'):
                    raise GraphQLError(f"{row_number} アーティスト名 に情報が記載されていません")

                if not data.get('artist_category_group'):
                    raise GraphQLError(f"{row_number} アーティストカテゴリーグループ に情報が記載されていません")

                artist_category_group = ArtistCategoryGroup.objects.filter(name=data.get('artist_category_group')).first()
                if not artist_category_group:
                    raise GraphQLError(f"{row_number} アーティストカテゴリーグループ に情報が記載されていません")

                artist, created = MstArtist.objects.update_or_create(
                    artist_name=data.get('artist_name'),
                    defaults={
                        'artist_name_abbv': utils.abbreviate(data.get('artist_name')),
                        'artist_category_group': artist_category_group
                    }
                )

                logger.info(f"registered artist: {model_to_dict(instance=artist, fields=[field.name for field in artist._meta.fields])}")

        return utils.Responser(result="Success")

class UploadArtistCategoryGroupMutation(graphene.Mutation):
    class Arguments:
        file_in = Upload(required=True)

    Output = utils.Responser

    @login_required
    def mutate(self, info, file_in):
        if not info.context.user.is_admin():
            raise GraphQLError("You are not allowed to access this data")

        excel = pd.ExcelFile(file_in)
        artist_category_data = excel.parse(0).dropna(how='all').replace(np.nan, None, regex=True)
        with transaction.atomic():
            for row_number, data in enumerate(artist_category_data.to_dict(orient="records")):
                logger.info(f"artist_category_data: {data}")

                if not data.get('name'):
                    raise GraphQLError(f"{row_number} Artist category name に情報が記載されていません")

                artist_category, created = ArtistCategoryGroup.objects.update_or_create(
                    name=data.get('name')
                )

                logger.info(f"registered artist category: {model_to_dict(instance=artist_category, fields=[field.name for field in artist_category._meta.fields])}")

        return utils.Responser(result="Success")
