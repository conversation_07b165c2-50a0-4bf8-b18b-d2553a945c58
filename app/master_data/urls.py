# register payslip_statement from views into urls
from django.urls import path
from django.views.decorators.csrf import csrf_exempt
from app.master_data.views import register_distribution, register_recipient, register_media_title

urlpatterns = [
    path('master-data/register-dist-data/', csrf_exempt(register_distribution), name='register_distribution_data'),
    path('master-data/register-recipient/', csrf_exempt(register_recipient), name='register_recipient'),
    path('master-data/register-media-title/', csrf_exempt(register_media_title), name='register_media_title'),
]
