from django.db import models
from django.utils.translation import gettext as _

from app.common.utils import DeletedManager, TimeStampedModel
from app.user.models import User

# Create your models here.
class MstSegment(TimeStampedModel):
    segment_code = models.CharField(_("Code"), max_length=100, blank=True, null=True, unique=True)
    segment_name = models.CharField(_("Name"), max_length=100, blank=True, null=True)
    segment_host = models.IntegerField(_("Host"), blank=True, null=True)

    objects = DeletedManager()

    class Meta:
        verbose_name = "Data Segment"
        verbose_name_plural = "Data Segments"
        ordering = ['-created_at']

    def __str__(self):
        return self.segment_name

class MstCompany(TimeStampedModel):
    company_code = models.CharField(_("Code"), max_length=100, blank=True, null=True, unique=True)
    company_name = models.Char<PERSON>ield(_("Name"), max_length=100, blank=True, null=True)

    objects = DeletedManager()

    class Meta:
        verbose_name = "Data Company"
        verbose_name_plural = "Data Companies"
        ordering = ['-created_at']

    def __str__(self):
        return self.company_name

class MstPaymentRecipient(TimeStampedModel):
    manager = models.ManyToManyField(User, verbose_name=_('User'), related_name='payment_recipient', blank=True)
    recipient_name = models.CharField(_("Recipient Name"), max_length=255, null=True, blank=True)
    billing_name = models.CharField(_("Billing Name"), max_length=255, null=True, blank=True)
    registered_number = models.CharField(_("Registered Number"), max_length=255, null=True, blank=True)

    objects = DeletedManager()

    class Meta:
        verbose_name = _('Payment Recipient')
        verbose_name_plural = _('Payment Recipients')
        ordering = ['-created_at']
        # constraints = [
        #     models.UniqueConstraint(fields=['user', 'recipient_name'], name='unique_manager'),
        # ]

    def __str__(self):
        return self.recipient_name

class MstDistributionPatternDetail(TimeStampedModel):
    recipient = models.ForeignKey(MstPaymentRecipient, on_delete=models.DO_NOTHING, related_name='distribution_pattern_detail', blank=True, null=True)
    percentage = models.FloatField(_("Recipient Percentage"), blank=True, null=True)

    objects = DeletedManager()

    class Meta:
        verbose_name = "Data Distribution Pattern Detail"
        verbose_name_plural = "Data Distribution Pattern Details"
        ordering = ['-created_at']

    def __str__(self):
        return self.recipient.recipient_name

class MstDistributionPattern(TimeStampedModel):
    pattern_code = models.CharField(_("Code"), max_length=100, blank=True, null=True)
    pattern_title = models.CharField(_("Title"), max_length=100, blank=True, null=True)
    accounting_company = models.ForeignKey(MstCompany, on_delete=models.DO_NOTHING, related_name='acc_distribution_pattern', blank=True, null=True)
    accounting_percentage = models.FloatField(_("Accounting Percentage"), blank=True, null=True, default=100)
    distributor_company = models.ForeignKey(MstCompany, on_delete=models.DO_NOTHING, related_name='dist_distribution_pattern', blank=True, null=True)
    distributor_percentage = models.FloatField(_("Distribution Percentage"), blank=True, null=True)
    third_recipient_company = models.ForeignKey(MstCompany, on_delete=models.DO_NOTHING, related_name='third_distribution_pattern', blank=True, null=True)
    third_recipient_percentage = models.FloatField(_("3rd Recipient Percentage"), blank=True, null=True)
    pattern_detail = models.ManyToManyField(MstDistributionPatternDetail, related_name='distribution_pattern', blank=True)

    objects = DeletedManager()

    class Meta:
        verbose_name = "Data Distribution Pattern"
        verbose_name_plural = "Data Distribution Patterns"
        ordering = ['-created_at']

    def __str__(self):
        return self.pattern_title

class MstSaleRoute(TimeStampedModel):
    route_code = models.CharField(_("Code"), max_length=100, blank=True, null=True, unique=True)
    route_name = models.CharField(_("Name"), max_length=100, blank=True, null=True)

    objects = DeletedManager()

    class Meta:
        verbose_name = "Data Distribution Group Code"
        verbose_name_plural = "Data Distribution Group Codes"
        ordering = ['-created_at']

    def __str__(self):
        return self.route_name

class MstDistributionPatternGroup(TimeStampedModel):
    group_code = models.CharField(_("Code"), max_length=100, blank=True, null=True)
    group_name = models.CharField(_("Name"), max_length=100, blank=True, null=True)

    objects = DeletedManager()

    class Meta:
        verbose_name = "Data Distribution Pattern Group"
        verbose_name_plural = "Data Distribution Pattern Groups"
        ordering = ['-created_at']

    def __str__(self):
        return self.group_name

class MstDistributionPatternGroupDetail(TimeStampedModel):
    pattern_group = models.ForeignKey(MstDistributionPatternGroup, on_delete=models.DO_NOTHING, related_name='distribution_pattern_group_detail', blank=True, null=True)
    sale_route = models.ForeignKey(MstSaleRoute, on_delete=models.DO_NOTHING, related_name='distribution_pattern_group_detail', blank=True, null=True)
    pattern = models.ForeignKey(MstDistributionPattern, on_delete=models.DO_NOTHING, related_name='distribution_pattern_group_detail', blank=True, null=True)

    objects = DeletedManager()

    class Meta:
        verbose_name = "Data Distribution Pattern Group Detail"
        verbose_name_plural = "Data Distribution Pattern Group Details"
        ordering = ['-created_at']
        unique_together = ['pattern_group', 'sale_route', 'pattern']

    def __str__(self):
        return self.sale_route.route_name

class ArtistCategoryGroup(TimeStampedModel):
    name = models.CharField(_("Name"), max_length=255, unique=True)

    objects = DeletedManager()

    class Meta:
        verbose_name = _('Artist Category Group')
        verbose_name_plural = _('Artist Category Groups')
        ordering = ['-created_at']

class MstArtist(TimeStampedModel):
    artist_name = models.CharField(_("Name"), max_length=100, blank=True, null=True)
    artist_name_abbv = models.CharField(_("Name Abbv"), max_length=100, blank=True, null=True)
    artist_category_group = models.ForeignKey(ArtistCategoryGroup,
                                              on_delete=models.PROTECT, related_name='artist',
                                              blank=True, null=True, db_constraint=False,
                                              error_messages={
            'protected': _("すでにアーティストと紐づいているため削除できません")
        })

    objects = DeletedManager()

    class Meta:
        verbose_name = "Data Artist"
        verbose_name_plural = "Data Artists"
        ordering = ['-created_at']

    def __str__(self):
        return self.artist_name

class MediaTitle(TimeStampedModel):
    name = models.CharField(_("Name"), max_length=100, blank=True, null=True)
    code = models.CharField(_("Code"), max_length=100, blank=True, null=True, unique=True)
    starter_royalty = models.FloatField(_("Starter Royalty"), blank=True, null=True, default=0)
    advance_royalty = models.FloatField(_("Advance Royalty"), blank=True, null=True, default=0)
    switch_sale_amount = models.FloatField(_("Switch Sale Amount"), blank=True, null=True, default=0)

    objects = DeletedManager()

    class Meta:
        verbose_name = "Data Media Title"
        verbose_name_plural = "Data Media Titles"
        ordering = ['-created_at']

    def __str__(self):
        return self.name

class ProductCategoryGroup(TimeStampedModel):
    name = models.CharField(_("Name"), max_length=255, unique=True)

    objects = DeletedManager()

    class Meta:
        verbose_name = _('Product Category Group')
        verbose_name_plural = _('Product Category Groups')
        ordering = ['-created_at']

    def __str__(self):
        return self.name
