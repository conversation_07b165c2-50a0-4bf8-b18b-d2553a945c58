import re
from django.core.management import BaseCommand
from django.forms import model_to_dict
import numpy as np
import pandas as pd

from app.common import utils
from app.master_data import usecase
from app.master_data.models import MstCompany, MstDistributionPattern, MstDistributionPatternDetail, MstDistributionPatternGroup, MstDistributionPatternGroupDetail, MstPaymentRecipient, MstSaleRoute
from app.user.models import User

class Command(BaseCommand):
    help = 'Register Distribution Pattern'

    def add_arguments(self, parser):
        parser.add_argument("--filepath", action="append", type=str)

    def handle(self, *args, **options):
        file_in = ''
        if 'filepath' in options and options['filepath']:
            file_in = options['filepath'][0]
        usecase.register_distribution_data(file_in)

