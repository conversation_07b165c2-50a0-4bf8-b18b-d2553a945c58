# register payslip_statement from views into urls
from django.urls import path
from django.views.decorators.csrf import csrf_exempt
from app.project.views import export_all_project, export_distribution_price_list, export_sale_data, register_product, register_project
from app.project.views import payslip_statement

urlpatterns = [
    path('payslip_statement/', csrf_exempt(payslip_statement), name='payslip_statement'),
    path('export-sale-data/', csrf_exempt(export_sale_data), name='export_sale_data'),
    path('export-distribution-price/', csrf_exempt(export_distribution_price_list), name='export_distribution_price'),
    path('project/export-data/', csrf_exempt(export_all_project), name='export_project_data'),
    path('project/register-project/', csrf_exempt(register_project), name='register_project'),
    path('project/register-product/', csrf_exempt(register_product), name='register_product'),
]
