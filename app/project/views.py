import logging
import traceback
from django.conf import settings
from datetime import datetime
from io import BytesIO
from django.shortcuts import render
from django.http import HttpResponse, JsonResponse
from django.db.models import Q, Sum, FloatField
from django.db.models.functions import Cast
from django.views.decorators.http import require_http_methods
from django.db import models
from django.db.models import F, Subquery, OuterRef, IntegerField
import numpy as np
import pandas as pd
from django_q.tasks import async_task

from app.common import constants, utils
from app.project import usecase
from app.goods import usecase as usc_goods
from app.project.models import PaySlipReward, PaySlipStatement, PaySlipStatementReward, Project, RecipientRewardDistribution, RewardDistributorPortion, SaleGoodDetail

logger = logging.getLogger(__name__)

# Create your views here.
#create view to return pdf file of payslip statment
def payslip_statement(request):
    if not settings.DEBUG:
        access_token = request.GET.get('accessToken')
        if not utils.get_auth_user(access_token):
            return JsonResponse({'error': 'Unauthorized'}, status=401)

    id = request.GET.get('id')
    statement = PaySlipStatement.objects.get(pk=id)
    if not statement:
        return JsonResponse({'error': 'Statement not found'}, status=404)

    payslip_ids = statement.payslip_id_list.split(",")
    payslip_live = PaySlipReward.objects.filter(
        Q(pk__in=payslip_ids) & Q(reward_source_type=1) &
        (Q(project__segment__segment_name__icontains='ライブ') | Q(project__segment__segment_name__icontains='live'))
    )
    payslip_live_per_segment = {}
    for data in payslip_live:
        segment_data = {}
        if data.project.segment.pk not in payslip_live_per_segment:
            segment_data['segment'] = data.project.segment
            if 'ライブ' in data.project.segment.segment_name or 'live' in data.project.segment.segment_name.lower():
                segment_data['segment_type'] = 'LIVE活動'
            else:
                segment_data['segment_type'] = 'その他の活動'

            project_data = {}
            project_data['project'] = data.project
            project_data['reward'] = data

            segment_data['project_detail'] = [project_data]
            payslip_live_per_segment[data.project.segment.pk] = segment_data
            continue

        project_data = {}
        project_data['project'] = data.project
        project_data['reward'] = data
        payslip_live_per_segment[data.project.segment.pk]['project_detail'].append(project_data)

    total_amount_acquired = 0.0
    payslip_not_live = PaySlipReward.objects.filter(
        Q(pk__in=payslip_ids) & Q(reward_source_type=1) &
        ~Q(project__segment__segment_name__icontains='ライブ') & ~Q(project__segment__segment_name__icontains='live')
    )
    payslip_other_data = []
    for data in payslip_not_live:
        project_data = {}
        project_data['project'] = data.project
        project_data['reward'] = data
        payslip_other_data.append(project_data)
        total_amount_acquired += float(data.amount)

    payslip_sale_data = PaySlipReward.objects.filter(
        Q(pk__in=payslip_ids) & Q(reward_source_type=2)
    )
    payslip_sale_list = []
    for data in payslip_sale_data:
        project_data = {}
        project_data['sale_data'] = data.sale_good
        project_data['reward'] = data
        payslip_sale_list.append(project_data)

    payslip_carry_over_data = PaySlipReward.objects.filter(
        Q(pk__in=payslip_ids) & Q(reward_source_type=3)
    )
    payslip_carry_over_list = []
    for data in payslip_carry_over_data:
        project_data = {}
        project_data['reward'] = data
        payslip_carry_over_list.append(project_data)

    total_other_positive_income = PaySlipStatementReward.objects.filter(
        (Q(statement=statement) & Q(statement_type__in=[2,3]) & Q(reward_source_id__isnull=True)) &
        (~Q(item__icontains=constants.DEDUCTION_ITEMS) | ~Q(item__icontains=constants.CONSUMPTION_TAX))
    ).annotate(
        amount_as_integer=Cast('amount', IntegerField())
    ).filter(Q(amount_as_integer__gt=0)) \
    .aggregate(total_other_income=Sum('amount_as_integer'))['total_other_income']
    if not total_other_positive_income:
        total_other_positive_income = 0
    total_amount_acquired += total_other_positive_income

    total_other_negative_income = PaySlipStatementReward.objects.filter(
        (Q(statement=statement) & Q(statement_type__in=[2,3]) & Q(reward_source_id__isnull=True)) &
        (~Q(item__icontains=constants.DEDUCTION_ITEMS) | ~Q(item__icontains=constants.CONSUMPTION_TAX))
    ).annotate(
        amount_as_integer=Cast('amount', IntegerField())
    ).filter(Q(amount_as_integer__lt=0)) \
    .aggregate(total_other_income=Sum('amount_as_integer'))['total_other_income']
    if not total_other_negative_income:
        total_other_negative_income = 0
    total_amount_acquired += total_other_negative_income

    reward_and_consumption = PaySlipStatementReward.objects.filter(
        Q(statement=statement) & Q(statement_type='2') &
        (Q(item__icontains=constants.DEDUCTION_ITEMS) | Q(item__icontains=constants.DEDUCTION_ITEMS) | Q(item__icontains=constants.CONSUMPTION_TAX))
    )
    reward_and_consumption_dict = {obj.item: obj for obj in reward_and_consumption}
    reward_and_consumption_dict = utils.CaseInsensitiveDict(reward_and_consumption_dict)

    total_payment = statement.total_payment
    reward_tax = reward_and_consumption_dict.get(constants.DEDUCTION_ITEMS).amount if reward_and_consumption_dict.get(constants.DEDUCTION_ITEMS) else 0
    if reward_tax == 0:
        reward_tax = reward_and_consumption_dict.get(constants.DEDUCTION_ITEMS).amount if reward_and_consumption_dict.get(constants.DEDUCTION_ITEMS) else 0
    consumer_tax = reward_and_consumption_dict.get(constants.CONSUMPTION_TAX).amount if reward_and_consumption_dict.get(constants.CONSUMPTION_TAX) else 0

    total_reward = float(total_payment) + float(reward_tax) + float(consumer_tax)

    total_positive_amount_acquired = total_other_positive_income - utils.rounddown(consumer_tax)
    total_negative_amount_acquired = total_other_negative_income - utils.rounddown(reward_tax)

    total_payslip_amount = utils.rounddown(total_reward) + total_positive_amount_acquired + total_negative_amount_acquired

    response = utils.render_to_document('payslip/payslip_recipient_statement.html',
                                        {
                                            'payslip_live_per_segment': payslip_live_per_segment,
                                            'payslip_other_data': payslip_other_data,
                                            'payslip_sale_data': payslip_sale_list,
                                            'payslip_carry_over_data': payslip_carry_over_list,
                                            'billing_name': statement.recipient.billing_name,
                                            'registration_number': statement.recipient.registered_number,
                                            'billing_title': statement.title,
                                            'payslip_period': statement.period,
                                            'payslip_total_reward': total_payment,
                                            'total_amount_acquired': total_amount_acquired,
                                            'total_other_positive_income': total_other_positive_income,
                                            'total_other_negative_income': total_other_negative_income,
                                            'total_positive_amount_acquired': total_positive_amount_acquired,
                                            'total_negative_amount_acquired': total_negative_amount_acquired,
                                            'reward_tax': reward_tax,
                                            'consumption_tax': consumer_tax,
                                            'total_reward': total_reward,
                                            'total_payslip_amount': total_payslip_amount,
                                            'pay_method': statement.pay_method,
                                            'paid_date': statement.withdraw_date,
                                    }, plain_text=False)

    return response

def export_sale_data(request):
    access_token = request.GET.get('accessToken')
    user = utils.get_auth_user(access_token)
    if not user and not user.email:
        return JsonResponse({'error': 'Unauthorized'}, status=401)

    target_date = request.GET.get('targetDate')
    target_date = datetime.strptime(target_date, "%Y-%m-%d").date()
    sale_good = SaleGoodDetail.objects.filter(Q(target_month__year=target_date.year) & Q(target_month__month=target_date.month))
    if not sale_good:
        return JsonResponse({'error': 'Sale data not found'}, status=404)

    async_task(usc_goods.export_sale_data, target_date, user.email)

    return JsonResponse({'message': 'エクセルデータ生成後、管理者宛にExcelファイルを添付したメールを送付します'})

def export_all_project(request):
    if not settings.DEBUG:
        access_token = request.GET.get('accessToken')
        user = utils.get_auth_user(access_token)
        if not user and not user.is_admin():
            return JsonResponse({'error': 'Unauthorized'}, status=401)

    projects = Project.objects.exclude(project_status='5')
    data_dict_of_projects = {
        "PROJ ID": [],
        "PROJ NAME": [],
        "SEGMENT NAME": [],
        "ARTIST": [],
    }
    for project in projects:
        data_dict_of_projects["PROJ ID"].append(project.project_id)
        data_dict_of_projects["PROJ NAME"].append(project.project_name)
        if project.segment:
            data_dict_of_projects["SEGMENT NAME"].append(project.segment.segment_name)
        else:
            data_dict_of_projects["SEGMENT NAME"].append('')
        if project.artist:
            data_dict_of_projects["ARTIST"].append(project.artist.artist_name)
        else:
            data_dict_of_projects["ARTIST"].append('')

    df_from_dict = pd.DataFrame(data_dict_of_projects)

    buffer = BytesIO()
    # df_from_dict.to_excel('sale_good_export.xlsx', index=False)
    df_from_dict.to_excel(buffer, index=False, engine='openpyxl')
    buffer.seek(0)
    response = HttpResponse(buffer, content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = 'attachment; filename="project_export.xlsx"'

    return response

# Create view to receive post request file and register project usecase
def register_project(request):
    if request.method == 'POST':
        file_in = request.FILES.get('file')
        try:
            usecase.register_project(file_in)
        except Exception as e:
            logger.info(utils.append_error(e))
            return JsonResponse({'error': str(e), 'message': 'failed register project'}, status=400)
        return JsonResponse({'message': 'Success'}, status=200)
    return HttpResponse('Register Project')

# Create view to receive post request file and register product usecase
def register_product(request):
    if request.method == 'POST':
        file_in = request.FILES['file']
        excel = pd.ExcelFile(file_in)
        excel = excel.parse(0).dropna(how='all').replace(np.nan, None, regex=True)
        usc_goods.register_product(excel, None, file_in.name)
        return HttpResponse('Success')
    return HttpResponse('Register Product')

def export_distribution_price_list(request):
    if not settings.DEBUG:
        access_token = request.GET.get('accessToken')
        if not utils.get_auth_user(access_token):
            return JsonResponse({'error': 'Unauthorized'}, status=401)

    project_list = RewardDistributorPortion.objects.order_by().distinct('project').values('project')

    # reward_distributor_portions = RewardDistributorPortion.objects.filter(
    #     project__in=Subquery(project_list)
    # )

    # recipient_distribution = RecipientRewardDistribution.objects.filter(
    #     project__in=Subquery(project_list)
    # )

    distribution_price_data = {
        "プロジェクト": [],
        "ステータス": [],
        "粗利(税抜)": [],
        "窓口会社": [],
        "分配会社1": [],
        "分配会社2": [],
        "支払先": [],
    }
    for project in project_list:
        reward_portion_data = RewardDistributorPortion.objects.filter(
            project__pk=project.get('project')
        ).order_by('distributor_position')
        recipient_distribution_data = RecipientRewardDistribution.objects.filter(
            project__pk=project.get('project')
        )
        project_data = reward_portion_data.first().project

        if not project_data:
            continue

        distribution_price_data['プロジェクト'].append(project_data.project_name or '')
        distribution_price_data['ステータス'].append(project_data.project_status or '')
        distribution_price_data['粗利(税抜)'].append(project_data.profit_aft_tax or '')
        for distributor in reward_portion_data:
            if distributor.distributor_position == 1:
                distribution_price_data['窓口会社'].append(f"{distributor.company.company_name if distributor.company else ''}({distributor.reward})")
            elif distributor.distributor_position == 2:
                distribution_price_data['分配会社1'].append(f"{distributor.company.company_name if distributor.company else ''}({distributor.reward})")
            elif distributor.distributor_position == 3:
                distribution_price_data['分配会社2'].append(f"{distributor.company.company_name if distributor.company else ''}({distributor.reward})")

        recipient_data = []
        for recipient in recipient_distribution_data:
            recipient_data.append(f"{recipient.recipient.recipient_name}({recipient.reward})")

        distribution_price_data['支払先'].append('\n'.join(recipient_data))

    df_from_dict = pd.DataFrame(distribution_price_data)
    buffer = BytesIO()
    # df_from_dict.to_excel('sale_good_export.xlsx', index=False)
    # df_from_dict.to_excel(buffer, index=False, engine='openpyxl')
    with pd.ExcelWriter(buffer, engine="xlsxwriter") as writer:
        writer.book.formats[0].set_text_wrap()  # update global format with text wrap
        df_from_dict.T.reset_index().T.to_excel(writer, header=False, index=False)


    buffer.seek(0)
    response = HttpResponse(buffer, content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = 'attachment; filename="distribution_price.xlsx"'

    return response


