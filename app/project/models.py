from django.db import models
from django.utils.translation import gettext as _
from django.db.models import Q
from django.core.validators import MinValueValidator, MaxValueValidator

from app.common.utils import DeletedManager, TimeStampedModel
from app.goods.models import SaleGoodDetail
from app.master_data.models import MstArtist, MstCompany, MstDistributionPattern, MstPaymentRecipient, MstSegment
from app.user.models import User

# Create your models here.
class Project(TimeStampedModel):
    PROJECT_STATUS = (
        ('1', '作成中'),
        ('2', 'マネージャー確認'),
        ('3', 'マネージャー確認済'),
        ('4', '経理承認済'),
        ('5', '明細確定済'),
    )
    project_id = models.CharField(_("Project ID"), max_length=255, blank=True, null=True)
    project_name = models.CharField(_("Project Name"), max_length=255, blank=True, null=True)
    project_paid_date = models.DateField(_("Paid Date"), max_length=255, blank=True, null=True)
    temporary_sale = models.CharField(_("Temporary Sale"), max_length=255, blank=True, null=True)
    sale_price = models.CharField(_("Sale Price"), max_length=255, blank=True, null=True)
    sale_aft_tax = models.CharField(_("Sale After Tax"), max_length=255, blank=True, null=True)
    cost_price = models.CharField(_("Cost Price"), max_length=255, blank=True, null=True)
    cost_aft_tax = models.CharField(_("Cost After Tax"), max_length=255, blank=True, null=True)
    profit_aft_tax = models.CharField(_("Profit After Tax"), max_length=255, blank=True, null=True)
    pattern = models.ForeignKey(MstDistributionPattern, on_delete=models.DO_NOTHING, related_name='project_pattern', blank=True, null=True, db_constraint=False)
    overrided_pattern = models.ForeignKey(MstDistributionPattern, on_delete=models.DO_NOTHING, related_name='project_overrided_pattern', blank=True, null=True, db_constraint=False)
    segment = models.ForeignKey(MstSegment, on_delete=models.DO_NOTHING, related_name='project', blank=True, null=True)
    manager = models.ForeignKey(User, on_delete=models.DO_NOTHING, related_name='project', blank=True, null=True)
    project_status = models.CharField(_("Project Status"), choices=PROJECT_STATUS, blank=True, null=True, default='1')
    artist = models.ForeignKey(MstArtist, on_delete=models.DO_NOTHING, related_name='project', blank=True, null=True)
    issue_date = models.DateField(_("Issue Date"), blank=True, null=True)
    need_recalculate_reward = models.BooleanField(_("Need Recalculate Reward"), default=False)

    objects = DeletedManager()

    class Meta:
        verbose_name = _('Project')
        verbose_name_plural = _('Projects')
        ordering = ['-created_at']
        constraints = [
            models.UniqueConstraint(
                fields=['project_id'],
                condition=Q(is_delete=False),
                name='unique_project_id_if_not_deleted'
            ),
        ]

    def __str__(self):
        return self.project_name

class ProjectIncome(TimeStampedModel):
    PAID_STATUS = (
        ('1', 'Registered'),
        ('2', 'Paid'),
    )
    project = models.ForeignKey(Project, on_delete=models.DO_NOTHING, related_name='project_income', blank=True, null=True)
    income_code = models.CharField(_("Income Code"), max_length=255, blank=True, null=True, unique=True)
    issue_date = models.DateField(_("Issue Date"), blank=True, null=True)
    paid_date = models.DateField(_("Paid Date"), blank=True, null=True)
    paid_status = models.CharField(_("Paid Status"), choices=PAID_STATUS, max_length=10, default='1', blank=True, null=True)
    billing_title = models.CharField(_("Billing Title"), max_length=255, blank=True, null=True)
    billing_to = models.ForeignKey(MstCompany, on_delete=models.DO_NOTHING, related_name='project_income', blank=True, null=True)
    price = models.CharField(_("Price"), max_length=255, blank=True, null=True)
    note = models.CharField(_("Note"), max_length=255, blank=True, null=True)

    objects = DeletedManager()

    class Meta:
        verbose_name = _('Project Income')
        verbose_name_plural = _('Project Incomes')
        ordering = ['-created_at']

    def __str__(self):
        return self.project.project_name

class ProjectCost(TimeStampedModel):
    TAX_CATEGORY = (
        (10, '10%'),
        (8, '8%'),
    )
    project = models.ForeignKey(Project, on_delete=models.DO_NOTHING, related_name='project_cost', blank=True, null=True)
    cost_code = models.CharField(_("Cost Code"), max_length=255, blank=True, null=True, unique=True)
    issue_date = models.DateField(_("Issue Date"), blank=True, null=True)
    receipt_title = models.CharField(_("Billing Title"), max_length=255, blank=True, null=True)
    paid_to = models.CharField(_("Paid To"), max_length=255, blank=True, null=True)
    price = models.CharField(_("Price"), max_length=255, blank=True, null=True)
    note = models.CharField(_("Price"), max_length=255, blank=True, null=True)
    tax_category = models.IntegerField(_("Tax Category"), choices=TAX_CATEGORY, default=10, blank=True, null=True)

    objects = DeletedManager()

    class Meta:
        verbose_name = _('Project Cost')
        verbose_name_plural = _('Project Costs')
        ordering = ['-created_at']

    def __str__(self):
        return self.project.project_name


class ProjectLogManager(DeletedManager):
    def save_log(self, project, updated_by, log_description):
        try:
            return self.create(project=project, updated_by=updated_by, log_description=log_description)
        except Exception as e:
            print(e)
            return None

class ProjectLog(TimeStampedModel):
    project = models.ForeignKey(Project, on_delete=models.DO_NOTHING, related_name='project_log', blank=True, null=True)
    updated_by = models.ForeignKey(User, on_delete=models.DO_NOTHING, related_name='project_log', blank=True, null=True)
    log_description = models.TextField(_("Log Description"), blank=True, null=True)

    objects = ProjectLogManager()

    class Meta:
        verbose_name = _('Project Log')
        verbose_name_plural = _('Project Logs')
        ordering = ['-created_at']

    def __str__(self):
        return self.project.project_name

class PaySlipReward(TimeStampedModel):
    REWARD_TYPE = (
        (1, 'Project'),
        (2, 'Sale Data'),
        (3, 'Carry Over'),
    )
    reward_source_type = models.IntegerField(_("Reward Source Type"), choices=REWARD_TYPE, default=1, blank=True, null=True)
    reward_source_id = models.IntegerField(_("Reward Source ID"), blank=True, null=True)
    project = models.ForeignKey(Project, on_delete=models.DO_NOTHING, related_name='payment_recipient_detail', blank=True, null=True)
    sale_good = models.ForeignKey(SaleGoodDetail, on_delete=models.DO_NOTHING, related_name='payment_recipient_detail', blank=True, null=True, db_constraint=False)
    recipient = models.ForeignKey(MstPaymentRecipient, on_delete=models.DO_NOTHING, related_name='payment_recipient_detail', blank=True, null=True)
    pay_date = models.DateField(_("Pay Date"), blank=True, null=True)
    billing_title = models.CharField(_("Billing Title"), max_length=255, blank=True, null=True)
    amount = models.CharField(_("Amount"), max_length=255, blank=True, null=True)
    is_paid = models.BooleanField(_("Is Already Create Pay Slip"), default=False)
    note = models.TextField(_("Note"), blank=True, null=True)

    objects = DeletedManager()

    class Meta:
        verbose_name = _('Payment Reward')
        verbose_name_plural = _('Payment Rewards')
        ordering = ['-created_at']

    def __str__(self):
        return self.recipient.recipient_name

class PaySlipStatement(TimeStampedModel):
    recipient = models.ForeignKey(MstPaymentRecipient, on_delete=models.DO_NOTHING, related_name='payment_statement', blank=True, null=True)
    title = models.CharField(_("Title"), max_length=255, blank=True, null=True)
    period = models.CharField(_("Period"), max_length=255, blank=True, null=True)
    withdraw_date = models.DateField(_("Withdraw Date"), blank=True, null=True)
    pay_method = models.CharField(_("Pay Method"), max_length=255, blank=True, null=True)
    total_payment = models.CharField(_("Total Payment"), max_length=255, blank=True, null=True)
    payslip_id_list = models.CharField(_("Pay Slip ID List"), max_length=255, blank=True, null=True)

    objects = DeletedManager()

    class Meta:
        verbose_name = _('Payment Statement')
        verbose_name_plural = _('Payment Statements')
        ordering = ['-created_at']

    def __str__(self):
        return self.title

class PaySlipStatementReward(TimeStampedModel):
    STATEMENT_TYPE = (
        ('1', 'Reward'),
        ('2', 'Other'),
    )
    REWARD_TYPE = (
        (1, 'Project'),
        (2, 'Sale Data'),
        (3, 'Carry Over'),
    )
    statement = models.ForeignKey(PaySlipStatement, on_delete=models.DO_NOTHING, related_name='payment_statement_reward', blank=True, null=True)
    item = models.CharField(_("Item"), max_length=255, blank=True, null=True)
    amount = models.CharField(_("Amount"), max_length=255, blank=True, null=True)
    statement_type = models.CharField(_("Statement Type"), choices=STATEMENT_TYPE, max_length=10, default='1', blank=True, null=True)
    reward_type = models.CharField(_("Reward Type"), choices=REWARD_TYPE, max_length=10, default='1', blank=True, null=True)
    reward_source_id = models.IntegerField(_("Reward Source ID"), blank=True, null=True)

    objects = DeletedManager()

    class Meta:
        verbose_name = _('Payment Statement Reward')
        verbose_name_plural = _('Payment Statement Rewards')
        ordering = ['-created_at']

    def __str__(self):
        return self.statement.title

class RewardDistributorPortion(TimeStampedModel):
    project = models.ForeignKey(Project, on_delete=models.DO_NOTHING, related_name='reward_distributor_portion', blank=True, null=True, db_constraint=False)
    sale_data = models.ForeignKey(SaleGoodDetail, on_delete=models.DO_NOTHING, related_name='reward_distributor_portion', blank=True, null=True, db_constraint=False)
    company = models.ForeignKey(MstCompany, on_delete=models.DO_NOTHING, related_name='reward_distributor_portion', blank=True, null=True, db_constraint=False)
    distributor_position = models.IntegerField(_("Distributor Position"), blank=True, null=True)
    income = models.CharField(_("Income Amount"), max_length=255, blank=True, null=True)
    expense = models.CharField(_("Expense Amount"), max_length=255, blank=True, null=True)
    reward = models.CharField(_("Reward Amount"), max_length=255, blank=True, null=True)

    objects = ProjectLogManager()

    class Meta:
        verbose_name = _('Reward Distributor Portion')
        verbose_name_plural = _('Reward Distributor Portions')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['is_delete', 'project']),
            models.Index(fields=['is_delete', 'project', 'sale_data']),
        ]

    def __str__(self):
        return self.company.company_name

class RecipientRewardDistribution(TimeStampedModel):
    recipient = models.ForeignKey(MstPaymentRecipient, on_delete=models.DO_NOTHING, related_name='recipient_reward_distribution', blank=True, null=True, db_constraint=False)
    project = models.ForeignKey(Project, on_delete=models.DO_NOTHING, related_name='recipient_reward_distribution', blank=True, null=True, db_constraint=False)
    sale_data = models.ForeignKey(SaleGoodDetail, on_delete=models.DO_NOTHING, related_name='recipient_reward_distribution', blank=True, null=True, db_constraint=False)
    reward = models.CharField(_("Reward Amount"), max_length=255, blank=True, null=True)

    objects = ProjectLogManager()

    class Meta:
        verbose_name = _('Recipient Reward Distribution')
        verbose_name_plural = _('Recipient Reward Distributions')
        ordering = ['-created_at']

    def __str__(self):
        return self.recipient.recipient_name

class DistributionCompany(TimeStampedModel):
    project = models.ForeignKey(Project, on_delete=models.DO_NOTHING, related_name='distribution_company', blank=True, null=True, db_constraint=False)
    pattern_code = models.CharField(_("Code"), max_length=100, blank=True, null=True)
    accounting_company = models.ForeignKey(MstCompany, on_delete=models.DO_NOTHING, related_name='acc_distribution_company', blank=True, null=True)
    accounting_percentage = models.FloatField(_("Accounting Percentage"), blank=True, null=True, default=100, validators=[MinValueValidator(0), MaxValueValidator(100)])
    distributor_company = models.ForeignKey(MstCompany, on_delete=models.DO_NOTHING, related_name='dist_distribution_company', blank=True, null=True)
    distributor_percentage = models.FloatField(_("Distribution Percentage"), blank=True, null=True, default=0, validators=[MinValueValidator(0), MaxValueValidator(100)])
    third_recipient_company = models.ForeignKey(MstCompany, on_delete=models.DO_NOTHING, related_name='third_distribution_company', blank=True, null=True)
    third_recipient_percentage = models.FloatField(_("3rd Recipient Percentage"), blank=True, null=True, default=0, validators=[MinValueValidator(0), MaxValueValidator(100)])

    objects = ProjectLogManager()

    class Meta:
        verbose_name = _('Distribution Company')
        verbose_name_plural = _('Distribution Companies')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['is_delete', 'project']),
        ]

    def __str__(self):
        return self.pattern_code

class DistributionRecipient(TimeStampedModel):
    project = models.ForeignKey(Project, on_delete=models.DO_NOTHING, related_name='distribution_recipient', blank=True, null=True, db_constraint=False)
    recipient = models.ForeignKey(MstPaymentRecipient, on_delete=models.DO_NOTHING, related_name='distribution_recipient', blank=True, null=True, db_constraint=False)
    percentage = models.FloatField(_("Recipient Percentage"), blank=True, null=True, default=0)

    objects = ProjectLogManager()

    class Meta:
        verbose_name = _('Distribution Recipient')
        verbose_name_plural = _('Distribution Recipients')
        ordering = ['-created_at']

    def __str__(self):
        return self.recipient.recipient_name