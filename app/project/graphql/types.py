import json
import graphene
from graphene_django import DjangoConnectionField, DjangoObjectType
from graphene_django.filter import DjangoFilterConnectionField

from app.goods.graphql.types import SaleGoodDetailNode
from app.project.models import DistributionCompany, DistributionRecipient, PaySlipReward, PaySlipStatement, Project, ProjectCost, ProjectIncome, ProjectLog, RecipientRewardDistribution, RewardDistributorPortion
from django.db.models import Q

class DistributionPriceNode(DjangoObjectType):
    pk = graphene.Int(source='pk')

    class Meta:
        model = RewardDistributorPortion
        fields = '__all__'
        filter_fields = {
            'project__project_id': ['exact'],
        }
        interfaces = (graphene.relay.Node, )

class RecipientPriceNode(DjangoObjectType):
    pk = graphene.Int(source='pk')

    class Meta:
        model = RecipientRewardDistribution
        fields = '__all__'
        filter_fields = {
            'project__project_id': ['exact'],
        }
        interfaces = (graphene.relay.Node, )

class ProjectNode(DjangoObjectType):
    pk = graphene.Int(source='pk')
    manager = graphene.Field('app.user.schema.UserNode')
    status_data = graphene.String()
    distribution_mapping = graphene.Field('app.project.schema.DistributionCompanyNode')
    distribution_price = DjangoConnectionField(DistributionPriceNode)
    recipient_mapping = DjangoConnectionField('app.project.schema.DistributionRecipientNode')
    recipient_price = DjangoConnectionField(RecipientPriceNode)

    class Meta:
        model = Project
        fields = '__all__'
        filter_fields = {
            'project_id': ['exact'],
            'project_name': ['icontains'],
            'project_status': ['exact'],
            'artist__id': ['exact'],
            'artist__artist_name': ['icontains'],
            'segment__id': ['exact'],
            'segment__segment_name': ['icontains'],
        }
        interfaces = (graphene.relay.Node, )

    def resolve_manager(self, info):
        return self.manager

    def resolve_status_data(self, info):
        status = self.project_status
        status_dict = {key: value for key, value in Project.PROJECT_STATUS}
        status_name = status_dict.get(status)
        return json.dumps({status: status_name}, ensure_ascii=False)

    def resolve_distribution_price(self, info):
        return RewardDistributorPortion.objects.filter(project=self).order_by('distributor_position')

    def resolve_recipient_price(self, info):
        return RecipientRewardDistribution.objects.filter(project=self)

    def resolve_distribution_mapping(self, info):
        return DistributionCompany.objects.filter(project=self).first()

    def resolve_recipient_mapping(self, info):
        return DistributionRecipient.objects.filter(project=self)

    # def resolve_distribution_price(self, info):
    #     distributor_portion = RewardDistributorPortion.objects.filter(project=self)
    #     distributor_portion_dict = {}
    #     for portion in distributor_portion.order_by('distributor_position'):
    #         portion_data = {
    #             'distributor': portion.company.company_name,
    #             'portion': portion.reward
    #         }
    #         distributor_portion_dict |= portion_data
    #     return json.dumps(distributor_portion_dict, ensure_ascii=False)

    # def resolve_recipient_price(self, info):
    #     recipient_portion = RecipientRewardDistribution.objects.filter(project=self)
    #     recipient_portion_dict = {}
    #     for portion in recipient_portion:
    #         portion_data = {
    #             'recipient': portion.recipient.recipient_name,
    #             'portion': portion.reward
    #         }
    #         recipient_portion_dict |= portion_data
    #     return json.dumps(recipient_portion_dict, ensure_ascii=False)

class ProjectIncomeNode(DjangoObjectType):
    pk = graphene.Int(source='pk')
    paid_status = graphene.String()

    class Meta:
        model = ProjectIncome
        fields = '__all__'
        filter_fields = {
            'income_code': ['exact'],
            'billing_title': ['exact', 'icontains'],
        }
        interfaces = (graphene.relay.Node, )

    def resolve_paid_status(self, info):
        status = self.paid_status
        if status == '1':
            return "Registered"
        return "Paid"

class ProjectCostNode(DjangoObjectType):
    pk = graphene.Int(source='pk')
    tax_category_id = graphene.String()
    tax_category_name = graphene.String()

    class Meta:
        model = ProjectCost
        fields = '__all__'
        filter_fields = {
            'cost_code': ['exact'],
            'receipt_title': ['exact', 'icontains'],
        }
        interfaces = (graphene.relay.Node, )

    def resolve_tax_category_id(self, info):
        tax_id = self.tax_category_id
        tax_dict = {key: key for key, value in ProjectCost.TAX_CATEGORY}
        return tax_dict.get(tax_id)

    def resolve_tax_category_name(self, info):
        tax_id = self.tax_category_id
        tax_dict = {key: value for key, value in ProjectCost.TAX_CATEGORY}
        return tax_dict.get(tax_id)

class ProjectLogNode(DjangoObjectType):
    pk = graphene.Int(source='pk')

    class Meta:
        model = ProjectLog
        fields = '__all__'
        filter_fields = {
            'project__project_name': ['exact', 'icontains'],
        }
        interfaces = (graphene.relay.Node, )

class PaySlipRewardNode(DjangoObjectType):
    pk = graphene.Int(source='pk')

    class Meta:
        model = PaySlipReward
        fields = '__all__'
        filter_fields = {
            'billing_title': ['exact', 'icontains'],
        }
        interfaces = (graphene.relay.Node, )

class PaySlipStatementNode(DjangoObjectType):
    pk = graphene.Int(source='pk')

    class Meta:
        model = PaySlipStatement
        fields = '__all__'
        filter_fields = {
            'title': ['exact', 'icontains'],
            'recipient__recipient_name': ['exact', 'icontains'],
        }
        interfaces = (graphene.relay.Node, )

class TaxListNode(graphene.ObjectType):
    code = graphene.String()
    name = graphene.String()

class RecipientRewardDistributionNode(DjangoObjectType):
    pk = graphene.Int(source='pk')

    class Meta:
        model = RecipientRewardDistribution
        fields = '__all__'
        filter_fields = {
            'project__project_id': ['exact'],
            'project__project_name': ['exact', 'icontains'],
            'recipient__id': ['exact'],
            'recipient__recipient_name': ['exact', 'icontains'],
        }
        interfaces = (graphene.relay.Node, )

class RewardDistributorPortionNode(DjangoObjectType):
    pk = graphene.Int(source='pk')
    first_distributor = graphene.Field(DistributionPriceNode)
    second_distributor = graphene.Field(DistributionPriceNode)
    third_distributor = graphene.Field(DistributionPriceNode)
    recipient_price = DjangoFilterConnectionField(RecipientRewardDistributionNode)
    sale_data_detail = graphene.Field(SaleGoodDetailNode)

    class Meta:
        model = RewardDistributorPortion
        fields = '__all__'
        filter_fields = {
            'project__project_name': ['exact', 'icontains'],
            'sale_data__product_detail__product_name': ['exact', 'icontains'],
            # 'sale_data__target_month': ['exact', 'year', 'month'],
        }
        interfaces = (graphene.relay.Node, )

    def resolve_first_distributor(self, info):
        reward_data = RewardDistributorPortion.objects.filter(Q(distributor_position=1))
        if self.project:
            reward_data = reward_data.filter(Q(project=self.project))
        if self.sale_data:
            reward_data = reward_data.filter(Q(sale_data=self.sale_data))
        return reward_data.first()

    def resolve_second_distributor(self, info):
        reward_data = RewardDistributorPortion.objects.filter(Q(distributor_position=2))
        if self.project:
            reward_data = reward_data.filter(Q(project=self.project))
        if self.sale_data:
            reward_data = reward_data.filter(Q(sale_data=self.sale_data))
        return reward_data.first()

    def resolve_third_distributor(self, info):
        reward_data = RewardDistributorPortion.objects.filter(Q(distributor_position=3))
        if self.project:
            reward_data = reward_data.filter(Q(project=self.project))
        if self.sale_data:
            reward_data = reward_data.filter(Q(sale_data=self.sale_data))
        return reward_data.first()

    def resolve_recipient_price(self, info, **kwargs):
        print(f"kwargs: {kwargs}")
        recipient_reward = RecipientRewardDistribution.objects.filter(Q(project=self.project))

        if self.sale_data:
            recipient_reward = RecipientRewardDistribution.objects.filter(Q(sale_data=self.sale_data))

        if kwargs.get('recipient__id'):
            recipient_id = kwargs.get('recipient__id')
            recipient_reward = recipient_reward.filter(Q(recipient__pk=recipient_id))

        return recipient_reward

    def resolve_sale_data_detail(self, info):
        return self.sale_data

class RewardDistributorPortionV2Node(graphene.ObjectType):
    distribution_price = DjangoFilterConnectionField(DistributionPriceNode)
    recipient_price = DjangoFilterConnectionField(RecipientRewardDistributionNode)

class DistributionCompanyNode(DjangoObjectType):
    pk = graphene.Int(source='pk')

    class Meta:
        model = DistributionCompany
        fields = '__all__'
        filter_fields = {
            'project__id': ['exact'],
            'project__project_name': ['exact', 'icontains'],
        }
        interfaces = (graphene.relay.Node, )

class DistributionRecipientInput(graphene.InputObjectType):
    project_id = graphene.Int(required=True)
    recipient_id = graphene.Int(required=True)
    percentage = graphene.Float(required=True)

class DistributionRecipientNode(DjangoObjectType):
    pk = graphene.Int(source='pk')

    class Meta:
        model = DistributionRecipient
        fields = '__all__'
        filter_fields = {
            'project__id': ['exact'],
            'project__project_name': ['exact', 'icontains'],
        }
        interfaces = (graphene.relay.Node, )

