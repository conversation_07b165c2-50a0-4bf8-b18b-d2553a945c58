from datetime import datetime
import logging
from django.db import IntegrityError, transaction
from django.forms import model_to_dict
import graphene
from graphql import GraphQLError
import numpy as np
import pandas as pd
from django.utils.translation import gettext as _

from app.common import utils
from app.common.graphql.mutations import BulkDjangoModelFormMutation
from app.master_data.models import MstCompany, MstDistributionPattern
from app.project import usecase
from app.project.forms import DistributionCompanyMappingForm, DistributionRecipientMappingForm, PaySlipRewardForm, PaySlipStatementForm, ProjectCostForm, ProjectForm, ProjectIncomeForm
from app.project.graphql.types import *
from app.project.models import PaySlipReward, PaySlipStatement, Project, ProjectCost, ProjectIncome, ProjectLog, RecipientRewardDistribution, RewardDistributorPortion
from graphql_jwt.decorators import login_required
from graphene_django.forms.mutation import DjangoModelFormMutation
from graphene import relay
from graphene_file_upload.scalars import Upload
from django.db.models import Sum, IntegerField

logger = logging.getLogger(__name__)

class CreateProject(DjangoModelFormMutation):
    project = graphene.Field(ProjectNode)

    class Meta:
        form_class = ProjectForm
        return_field_name = 'project'

    @classmethod
    def get_form_kwargs(cls, root, info, **input):
        kwargs = super().get_form_kwargs(root, info, **input)
        data = kwargs.get('data')
        data['logged_user'] = info.context.user
        kwargs['data'] = data
        return kwargs

    @classmethod
    @login_required
    def perform_mutate(cls, form, info):
        return super().perform_mutate(form, info)

class CloseProject(relay.ClientIDMutation):
    Output = utils.Responser

    class Input:
        id = graphene.ID(required=True)
        dist_pattern_id = graphene.ID()

    @login_required
    def mutate_and_get_payload(root, info, **kwargs):
        user = info.context.user
        if not user.is_admin():
            raise GraphQLError("You are not allowed to close project")

        project = Project.objects.get(pk=kwargs.get('id'))
        if not project:
            raise GraphQLError("Data not found")

        if project.is_delete:
            raise GraphQLError("Data already deleted")

        if '5' == project.project_status:
            raise GraphQLError("Project already closed")

        if not project.profit_aft_tax:
            raise GraphQLError("Profit after tax is required")

        overrided_pattern = None
        if kwargs.get('dist_pattern_id'):
            overrided_pattern = MstDistributionPattern.objects.filter(pk=kwargs.get('dist_pattern_id')).first()
            if not overrided_pattern:
                raise GraphQLError(_("Pattern not found"))

        with transaction.atomic():
            datenow = datetime.now().date()
            project.project_status = '5'
            project.project_paid_date = datenow
            project.overrided_pattern = overrided_pattern
            project.save()
            project_income = project.project_income.all()
            for income in project_income:
                income.paid_status = '2'
                income.paid_date = datenow
                income.save()

            usecase.calculate_reward_distribution(project.pk)

            # distribution_pattern = project.pattern
            # distributor_percentage = distribution_pattern.distributor_percentage if distribution_pattern.distributor_percentage else 0
            # distributor_portion = utils.to_float(project.profit_aft_tax) * distributor_percentage / 100
            # accounting_reward_portion = utils.to_float(project.profit_aft_tax) - distributor_portion
            # RewardDistributorPortion.objects.update_or_create(project=project, company=distribution_pattern.accounting_company, distributor_position=1,
            #                                                   defaults={
            #                                                       'income': utils.to_float(project.profit_aft_tax),
            #                                                       'expense': distributor_portion,
            #                                                       'reward': accounting_reward_portion,
            #                                                     })

            # third_recipient_percentage = distribution_pattern.third_recipient_percentage if distribution_pattern.third_recipient_percentage else 0
            # third_recipient_portion = utils.to_float(project.profit_aft_tax) * third_recipient_percentage / 100
            # distributor_reward_portion = distributor_portion - third_recipient_portion
            # RewardDistributorPortion.objects.update_or_create(project=project, company=distribution_pattern.distributor_company, distributor_position=2,
            #                                                   defaults={
            #                                                       'income': distributor_portion,
            #                                                       'expense': third_recipient_portion,
            #                                                       'reward': distributor_reward_portion,
            #                                                       })

            # recipent_portion = 0

            # pattern_detail_list = distribution_pattern.pattern_detail.all()
            # for pattern_detail in pattern_detail_list:
            #     ProjectLog.objects.save_log(project, info.context.user, f'Create payslip for {pattern_detail.recipient.recipient_name}')

            #     recipient_amount = utils.rounddown(utils.to_float(project.profit_aft_tax) * pattern_detail.percentage / 100)
            #     recipent_portion += recipient_amount
            #     PaySlipReward.objects.update_or_create(
            #         reward_source_type=1,
            #         reward_source_id=project.pk,
            #         project=project,
            #         recipient=pattern_detail.recipient,
            #         defaults={
            #             'amount': recipient_amount,
            #             'billing_title': project.project_name,
            #             'pay_date': project.project_paid_date,
            #         }
            #     )

            #     RecipientRewardDistribution.objects.update_or_create(
            #         project=project,
            #         recipient=pattern_detail.recipient,
            #         defaults={
            #             'reward': recipient_amount,
            #         }
            #     )

            # third_recipient_reward_portion = third_recipient_portion - recipent_portion
            # RewardDistributorPortion.objects.update_or_create(project=project, company=distribution_pattern.third_recipient_company, distributor_position=3,
            #                                                   defaults={
            #                                                       'income': third_recipient_portion,
            #                                                       'expense': recipent_portion,
            #                                                       'reward': third_recipient_reward_portion,
            #                                                       })

        ProjectLog.objects.save_log(project, info.context.user, f'Project closed by {info.context.user.username}')
        return utils.Responser(result="Success")

class CreateProjectIncome(DjangoModelFormMutation):
    project_income = graphene.Field(ProjectIncomeNode)

    class Meta:
        form_class = ProjectIncomeForm
        return_field_name = 'project_income'

    @classmethod
    def get_form_kwargs(cls, root, info, **input):
        kwargs = super().get_form_kwargs(root, info, **input)
        data = kwargs.get('data')
        data['logged_user'] = info.context.user
        kwargs['data'] = data
        return kwargs

    @classmethod
    @login_required
    def perform_mutate(cls, form, info):
        return super().perform_mutate(form, info)

class DeleteProjectIncomeData(relay.ClientIDMutation):
    Output = utils.Responser

    class Input:
        id = graphene.ID(required=True)

    @login_required
    def mutate_and_get_payload(root, info, id):
        with transaction.atomic():
            project_income = ProjectIncome.objects.filter(pk=id).first()
            if not project_income:
                raise GraphQLError("Data not found!")

            if project_income.is_delete:
                raise GraphQLError("Data already deleted!")

            if project_income.project.project_status == '5':
                raise GraphQLError("Project already closed!")

            # if project_income.paid_status == '2' or project_income.paid_date:
            #     raise GraphQLError("Income already paid!")

            project_income.is_delete = True
            project_income.save()

            project = project_income.project  # Assuming `self.instance` is a ProjectIncome instance
            usecase.calculate_project_earning(project.pk)
            usecase.calculate_reward_distribution(project.pk)

        ProjectLog.objects.save_log(project_income.project, info.context.user, f'Project income {project_income.income_code} deleted')
        return utils.Responser(result="Success")

class UploadPaidProjectIncomeMutation(graphene.Mutation):
    class Arguments:
        project_id = graphene.ID(required=True)
        file_in = Upload(required=True)

    Output = utils.Responser

    @login_required
    def mutate(self, info, file_in, project_id):
        user = info.context.user
        if not user.is_admin():
            raise GraphQLError("You are not allowed to upload income data")

        project_data = Project.objects.filter(pk=project_id)
        if not project_data.exists():
            raise GraphQLError("Project not found")

        if project_data.first().project_status == '5':
            raise GraphQLError("Project already closed")

        if not user.is_eligible_manager(project_data.first().manager.pk) and not user.is_admin():
            raise GraphQLError("You are not allowed to upload income data for this project")

        with transaction.atomic():
            logger.info(file_in.name)
            excel = pd.ExcelFile(file_in)
            project_income_data = excel.parse(0).dropna(how='all').replace(np.nan, None, regex=True)
            project_income_data[['date']] = project_income_data[['date']].astype(object).where(project_income_data[['date']].notnull(), None)
            for data in project_income_data.to_dict(orient="records"):
                logger.info(f"project_income_data: {data}")

                if not data.get('income_code'):
                    raise GraphQLError(f"Income Code is required")

                if not data.get('date') or data.get('date') == '-':
                    raise GraphQLError(f"Date is required")

                income_data = ProjectIncome.objects.filter(income_code=data.get('income_code')).first()
                if not income_data:
                    raise GraphQLError(f"Income data {data.get('income_code')} not found")

                income_data.paid_status = "2"
                income_data.paid_date = data.get('date')
                income_data.save()

            print(f"registered project paid income: {model_to_dict(instance=income_data, fields=[field.name for field in income_data._meta.fields])}")
            ProjectLog.objects.save_log(income_data.project, info.context.user, f"registered project paid income: {model_to_dict(instance=income_data, fields=[field.name for field in income_data._meta.fields])}")

            usecase.calculate_project_earning(project_id)
            usecase.calculate_reward_distribution(project_id)

        return utils.Responser(result="Success")

class UploadPaidDateIncomeMutation(graphene.Mutation):
    class Arguments:
        file_in = Upload(required=True)

    Output = utils.Responser

    @login_required
    def mutate(self, info, file_in):
        user = info.context.user
        if not user.is_admin():
            raise GraphQLError("You are not allowed to upload income data")

        with transaction.atomic():
            logger.info(file_in.name)
            excel = pd.ExcelFile(file_in)
            project_income_data = excel.parse(0).dropna(how='all').replace(np.nan, None, regex=True)
            project_income_data[['date']] = project_income_data[['date']].astype(object).where(project_income_data[['date']].notnull(), None)
            for data in project_income_data.to_dict(orient="records"):
                logger.info(f"project_income_data: {data}")

                if not data.get('income_code'):
                    raise GraphQLError(f"Income Code is required")

                if not data.get('date') or data.get('date') == '-':
                    raise GraphQLError(f"Date is required")

                income_data = ProjectIncome.objects.filter(income_code=data.get('income_code')).first()
                if not income_data:
                    raise GraphQLError(f"Income data {data.get('income_code')} not found")

                project_data = income_data.project
                if project_data.project_status == '5':
                    raise GraphQLError(f"Project {project_data.project_id} already closed")

                income_data.paid_status = "2"
                income_data.paid_date = data.get('date')
                income_data.save()

                project_data = Project.objects.filter(pk=project_data.pk)
                usecase.calculate_project_earning(project_data.first().pk)
                usecase.calculate_reward_distribution(project_data.first().pk)

            logger.debug(f"registered project paid income: {model_to_dict(instance=income_data, fields=[field.name for field in income_data._meta.fields])}")
            ProjectLog.objects.save_log(income_data.project, info.context.user, f"registered project paid income: {model_to_dict(instance=income_data, fields=[field.name for field in income_data._meta.fields])}")

        return utils.Responser(result="Success")

class UploadProjectIncomeMutation(graphene.Mutation):
    class Arguments:
        project_id = graphene.ID(required=True)
        file_in = Upload(required=True)

    Output = utils.Responser

    @login_required
    def mutate(self, info, file_in, project_id):
        user = info.context.user
        if not user.is_admin():
            raise GraphQLError("You are not allowed to upload income data")

        project_data = Project.objects.filter(pk=project_id)
        if not project_data.exists():
            raise GraphQLError("Project not found")

        if project_data.first().project_status == '5':
            raise GraphQLError("Project already closed")

        if not info.context.user.is_eligible_manager(project_data.first().manager.pk) and not info.context.user.is_admin():
            raise GraphQLError("You are not allowed to upload income data for this project")

        with transaction.atomic():
            logger.info(file_in.name)
            excel = pd.ExcelFile(file_in)
            project_income_data = excel.parse(0).dropna(how='all').replace(np.nan, None, regex=True)
            project_income_data[['date']] = project_income_data[['date']].astype(object).where(project_income_data[['date']].notnull(), None)
            for data in project_income_data.to_dict(orient="records"):
                logger.info(f"project_income_data: {data}")

                if not data['billing_object']:
                    raise GraphQLError(f"Billing to is required")

                billing_to = MstCompany.objects.filter(company_name=data.get('billing_object')).first()
                if not billing_to:
                    raise GraphQLError(f"Billing to {data.get('billing_object')} not found")

                total_income = ProjectIncome.objects.count()

                is_paid_project = data.get('date') is not None or not data.get('date') == '-'
                paid_status = '2' if is_paid_project else '1'
                if not data.get('date') or data.get('date') == '-':
                    data['date'] = None

                if data.get('income_code') and not ProjectIncome.objects.filter(project=project_data.first(), income_code=data.get('income_code')).exists():
                    raise GraphQLError(f"Income code {data.get('income_code')} already deleted")

                if 'income_code' in data:
                    proj_inc, created = ProjectIncome.objects.update_or_create(
                        project=project_data.first(),
                        income_code=data.get('income_code'),
                        defaults={
                            'paid_date': data.get('date'),
                            'paid_status': paid_status,
                            'billing_title': data.get('subject'),
                            'billing_to': billing_to,
                            'price': data.get('sale'),
                            'note': data.get('note'),
                            'issue_date': data.get('issue_date'),
                        }
                    )
                else:
                    try:
                        proj_inc, created = ProjectIncome.objects.update_or_create(
                            project=project_data.first(),
                            income_code="INC-" + "{:04d}".format(total_income + 1),
                            defaults={
                                'paid_date': data.get('date'),
                                'paid_status': paid_status,
                                'billing_title': data.get('subject'),
                                'billing_to': billing_to,
                                'price': data.get('sale'),
                                'note': data.get('note'),
                                'issue_date': data.get('issue_date'),
                            }
                        )
                    except IntegrityError as e:
                        raise GraphQLError("Income code alredy exist")

            logger.debug(f"registered project income: {model_to_dict(instance=proj_inc, fields=[field.name for field in proj_inc._meta.fields])}")
            ProjectLog.objects.save_log(proj_inc.project, info.context.user, f"registered project income: {model_to_dict(instance=proj_inc, fields=[field.name for field in proj_inc._meta.fields])}")

            with transaction.atomic():
                usecase.calculate_project_earning(project_id)
                usecase.calculate_reward_distribution(project_id)

        return utils.Responser(result="Success")

class UploadBulkProjectIncomeMutation(graphene.Mutation):
    class Arguments:
        file_in = Upload(required=True)

    Output = utils.Responser

    @login_required
    def mutate(self, info, file_in):
        user = info.context.user
        if not user.is_admin():
            raise GraphQLError("You are not allowed to upload income data")

        logger.info(file_in.name)
        excel = pd.ExcelFile(file_in)
        project_income_data = excel.parse(0).dropna(how='all').replace(np.nan, None, regex=True)
        project_income_data[['date']] = project_income_data[['date']].astype(object).where(project_income_data[['date']].notnull(), None)
        for data in project_income_data.to_dict(orient="records"):
            logger.info(f"project_income_data: {data}")

            project_data = Project.objects.filter(project_id=data.get('proj id')).first()
            if not project_data:
                logger.error(f"Project {data.get('proj id')} not found")
                raise GraphQLError(f"{data.get('proj id')}の情報が登録できませんでした")

            with transaction.atomic():
                if project_data.project_status == '5':
                    logger.error(f"Project {data.get('proj id')} already closed")
                    raise GraphQLError(f"{data.get('proj id')}の情報が登録できませんでした")

                if not info.context.user.is_eligible_manager(project_data.manager.pk) and not info.context.user.is_admin():
                    raise GraphQLError("You are not allowed to upload income data for this project")

                if not data.get('billing_object'):
                    raise GraphQLError(f"Billing to is required")

                billing_to = MstCompany.objects.filter(company_name=data.get('billing_object')).first()
                if not billing_to:
                    raise GraphQLError(f"Billing to {data.get('billing_object')} not found")

                is_paid_project = data.get('date') is not None or not data.get('date') == '-'
                paid_status = '2' if is_paid_project else '1'
                if not data.get('date') or data.get('date') == '-':
                    data['date'] = None

                if data.get('income_code') and not ProjectIncome.objects.filter(project=project_data, income_code=data.get('income_code')).exists():
                    raise GraphQLError(f"Income code {data.get('income_code')} already deleted")

                if 'income_code' in data:
                    proj_inc, created = ProjectIncome.objects.update_or_create(
                        project=project_data,
                        income_code=data.get('income_code'),
                        defaults={
                            'paid_date': data.get('date'),
                            'paid_status': paid_status,
                            'billing_title': data.get('subject'),
                            'billing_to': billing_to,
                            'price': data.get('sale'),
                            'note': data.get('note'),
                            'issue_date': data.get('issue_date'),
                        }
                    )
                else:
                    total_income = ProjectIncome.objects.get_total_data()
                    insert_count = 0
                    while True:
                        if insert_count > 10:
                            break
                        try:
                            income_code = "INC-" + str(total_income + 1)
                            proj_inc, created = ProjectIncome.objects.update_or_create(
                                project=project_data,
                                income_code=income_code,
                                defaults={
                                    'paid_date': data.get('date'),
                                    'paid_status': paid_status,
                                    'billing_title': data.get('subject'),
                                    'billing_to': billing_to,
                                    'price': data.get('sale'),
                                    'note': data.get('note'),
                                    'issue_date': data.get('issue_date'),
                                }
                            )
                            if created or proj_inc.income_code == income_code:
                                break
                            
                        except IntegrityError as e:
                            total_income += 1
                            insert_count += 1

            logger.debug(f"registered project income: {model_to_dict(instance=proj_inc, fields=[field.name for field in proj_inc._meta.fields])}")
            ProjectLog.objects.save_log(proj_inc.project, info.context.user, f"registered project income: {model_to_dict(instance=proj_inc, fields=[field.name for field in proj_inc._meta.fields])}")

            with transaction.atomic():
                usecase.calculate_project_earning(project_data.pk)
                usecase.calculate_reward_distribution(project_data.pk)

        return utils.Responser(result="Success")

class UploadProjectCostMutation(graphene.Mutation):
    class Arguments:
        file_in = Upload(required=True)

    Output = utils.Responser

    @login_required
    def mutate(self, info, file_in):
        user = info.context.user
        if not user.is_admin():
            raise GraphQLError("You are not allowed to upload cost data")

        logger.info(file_in.name)
        excel = pd.ExcelFile(file_in)
        project_income_data = excel.parse(0).dropna(how='all').replace(np.nan, None, regex=True)
        for data in project_income_data.to_dict(orient="records"):
            with transaction.atomic():
                logger.info(f"project_cost_data: {data}")

                project_data = Project.objects.filter(project_id=data.get('proj id')).first()
                if not project_data:
                    logger.error(f"Project {data.get('proj id')} not found")
                    raise GraphQLError(f"{data.get('proj id')}の情報が登録できませんでした")

                if project_data.project_status == '5':
                    logger.error(f"Project {data.get('proj id')} already closed")
                    raise GraphQLError(f"{data.get('proj id')}の情報が登録できませんでした")

                if data.get('tax') not in [8, 10]:
                    logger.error(f"Project {data.get('proj id')} tax invalid")
                    raise GraphQLError(f"空白の税区分があります、修正し再度インポートしてください")

                total_cost = ProjectCost.objects.count()
                while True:
                    try:
                        cost_code = "CST-" + "{:04d}".format(total_cost + 1)
                        if ProjectCost.objects.filter(cost_code=cost_code).exists():
                            total_cost += 1
                            continue

                        proj_cost, created = ProjectCost.objects.update_or_create(
                            project=project_data,
                            cost_code="CST-" + "{:04d}".format(total_cost + 1),
                            defaults={
                                'issue_date': data.get('paid date'),
                                'receipt_title': data.get('title'),
                                'paid_to': data.get('pay to'),
                                'price': data.get('price'),
                                'note': data.get('note'),
                                'tax_category': data.get('tax'),
                            }
                        )
                    except IntegrityError as e:
                        logger.warning(f"Cost code already exist")
                        total_cost += 1
                        continue
                    break


            logger.debug(f"registered project cost: {model_to_dict(instance=proj_cost, fields=[field.name for field in proj_cost._meta.fields])}")
            ProjectLog.objects.save_log(proj_cost.project, info.context.user, f"registered project cost: {model_to_dict(instance=proj_cost, fields=[field.name for field in proj_cost._meta.fields])}")

            with transaction.atomic():
                usecase.calculate_project_earning(project_data.pk)
                usecase.calculate_reward_distribution(project_data.pk)

        return utils.Responser(result="Success")

class CreateProjectCost(DjangoModelFormMutation):
    project_cost = graphene.Field(ProjectCostNode)

    class Meta:
        form_class = ProjectCostForm
        return_field_name = 'project_cost'

    @classmethod
    def get_form_kwargs(cls, root, info, **input):
        kwargs = super().get_form_kwargs(root, info, **input)
        data = kwargs.get('data')
        data['logged_user'] = info.context.user
        kwargs['data'] = data
        return kwargs

    @classmethod
    @login_required
    def perform_mutate(cls, form, info):
        return super().perform_mutate(form, info)

class DeleteProjectCostData(relay.ClientIDMutation):
    Output = utils.Responser

    class Input:
        id = graphene.ID(required=True)

    @login_required
    def mutate_and_get_payload(root, info, id):
        with transaction.atomic():
            project_cost = ProjectCost.objects.get(pk=id)
            if not project_cost:
                raise GraphQLError("Data not found!")

            if project_cost.is_delete:
                raise GraphQLError("Data already deleted!")

            if project_cost.project.project_status == '5':
                raise GraphQLError("Project already closed!")

            project_cost.is_delete = True
            project_cost.save()

            project = project_cost.project  # Assuming `self.instance` is a ProjectIncome instance
            usecase.calculate_project_earning(project.pk)
            usecase.calculate_reward_distribution(project.pk)

        ProjectLog.objects.save_log(project_cost.project, info.context.user, f'Project cost {project_cost.cost_code} deleted')
        return utils.Responser(result="Success")

class UpdatePaySlipReward(DjangoModelFormMutation):
    pay_slip_reward = graphene.Field(PaySlipRewardNode)

    class Meta:
        form_class = PaySlipRewardForm
        return_field_name = 'pay_slip_reward'

    @classmethod
    @login_required
    def perform_mutate(cls, form, info):
        return super().perform_mutate(form, info)

class CreatePaySlipStatement(DjangoModelFormMutation):
    pay_slip_statement = graphene.Field(PaySlipStatementNode)

    class Meta:
        form_class = PaySlipStatementForm
        return_field_name = 'pay_slip_statement'

    @classmethod
    def get_form_kwargs(cls, root, info, **input):
        kwargs = super().get_form_kwargs(root, info, **input)
        data = kwargs.get('data')
        data['logged_user'] = info.context.user
        kwargs['data'] = data
        return kwargs

    @classmethod
    @login_required
    def perform_mutate(cls, form, info):
        return super().perform_mutate(form, info)

class DownloadPaySlipStatement(relay.ClientIDMutation):
    file = graphene.String()

    class Input:
        id = graphene.ID(required=True)

    @login_required
    def mutate_and_get_payload(root, info, id):
        statement = PaySlipStatement.objects.get(pk=id)
        if not statement:
            raise GraphQLError("Data not found")

        payslip_ids = statement.payslip_id_list.split(",")
        payslip = PaySlipReward.objects.filter(pk__in=payslip_ids)
        payslip_per_segment = {}
        for data in payslip:
            segment_data = {}
            if data.project.segment.pk not in payslip_per_segment:
                segment_data['segment'] = data.project.segment
                if 'ライブ' in data.project.segment.segment_name or 'live' in data.project.segment.segment_name.lower():
                    segment_data['segment_type'] = 'LIVE活動'
                else:
                    segment_data['segment_type'] = 'その他の活動'

                project_data = {}
                project_data['project'] = data.project
                project_data['reward'] = data

                segment_data['project_detail'] = [project_data]
                payslip_per_segment[data.project.segment.pk] = segment_data
                continue

            project_data = {}
            project_data['project'] = data.project
            project_data['reward'] = data
            payslip_per_segment[data.project.segment.pk]['project_detail'].append(project_data)

        pdf_result = utils.render_to_document('payslip/payslip_recipient_statement.html',
                                         {
                                             'payslip_per_segment': payslip_per_segment,
                                             'recipient_name': statement.recipient.recipient_name,
                                             'registration_number': statement.recipient.registered_number,
                                             'payslip_period': statement.period,
                                        })

        return DownloadPaySlipStatement(file=pdf_result)

class BulkChangeProjectStatusMutation(graphene.Mutation):
    class Arguments:
        project_ids = graphene.String(required=True)
        target_status = graphene.String(required=True)

    Output = utils.Responser

    @login_required
    def mutate(self, info, project_ids, target_status):
        user = info.context.user
        if not user.is_admin() and not target_status == '3':
            raise GraphQLError("You are not allowed to change project status")

        if target_status == '5':
            raise GraphQLError("You are not allowed to change project status to closed")

        project_ids = project_ids.split(",")
        project_list = Project.objects.filter(pk__in=project_ids).exclude(project_status='5')
        if len(project_list.all()) == 0:
            raise GraphQLError("Data not found")

        status_dict = {key: value for key, value in Project.PROJECT_STATUS}
        if target_status == '5' or status_dict.get(target_status) is None:
            raise GraphQLError("Invalid status!")

        if user.is_manager() and not target_status == '3' and not project_list.first().project_status == '2':
            raise GraphQLError("You are not allowed to change project status")

        if user.is_manager() and target_status == '3' and project_list.first().project_status == '2':
            project_list.first().project_status = target_status
            project_list.first().save()

            return utils.Responser(result="Success")

        with transaction.atomic():
            for project in project_list:
                project.project_status = target_status
                if target_status == '5':
                    project.project_paid_date = datetime.now().date()
                project.save()

                ProjectLog.objects.save_log(project, info.context.user, f'Project {project.project_id} status changed to {target_status} by {info.context.user.username}')

        return utils.Responser(result="Success")

class RecalculateProjectDistributionRewardMutation(graphene.Mutation):
    class Arguments:
        project_id = graphene.ID(required=True)

    Output = utils.Responser

    @login_required
    def mutate(self, info, project_id):
        user = info.context.user
        if not user.is_admin():
            raise GraphQLError("You are not allowed to recalculate distribution reward")

        project = Project.objects.filter(pk=project_id).first()
        if not project:
            raise GraphQLError("Data not found")

        if project.project_status == '5':
            raise GraphQLError("Project already closed")

        with transaction.atomic():
            usecase.calculate_reward_distribution(project.pk)

        return utils.Responser(result="Success")

class UpdateProjectDistributionCompanyMappingMutation(DjangoModelFormMutation):
    project_distribution_company_mapping = graphene.Field(DistributionCompanyNode)

    class Meta:
        form_class = DistributionCompanyMappingForm
        return_field_name = 'project_distribution_company_mapping'

    @classmethod
    def get_form_kwargs(cls, root, info, **input):
        kwargs = super().get_form_kwargs(root, info, **input)
        data = kwargs.get('data')
        data['logged_user'] = info.context.user
        kwargs['data'] = data
        return kwargs

    @classmethod
    @login_required
    def perform_mutate(cls, form, info):
        return super().perform_mutate(form, info)

class UpdateProjectDistributionRecipientMappingMutation(DjangoModelFormMutation):
    project_distribution_recipient_mapping = graphene.Field(DistributionRecipientNode)

    class Meta:
        form_class = DistributionRecipientMappingForm
        return_field_name = 'project_distribution_recipient_mapping'

    @classmethod
    def get_form_kwargs(cls, root, info, **input):
        kwargs = super().get_form_kwargs(root, info, **input)
        data = kwargs.get('data')
        data['logged_user'] = info.context.user
        kwargs['data'] = data
        return kwargs

    @classmethod
    @login_required
    def perform_mutate(cls, form, info):
        return super().perform_mutate(form, info)
