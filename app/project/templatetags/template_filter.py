from django import template
import math
from app.common import utils

register = template.Library()

@register.filter(name='access_by_index')
def access_by_index(value, index):
    try:
        return value[index]
    except IndexError:
        return None

@register.filter(name='roundup')
def roundup(value):
    return utils.rounddown(float(value))

@register.filter(name='format_currency')
def format_currency(value):
    return utils.format_currency_value(int(value))