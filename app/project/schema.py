import graphene
from graphene_django import DjangoConnectionField
from graphene_django.filter import DjangoFilterConnection<PERSON>ield
from graphql import GraphQLError

from app.master_data.models import MstPaymentRecipient
from app.project.graphql.mutations import *
from app.project.graphql.types import *
from app.project.models import PaySlipReward, PaySlipStatement, Project, ProjectCost, ProjectIncome, ProjectLog
from graphql_jwt.decorators import login_required
from django.db.models import Q


class Query(graphene.ObjectType):
    project_detail = graphene.Field(ProjectNode, id=graphene.ID())
    all_project = DjangoFilterConnectionField(ProjectNode, search=graphene.String(), status=graphene.String())
    all_project_income = DjangoConnectionField(ProjectIncomeNode, project_id=graphene.ID(), paid_status=graphene.Boolean())
    all_project_cost = DjangoConnectionField(ProjectCostNode, project_id=graphene.ID())
    all_project_log = DjangoConnectionField(ProjectLogNode, project_id=graphene.ID())
    payment_recipient_list = DjangoFilterConnectionField('app.user.schema.PaymentRecipientNode', recipient_id=graphene.ID())
    payslip_per_recipient = DjangoConnectionField(PaySlipRewardNode, recipient_id=graphene.ID(), start_date=graphene.Date(), end_date=graphene.Date())
    all_pay_method = graphene.List(graphene.String)
    all_pay_slip_statement = DjangoFilterConnectionField(PaySlipStatementNode, recipient_id=graphene.ID())
    all_tax = graphene.List(TaxListNode)
    distribution_price_list = DjangoFilterConnectionField(RewardDistributorPortionNode)
    project_distribution_company_mapping = graphene.Field(DistributionCompanyNode, project_id=graphene.ID())
    project_distribution_company_mapping_list = DjangoFilterConnectionField(DistributionCompanyNode)
    project_distribution_recipient_mapping_list = DjangoFilterConnectionField(DistributionRecipientNode)

    @login_required
    def resolve_project_detail(self, info, id):
        project = Project.objects.get(pk=id)
        if not project:
            raise GraphQLError("Data not found")
        return project

    @login_required
    def resolve_all_project(self, info, **kwargs):
        if info.context.user.is_admin():
            project = Project.objects.all()
        else:
            project = Project.objects.filter(manager=info.context.user)

        if 'search' in kwargs:
            project = project.filter(project_name__icontains=kwargs.get("search"))

        if 'status' in kwargs:
            project = project.filter(project_status=kwargs.get("status"))

        return project

    @login_required
    def resolve_all_project_income(self, info, **kwargs):
        income = ProjectIncome.objects.all()
        if 'project_id' in kwargs:
            if kwargs.get('project_id'):
                income = income.filter(project__pk=kwargs.get('project_id'))

        if 'paid_status' in kwargs:
            paid_status = True if kwargs.get('paid_status') else False
            income = income.filter(paid_date__isnull=not paid_status)

        return income

    @login_required
    def resolve_all_project_cost(self, info, **kwargs):
        if 'project_id' not in kwargs:
            return ProjectCost.objects.none()
        return ProjectCost.objects.filter(project__pk=kwargs.get('project_id'))

    @login_required
    def resolve_all_project_log(self, info, **kwargs):
        logs = ProjectLog.objects.all()
        if 'project_id' in kwargs:
            if kwargs.get('project_id'):
                logs = logs.filter(project__pk=kwargs.get('project_id'))
        return logs

    @login_required
    def resolve_payment_recipient_list(self, info, **kwargs):
        if info.context.user.is_admin():
            return MstPaymentRecipient.objects.all()

        recipient = MstPaymentRecipient.objects.filter(manager=info.context.user)
        if 'recipient_id' in kwargs:
            recipient = recipient.filter(pk=kwargs.get('recipient_id'))

        return recipient

    @login_required
    def resolve_payslip_per_recipient(self, info, **kwargs):
        if 'recipient_id' not in kwargs:
            return PaySlipReward.objects.none()

        rewards = PaySlipReward.objects.filter(recipient__pk=kwargs.get('recipient_id'), is_paid=False)

        if ('start_date' in kwargs and not 'end_date' in kwargs) or ( not 'start_date' in kwargs and 'end_date' in kwargs):
            raise GraphQLError("Start date or end date is required")

        if 'start_date' in kwargs and 'end_date' in kwargs:
            rewards = rewards.filter(pay_date__range=[kwargs.get('start_date'), kwargs.get('end_date')])

        return rewards

    @login_required
    def resolve_all_pay_method(self, info):
        return ["銀行振込","アドバンスと相殺","マイナス繰越","貸付金と相殺"]

    @login_required
    def resolve_all_pay_slip_statement(self, info, **kwargs):
        if 'recipient_id' not in kwargs:
            return PaySlipStatement.objects.none()
        return PaySlipStatement.objects.filter(recipient__pk=kwargs.get('recipient_id'))

    @login_required
    def resolve_all_tax(self, info):
        tax_list = [
            TaxListNode(code='8%', name='8%'),
            TaxListNode(code='10%', name='10%'),
        ]
        return tax_list

    @staticmethod
    @login_required
    def resolve_distribution_price_list(self, info, **kwargs):
        return RewardDistributorPortion.objects.filter(sale_data__isnull=True).order_by('project__id', '-created_at').distinct('project__id').all()

    @login_required
    def resolve_project_distribution_company_mapping(self, info, **kwargs):
        if 'project_id' not in kwargs:
            return DistributionCompany.objects.none()
        return DistributionCompany.objects.filter(project__pk=kwargs.get('project_id'))

    @login_required
    def resolve_project_distribution_company_mapping_list(self, info, **kwargs):
        return DistributionCompany.objects.all()

    @login_required
    def resolve_project_distribution_recipient_mapping(self, info, **kwargs):
        return DistributionRecipient.objects.all()

class Mutation(graphene.ObjectType):
    create_project = CreateProject.Field()
    close_project = CloseProject.Field()
    create_project_income = CreateProjectIncome.Field()
    delete_project_income = DeleteProjectIncomeData.Field()
    upload_project_income = UploadProjectIncomeMutation.Field()
    upload_paid_project_income = UploadPaidProjectIncomeMutation.Field()
    upload_paid_date_income = UploadPaidDateIncomeMutation.Field()
    create_project_cost = CreateProjectCost.Field()
    delete_project_cost = DeleteProjectCostData.Field()
    create_pay_slip_statement = CreatePaySlipStatement.Field()
    download_pay_slip_statement = DownloadPaySlipStatement.Field()
    update_pay_slip_reward = UpdatePaySlipReward.Field()
    bulk_update_project_status = BulkChangeProjectStatusMutation.Field()
    upload_project_cost = UploadProjectCostMutation.Field()
    upload_bulk_project_income = UploadBulkProjectIncomeMutation.Field()
    recalculate_project_distribution_reward = RecalculateProjectDistributionRewardMutation.Field()
    update_project_distribution_company_mapping = UpdateProjectDistributionCompanyMappingMutation.Field()
    update_project_distribution_recipient_mapping = UpdateProjectDistributionRecipientMappingMutation.Field()
