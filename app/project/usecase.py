import logging
from django.forms import model_to_dict
import numpy as np
import pandas as pd
from django.conf import settings

from django.db.models import <PERSON><PERSON>, IntegerField, ExpressionWrapper, <PERSON>loatField
from django.db import transaction
from django.db.models.functions import Cast

from app.common import utils
from app.master_data.models import MstArtist, MstDistributionPattern, MstSegment
from app.project.models import DistributionCompany, DistributionRecipient, PaySlipReward, Project, RecipientRewardDistribution, RewardDistributorPortion
from app.user.models import User

media_directory = settings.MEDIA_ROOT
logger = logging.getLogger(__name__)

def register_project(file_in):
    excel = pd.ExcelFile(file_in)
    project_data = excel.parse(0).dropna(how='all').replace(np.nan, None, regex=True)

    expected_column = ['project_name', 'pattern_name', 'segment_name', 'manager_email', 'artist_name', 'issue_date']
    for col in expected_column:
        if col not in project_data.columns:
            logger.error(f"Column {col} not found in the excel file")
            raise Exception("Excelフォーマットが正しくありません")

    try:
        project_data[['issue_date']] = project_data[['issue_date']].astype(object).where(project_data[['issue_date']].notnull(), None)
    except Exception as e:
        logger.error(f"Error: {e}")
        raise Exception("Excelフォーマットが正しくありません")

    with transaction.atomic():
        for data in project_data.to_dict(orient="records"):
            logger.info(f"project_data: {data}")
            logger.info(f"=========================")
            if not data.get('project_name'):
                logger.info("project_name is required.")
                continue

            manager = None
            if data.get('manager_email'):
                manager = User.objects.filter(email=data.get('manager_email')).first()
                if not manager:
                    logger.info(f'Manager {data.get("manager_email")} not found.')
                    continue
            else:
                logger.info("manager_email is required.")
                continue

            pattern_data = None
            if data.get('pattern_name'):
                pattern_data = MstDistributionPattern.objects.filter(pattern_title__iexact=data.get('pattern_name')).first()
                if not pattern_data:
                    logger.info(f'Pattern Data {data.get("pattern_name")} not found.')
                    continue
            else:
                logger.info("pattern_name is required.")
                continue

            segment = None
            if data.get('segment_name'):
                segment = MstSegment.objects.filter(segment_name=data.get('segment_name')).first()
                if not segment:
                    logger.info(f'Segment Data {data.get("segment_name")} not found.')
                    continue
            else:
                logger.info("segment_name is required.")
                continue

            artist = None
            if data.get('artist_name'):
                artist = MstArtist.objects.filter(artist_name=data.get('artist_name')).first()
                if not segment:
                    logger.info(f'Artist Data {data.get("artist_name")} not found.')
                    continue
            else:
                logger.info("artist_name is required.")
                continue

            # project_status = '1'
            # if not '-' == data.get('project_paid_date'):
            #     project_status = '2'
            # else:
            #     data.get('project_paid_date') = None

            # total_project = Project.objects.get_total_data()
            try:
                last_project = Project.objects.latest('id')
            except Exception as e:
                logger.info(utils.append_error(e))
                last_project = None
            last_id = last_project.pk if last_project else 0
            project_prefix = 'PRJ'
            if artist:
                project_prefix = artist.artist_name_abbv if artist.artist_name_abbv else 'PRJ'

            loop_product_id = 0
            project_id = project_prefix + "{:04d}".format(last_id + 1)
            while True:
                try:
                    if not Project.objects.filter(project_id=project_id).exists():
                        break

                    if loop_product_id > 10:
                        break

                    last_id += 1
                    project_id = project_prefix + "{:04d}".format(last_id)
                    loop_product_id += 1
                except Exception as e:
                    if loop_product_id > 10:
                        break

                    last_id += 1
                    project_id = project_prefix + "{:04d}".format(last_id)
                    loop_product_id += 1
                    continue

            project, created = Project.objects.update_or_create(
                project_id=project_id,
                    defaults={
                        'project_name': data.get('project_name'),
                        'manager': manager,
                        'pattern': pattern_data,
                        'segment': segment,
                        'issue_date': data.get('issue_date'),
                        'artist': artist
                        # 'project_paid_date': data.get('project_paid_date'),
                        # 'temporary_sale': data.get('temporary_sale'),
                        # 'sale_aft_tax': data.get('sale_aft_tax'),
                        # 'cost_aft_tax': data.get('cost_aft_tax'),
                        # 'temp_profit_aft_tax': data.get('temp_profit_aft_tax'),
                        # 'profit_aft_tax': data.get('profit_aft_tax'),
                        # 'project_status': project_status,
                    }
            )

            if project:
                logger.info(f"registered project: {model_to_dict(instance=project, fields=[field.name for field in project._meta.fields])}")

def calculate_project_earning(project_pk):
    if project_pk is None:
        return
    temporary_sale = 0
    project_data = Project.objects.filter(pk=project_pk)
    temporary_sale_query = project_data.filter(project_income__is_delete=False).annotate(total_income=Sum(Cast('project_income__price', IntegerField()))).first()
    if temporary_sale_query:
        temporary_sale = temporary_sale_query.total_income
        temporary_sale = 0 if temporary_sale is None else temporary_sale / 1.1

    sale_price = 0
    sale_price_query = project_data.filter(project_income__is_delete=False).annotate(total_income=Sum(Cast('project_income__price', IntegerField()))).first()
    if sale_price_query:
        sale_price = sale_price_query.total_income
    sale_price = 0 if sale_price is None else sale_price
    sale_after_tax = 0
    if sale_price > 0:
        sale_after_tax = sale_price / 1.1

    cost_price = 0
    total_cost_query = project_data.filter(project_cost__is_delete=False).aggregate(
        total_cost=Sum(ExpressionWrapper(Cast('project_cost__price', IntegerField()), output_field=IntegerField()))
    )
    if total_cost_query.get('total_cost'):
        cost_price = total_cost_query.get('total_cost')
    cost_price = 0 if cost_price is None else cost_price

    cost_after_tax = 0
    total_cost_aft_tax_query = project_data.filter(project_cost__is_delete=False).aggregate(
        total_cost=Sum(ExpressionWrapper(Cast('project_cost__price', IntegerField()) / (1 + Cast('project_cost__tax_category', FloatField()) / 100), output_field=IntegerField()))
    )
    if total_cost_aft_tax_query.get('total_cost'):
        cost_after_tax = total_cost_aft_tax_query.get('total_cost')
    cost_after_tax = 0 if cost_after_tax is None else cost_after_tax

    profit = sale_after_tax - cost_after_tax
    Project.objects.filter(pk=project_pk).update(
        temporary_sale=utils.rounddown(temporary_sale),
        sale_price=utils.rounddown(sale_price),
        sale_aft_tax=utils.rounddown(sale_after_tax),
        cost_price=utils.rounddown(cost_price),
        cost_aft_tax=utils.rounddown(cost_after_tax),
        profit_aft_tax=utils.rounddown(profit))

def calculate_reward_distribution(project_id):
    project = Project.objects.filter(pk=project_id)
    if not project.exists():
        return
    project = project.first()

    distribution_pattern = project.overrided_pattern
    if hasattr(distribution_pattern, 'pattern_detail'):
        pattern_detail_list = distribution_pattern.pattern_detail.all()

    if not distribution_pattern:
        distribution_pattern = DistributionCompany.objects.filter(project=project).first()
        pattern_detail_list = DistributionRecipient.objects.filter(project=project).all()

    if not distribution_pattern:
        logger.info(f"Distribution Pattern not found for project {project.project_name}")
        return

    distributor_percentage = distribution_pattern.distributor_percentage if distribution_pattern.distributor_percentage else 0
    distributor_portion = utils.to_float(project.profit_aft_tax) * distributor_percentage / 100
    accounting_reward_portion = utils.to_float(project.profit_aft_tax) - distributor_portion
    RewardDistributorPortion.objects.update_or_create(project=project, distributor_position=1,
                                                        defaults={
                                                            'company': distribution_pattern.accounting_company,
                                                            'income': utils.rounddown(utils.to_float(project.profit_aft_tax)),
                                                            'expense': utils.rounddown(distributor_portion),
                                                            'reward': utils.rounddown(accounting_reward_portion),
                                                        })

    third_recipient_percentage = distribution_pattern.third_recipient_percentage if distribution_pattern.third_recipient_percentage else 0
    third_recipient_portion = utils.to_float(project.profit_aft_tax) * third_recipient_percentage / 100
    distributor_reward_portion = distributor_portion - third_recipient_portion
    RewardDistributorPortion.objects.update_or_create(project=project, distributor_position=2,
                                                        defaults={
                                                            'company': distribution_pattern.distributor_company,
                                                            'income': utils.rounddown(distributor_portion),
                                                            'expense': utils.rounddown(third_recipient_portion),
                                                            'reward': utils.rounddown(distributor_reward_portion),
                                                            })

    recipent_portion = 0

    if project.project_status != '5':
        RecipientRewardDistribution.objects.filter(
            project=project,
        ).delete()
    for pattern_detail in pattern_detail_list:
        recipient_amount = utils.rounddown(utils.to_float(project.profit_aft_tax) * pattern_detail.percentage / 100)
        recipent_portion += recipient_amount
        if project.project_status == '5':
            PaySlipReward.objects.update_or_create(
                reward_source_type=1,
                reward_source_id=project.pk,
                project=project,
                recipient=pattern_detail.recipient,
                defaults={
                    'amount': recipient_amount,
                    'billing_title': project.project_name,
                    'pay_date': project.project_paid_date,
                }
            )

        RecipientRewardDistribution.objects.update_or_create(
            project=project,
            recipient=pattern_detail.recipient,
            defaults={
                'reward': recipient_amount,
            }
        )

    third_recipient_reward_portion = third_recipient_portion - recipent_portion
    RewardDistributorPortion.objects.update_or_create(project=project, distributor_position=3,
                                                        defaults={
                                                            'company': distribution_pattern.third_recipient_company,
                                                            'income': utils.rounddown(third_recipient_portion),
                                                            'expense': utils.rounddown(recipent_portion),
                                                            'reward': utils.rounddown(third_recipient_reward_portion),
                                                            })