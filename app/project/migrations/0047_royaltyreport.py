# Generated by Django 4.2.13 on 2024-08-13 03:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('project', '0046_productdetail_media_title_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='RoyaltyReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('file_status', models.CharField(blank=True, choices=[('in_progress', 'In Progress'), ('done', 'Done'), ('error', 'Error')], null=True, verbose_name='File Status')),
                ('file_name', models.CharField(blank=True, max_length=255, null=True, verbose_name='File Name')),
                ('report_file', models.FileField(blank=True, null=True, upload_to='royalty_report/', verbose_name='Report File')),
            ],
            options={
                'verbose_name': 'Royalty Report',
                'verbose_name_plural': 'Royalty Reports',
                'ordering': ['-created_at'],
            },
        ),
    ]
