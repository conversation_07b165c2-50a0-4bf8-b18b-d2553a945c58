# Generated by Django 4.2.13 on 2024-06-25 06:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('project', '0030_payslipreward_note'),
    ]

    operations = [
        migrations.AddField(
            model_name='productdetail',
            name='issue_date',
            field=models.DateField(blank=True, null=True, verbose_name='Issue Date'),
        ),
        migrations.AddField(
            model_name='productdetail',
            name='product_category',
            field=models.CharField(blank=True, choices=[('cd', 'CD'), ('dvd', 'DVD'), ('bluray', 'ブルーレイ'), ('book', '書籍')], max_length=255, null=True, verbose_name='Product Category'),
        ),
        migrations.AddField(
            model_name='productdetail',
            name='sale_price',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Sale Price'),
        ),
    ]
