# Generated by Django 4.2.13 on 2024-06-08 12:51

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('master_data', '0014_mstdistributionpatterngroupdetail_pattern_group'),
        ('project', '0011_projectcost_issue_date'),
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentStatement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('title', models.CharField(blank=True, max_length=255, null=True, verbose_name='Title')),
                ('period', models.CharField(blank=True, max_length=255, null=True, verbose_name='Period')),
                ('withdraw_date', models.DateField(blank=True, null=True, verbose_name='Withdraw Date')),
                ('pay_method', models.CharField(blank=True, max_length=255, null=True, verbose_name='Pay Method')),
                ('total_payment', models.CharField(blank=True, max_length=255, null=True, verbose_name='Total Payment')),
                ('recipient', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='payment_statement', to='master_data.mstpaymentrecipient')),
            ],
            options={
                'verbose_name': 'Payment Statement',
                'verbose_name_plural': 'Payment Statements',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PaymentStatementReward',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('item', models.CharField(blank=True, max_length=255, null=True, verbose_name='Item')),
                ('amount', models.CharField(blank=True, max_length=255, null=True, verbose_name='Amount')),
                ('statement_type', models.CharField(blank=True, choices=[('1', 'Reward'), ('2', 'Other')], default='1', max_length=10, null=True, verbose_name='Statement Type')),
                ('statement', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='payment_statement_reward', to='project.paymentstatement')),
            ],
            options={
                'verbose_name': 'Payment Statement Reward',
                'verbose_name_plural': 'Payment Statement Rewards',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PaymentReward',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('pay_date', models.DateField(blank=True, null=True, verbose_name='Pay Date')),
                ('billing_title', models.CharField(blank=True, max_length=255, null=True, verbose_name='Billing Title')),
                ('amount', models.CharField(blank=True, max_length=255, null=True, verbose_name='Amount')),
                ('project', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='payment_recipient_detail', to='project.project')),
                ('recipient', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='payment_recipient_detail', to='master_data.mstpaymentrecipient')),
            ],
            options={
                'verbose_name': 'Payment Reward',
                'verbose_name_plural': 'Payment Rewards',
                'ordering': ['-created_at'],
            },
        ),
    ]
