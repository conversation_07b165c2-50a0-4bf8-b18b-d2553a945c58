# Generated by Django 4.2.13 on 2024-06-19 06:38

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('master_data', '0015_mstsegment_segment_host'),
        ('project', '0017_remove_payslipstatementreward_payslip_id_list_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductDetail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('product_id', models.CharField(blank=True, max_length=255, null=True, verbose_name='Product ID')),
                ('product_name', models.CharField(blank=True, max_length=255, null=True, verbose_name='Product Name')),
                ('price', models.CharField(blank=True, max_length=255, null=True, verbose_name='Price')),
                ('tax', models.CharField(blank=True, max_length=255, null=True, verbose_name='Tax')),
                ('dedecution_rate', models.CharField(blank=True, max_length=255, null=True, verbose_name='Deduction Rate')),
                ('artist', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='product_detail', to='master_data.mstpaymentrecipient')),
                ('distribution_pattern_group', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='product_detail', to='master_data.mstdistributionpatterngroup')),
                ('holding_company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='product_detail', to='master_data.mstcompany')),
                ('manager', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='product_detail', to=settings.AUTH_USER_MODEL)),
                ('segment', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='product_detail', to='master_data.mstsegment')),
            ],
            options={
                'verbose_name': 'Product Detail',
                'verbose_name_plural': 'Product Details',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SaleGoodDetail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('sale', models.CharField(blank=True, max_length=255, null=True, verbose_name='Sale')),
                ('quantity', models.CharField(blank=True, max_length=255, null=True, verbose_name='Quantity')),
                ('price', models.CharField(blank=True, max_length=255, null=True, verbose_name='Price')),
                ('product_detail', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='sale_good_detail', to='project.productdetail')),
                ('sale_route', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='sale_good_detail', to='master_data.mstsaleroute')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='SaleGood',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('filename', models.CharField(blank=True, max_length=255, null=True, verbose_name='File Name')),
                ('status', models.CharField(blank=True, choices=[('in_progress', 'In Progress'), ('done', 'Done'), ('error', 'Error')], max_length=255, null=True, verbose_name='Status')),
                ('project', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='sale_good', to='project.project')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='sale_good', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Sale Good',
                'verbose_name_plural': 'Sale Goods',
                'ordering': ['-created_at'],
            },
        ),
    ]
