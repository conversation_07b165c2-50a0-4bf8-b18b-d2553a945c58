# Generated by Django 4.2.13 on 2024-08-02 12:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('project', '0041_salegooddetail_report_type'),
    ]

    operations = [
        migrations.CreateModel(
            name='SaleReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('target_month', models.DateField(blank=True, null=True, verbose_name='Target Month')),
                ('file_status', models.CharField(blank=True, choices=[('in_progress', 'In Progress'), ('done', 'Done'), ('error', 'Error')], null=True, verbose_name='File Status')),
                ('report_file', models.FileField(blank=True, null=True, upload_to='sale_report/', verbose_name='Report File')),
            ],
            options={
                'verbose_name': 'Sale Report',
                'verbose_name_plural': 'Sale Reports',
                'ordering': ['-created_at'],
            },
        ),
    ]
