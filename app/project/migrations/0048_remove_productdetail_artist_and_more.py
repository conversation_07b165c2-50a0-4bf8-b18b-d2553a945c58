# Generated by Django 4.2.13 on 2024-08-23 08:47

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('goods', '0001_initial'),
        ('project', '0047_royaltyreport'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='productdetail',
            name='artist',
        ),
        migrations.RemoveField(
            model_name='productdetail',
            name='distribution_pattern_group',
        ),
        migrations.RemoveField(
            model_name='productdetail',
            name='holding_company',
        ),
        migrations.RemoveField(
            model_name='productdetail',
            name='media_title',
        ),
        migrations.RemoveField(
            model_name='productdetail',
            name='segment',
        ),
        migrations.RemoveField(
            model_name='productdetail',
            name='user',
        ),
        migrations.DeleteModel(
            name='RoyaltyReport',
        ),
        migrations.RemoveField(
            model_name='salegood',
            name='user',
        ),
        migrations.RemoveField(
            model_name='salegooddetail',
            name='product_detail',
        ),
        migrations.RemoveField(
            model_name='salegooddetail',
            name='sale_good',
        ),
        migrations.RemoveField(
            model_name='salegooddetail',
            name='sale_route',
        ),
        migrations.DeleteModel(
            name='SaleReport',
        ),
        migrations.AlterField(
            model_name='payslipreward',
            name='sale_good',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='payment_recipient_detail', to='goods.salegooddetail'),
        ),
        migrations.AlterField(
            model_name='rewarddistributorportion',
            name='sale_data',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='reward_distributor_portion', to='goods.salegooddetail'),
        ),
        migrations.DeleteModel(
            name='ProductDetail',
        ),
        migrations.DeleteModel(
            name='SaleGood',
        ),
        migrations.DeleteModel(
            name='SaleGoodDetail',
        ),
    ]
