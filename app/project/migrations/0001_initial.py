# Generated by Django 4.2.13 on 2024-06-01 06:40

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('master_data', '0014_mstdistributionpatterngroupdetail_pattern_group'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Project',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('project_id', models.CharField(blank=True, max_length=255, null=True, verbose_name='Project ID')),
                ('project_name', models.CharField(blank=True, max_length=255, null=True, verbose_name='Project Name')),
                ('temporary_sale', models.CharField(blank=True, max_length=255, null=True, verbose_name='Temporary Sale')),
                ('sale_aft_tax', models.CharField(blank=True, max_length=255, null=True, verbose_name='Sale After Tax')),
                ('cost_aft_tax', models.CharField(blank=True, max_length=255, null=True, verbose_name='Cost After Tax')),
                ('temp_profit_aft_tax', models.CharField(blank=True, max_length=255, null=True, verbose_name='Temporary Profit After Tax')),
                ('manager', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='project', to=settings.AUTH_USER_MODEL)),
                ('pattern', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='project', to='master_data.mstdistributionpattern')),
                ('segment', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='project', to='master_data.mstsegment')),
            ],
            options={
                'verbose_name': 'Project',
                'verbose_name_plural': 'Projects',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProjectIncome',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('income_code', models.CharField(blank=True, max_length=255, null=True, verbose_name='Income Code')),
                ('paid_date', models.DateField(blank=True, null=True, verbose_name='Paid Date')),
                ('paid_status', models.CharField(blank=True, max_length=255, null=True, verbose_name='Paid Status')),
                ('billing_title', models.CharField(blank=True, max_length=255, null=True, verbose_name='Billing Title')),
                ('price', models.CharField(blank=True, max_length=255, null=True, verbose_name='Price')),
                ('note', models.CharField(blank=True, max_length=255, null=True, verbose_name='Price')),
                ('billing_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='project_income', to=settings.AUTH_USER_MODEL)),
                ('project', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='project_income', to='project.project')),
            ],
            options={
                'verbose_name': 'Project Income',
                'verbose_name_plural': 'Project Incomes',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProjectCost',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('cost_code', models.CharField(blank=True, max_length=255, null=True, verbose_name='Cost Code')),
                ('receipt_title', models.CharField(blank=True, max_length=255, null=True, verbose_name='Billing Title')),
                ('price', models.CharField(blank=True, max_length=255, null=True, verbose_name='Price')),
                ('note', models.CharField(blank=True, max_length=255, null=True, verbose_name='Price')),
                ('paid_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='project_cost', to=settings.AUTH_USER_MODEL)),
                ('project', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='project_cost', to='project.project')),
            ],
            options={
                'verbose_name': 'Project Cost',
                'verbose_name_plural': 'Project Costs',
                'ordering': ['-created_at'],
            },
        ),
    ]
