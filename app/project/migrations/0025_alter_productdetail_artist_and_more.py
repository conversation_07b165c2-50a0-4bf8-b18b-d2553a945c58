# Generated by Django 4.2.13 on 2024-06-20 13:55

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('master_data', '0016_mstartist'),
        ('project', '0024_salegooddetail_target_month'),
    ]

    operations = [
        migrations.AlterField(
            model_name='productdetail',
            name='artist',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='product_detail', to='master_data.mstartist'),
        ),
        migrations.AlterField(
            model_name='salegooddetail',
            name='deduction',
            field=models.IntegerField(blank=True, max_length=255, null=True, verbose_name='Deduction'),
        ),
        migrations.AlterField(
            model_name='salegooddetail',
            name='price',
            field=models.IntegerField(blank=True, max_length=255, null=True, verbose_name='Price'),
        ),
        migrations.AlterField(
            model_name='salegooddetail',
            name='quantity',
            field=models.IntegerField(blank=True, max_length=255, null=True, verbose_name='Quantity'),
        ),
        migrations.AlterField(
            model_name='salegooddetail',
            name='sale',
            field=models.IntegerField(blank=True, max_length=255, null=True, verbose_name='Sale'),
        ),
    ]
