# Generated by Django 4.2.13 on 2024-09-10 00:08

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('master_data', '0021_alter_mediatitle_advance_royalty_and_more'),
        ('project', '0049_alter_payslipreward_sale_good'),
    ]

    operations = [
        migrations.CreateModel(
            name='RecipientRewardDistribution',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('income', models.CharField(blank=True, max_length=255, null=True, verbose_name='Income Amount')),
                ('expense', models.CharField(blank=True, max_length=255, null=True, verbose_name='Expense Amount')),
                ('reward', models.CharField(blank=True, max_length=255, null=True, verbose_name='Reward Amount')),
                ('project', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='recipient_reward_distribution', to='project.project')),
                ('recipient', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='recipient_reward_distribution', to='master_data.mstpaymentrecipient')),
            ],
            options={
                'verbose_name': 'Recipient Reward Distribution',
                'verbose_name_plural': 'Recipient Reward Distributions',
                'ordering': ['-created_at'],
            },
        ),
    ]
