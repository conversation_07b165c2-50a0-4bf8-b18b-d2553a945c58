# Generated by Django 4.2.13 on 2025-04-30 00:42

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('project', '0060_alter_project_overrided_pattern_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='project',
            name='need_recalculate_reward',
            field=models.BooleanField(default=False, verbose_name='Need Recalculate Reward'),
        ),
        migrations.AlterField(
            model_name='distributioncompany',
            name='accounting_percentage',
            field=models.FloatField(blank=True, default=100, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='Accounting Percentage'),
        ),
        migrations.AlterField(
            model_name='distributioncompany',
            name='distributor_percentage',
            field=models.FloatField(blank=True, default=0, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='Distribution Percentage'),
        ),
        migrations.AlterField(
            model_name='distributioncompany',
            name='third_recipient_percentage',
            field=models.FloatField(blank=True, default=0, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='3rd Recipient Percentage'),
        ),
    ]
