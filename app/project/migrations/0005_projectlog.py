# Generated by Django 4.2.13 on 2024-06-01 11:05

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('project', '0004_alter_projectcost_paid_to_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProjectLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('log_description', models.TextField(blank=True, null=True, verbose_name='Log Description')),
                ('project', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='project_log', to='project.project')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='project_log', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Project Log',
                'verbose_name_plural': 'Project Logs',
                'ordering': ['-created_at'],
            },
        ),
    ]
