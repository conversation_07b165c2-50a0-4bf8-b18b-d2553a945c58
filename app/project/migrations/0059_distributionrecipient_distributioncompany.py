# Generated by Django 4.2.13 on 2025-02-06 23:13

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('master_data', '0022_mstartist_artist_name_abbv'),
        ('project', '0058_alter_project_project_id_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='DistributionRecipient',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('percentage', models.FloatField(blank=True, default=0, null=True, verbose_name='Recipient Percentage')),
                ('project', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='distribution_recipient', to='project.project')),
                ('recipient', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='distribution_recipient', to='master_data.mstpaymentrecipient')),
            ],
            options={
                'verbose_name': 'Distribution Recipient',
                'verbose_name_plural': 'Distribution Recipients',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DistributionCompany',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('pattern_code', models.CharField(blank=True, max_length=100, null=True, verbose_name='Code')),
                ('accounting_percentage', models.FloatField(blank=True, default=100, null=True, verbose_name='Accounting Percentage')),
                ('distributor_percentage', models.FloatField(blank=True, default=0, null=True, verbose_name='Distribution Percentage')),
                ('third_recipient_percentage', models.FloatField(blank=True, default=0, null=True, verbose_name='3rd Recipient Percentage')),
                ('accounting_company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='acc_distribution_company', to='master_data.mstcompany')),
                ('distributor_company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='dist_distribution_company', to='master_data.mstcompany')),
                ('project', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='distribution_company', to='project.project')),
                ('third_recipient_company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='third_distribution_company', to='master_data.mstcompany')),
            ],
            options={
                'verbose_name': 'Distribution Company',
                'verbose_name_plural': 'Distribution Companies',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['is_delete', 'project'], name='project_dis_is_dele_bb30c7_idx')],
            },
        ),
    ]
