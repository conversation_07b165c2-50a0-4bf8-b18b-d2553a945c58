# Generated by Django 4.2.13 on 2024-07-09 11:07

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('master_data', '0017_alter_mstsaleroute_options_and_more'),
        ('project', '0034_alter_payslipreward_reward_source_type_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProjectDistributorPortion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('distributor_position', models.IntegerField(blank=True, null=True, verbose_name='Distributor Position')),
                ('amount_portion', models.CharField(blank=True, max_length=255, null=True, verbose_name='Amount Portion')),
                ('company', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='project_distributor_portion', to='master_data.mstcompany')),
                ('project', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='project_distributor_portion', to='project.project')),
            ],
            options={
                'verbose_name': 'Project Log',
                'verbose_name_plural': 'Project Logs',
                'ordering': ['-created_at'],
            },
        ),
    ]
