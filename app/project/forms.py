from datetime import datetime
from django import forms
from graphql_relay import to_global_id

from app.common import constants, utils
from app.master_data.models import MstDistributionPattern
from app.project import usecase
from app.project.models import DistributionCompany, DistributionRecipient, PaySlipReward, PaySlipStatement, PaySlipStatementReward, Project, ProjectCost, ProjectIncome, ProjectLog
from django.utils.translation import gettext as _
from django.db import transaction



class ProjectForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        for field in self.Meta.required:
            self.fields[field].required = True

    class Meta:
        model = Project
        fields = ('segment', 'project_name', 'pattern', 'manager', 'project_status', 'artist', 'issue_date')
        required = ('segment', 'project_name', 'pattern', 'manager', 'artist', 'issue_date')

    def clean(self):
        cleaned_data = super().clean()
        if not self.data.get('logged_user').is_authenticated:
            raise forms.ValidationError(_('You are not logged in.'))

        logged_user_id = self.data.get('logged_user').id
        is_admin = self.data.get('logged_user').groups_id == 1 or self.data.get('logged_user').groups_id == 2
        if cleaned_data.get('manager').pk != logged_user_id and not is_admin:
            raise forms.ValidationError({'manager': [_('You are not allowed to create project for other manager.'),]})

        if self.instance.pk and not is_admin:
            raise forms.ValidationError(_('You are not allowed to edit Project.'))

        if self.instance.pk and self.instance.is_delete:
            raise forms.ValidationError(_("Data already deleted"))

        if self.instance.pk and (self.instance.project_status == '5' or self.instance.project_paid_date):
            raise forms.ValidationError(_("Cannot edit closed project"))

        if 'manager' not in cleaned_data and not cleaned_data.get('manager'):
            raise forms.ValidationError({'manager': [_('Manager not found.'),]})

        # if Project.objects.filter(project_name=cleaned_data.get('project_name'), is_delete=False).exclude(pk=self.instance.pk).exists():
        #     raise forms.ValidationError({'project_name': [_('既に存在するプロジェクト名は登録できません'),]})

        if self.instance.pk and not is_admin and (cleaned_data.get('project_status') != '3' and self.instance.project_status == '2'):
            raise forms.ValidationError({'project_status': [_("Invalid status!"),]})

        if self.instance.pk and cleaned_data.get('project_status') == '5':
            raise forms.ValidationError({'project_status': [_("Invalid status!"),]})

        if cleaned_data.get('project_status') and not utils.valid_enum(cleaned_data.get('project_status'), Project.PROJECT_STATUS):
            raise forms.ValidationError({'project_status': [_("Invalid status!"),]})

        return cleaned_data

    def save(self, commit=True):
        model = super().save(commit=False)
        with transaction.atomic():

            if self.cleaned_data.get('project_status') == '5':
                model.project_paid_date = datetime.now()

            if not model.project_id:
                # total_project = Project.objects.get_total_data()
                last_project = Project.objects.latest('id')
                last_id = last_project.pk if last_project else 0
                project_prefix = 'PRJ'
                if self.cleaned_data.get('artist') and self.cleaned_data.get('artist').artist_name_abbv:
                    project_prefix = self.cleaned_data.get('artist').artist_name_abbv

                loop_product_id = 0
                while True:
                    try:
                        project_id = project_prefix + "{:04d}".format(last_id + 1)
                        if not Project.objects.filter(project_id=project_id).exists():
                            model.project_id = project_id
                            break

                        if loop_product_id > 10:
                            raise forms.ValidationError("An error occurred while generating the project id. Please try again.")

                        loop_product_id += 1
                        last_id += 1
                        continue
                    except Exception as e:
                        if loop_product_id > 10:
                            raise forms.ValidationError("An error occurred while generating the project id. Please try again.")

                        loop_product_id += 1
                        last_id += 1
                        continue
            if commit:
                model.save()

            if self.cleaned_data.get('id'):
                usecase.calculate_project_earning(model.pk)
                usecase.calculate_reward_distribution(model.pk)

            pattern_data = model.pattern
            distribution_pattern_snapshot = DistributionCompany.objects.filter(project=model)

            if pattern_data and not distribution_pattern_snapshot.exists():
                DistributionCompany.objects.update_or_create(
                    project=model,
                    pattern_code=pattern_data.pattern_code,
                    defaults={
                        'pattern_code': pattern_data.pattern_code,
                        'accounting_company': pattern_data.accounting_company,
                        'accounting_percentage': pattern_data.accounting_percentage,
                        'distributor_company': pattern_data.distributor_company,
                        'distributor_percentage': pattern_data.distributor_percentage,
                        'third_recipient_company': pattern_data.third_recipient_company,
                        'third_recipient_percentage': pattern_data.third_recipient_percentage,
                    }
                )

            recipient_data = MstDistributionPattern.objects.filter(pk=pattern_data.pk).first().pattern_detail

            if recipient_data.exists() and not distribution_pattern_snapshot.exists():
                for recipient in recipient_data.all():
                    DistributionRecipient.objects.update_or_create(
                        project=model,
                        recipient=recipient.recipient,
                        defaults={
                            'percentage': recipient.percentage,
                        }
                    )

            if self.cleaned_data.get('id'):
                ProjectLog.objects.save_log(model, self.data.get('logged_user'), 'Project updated')
            else:
                ProjectLog.objects.save_log(model, self.data.get('logged_user'), 'Project added')

        return model

class ProjectIncomeForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        for field in self.Meta.required:
            self.fields[field].required = True

    class Meta:
        model = ProjectIncome
        fields = ('project', 'issue_date', 'paid_date', 'paid_status', 'billing_title', 'billing_to', 'price', 'note')
        required = ['project', 'issue_date', 'billing_title', 'billing_to', 'price',]

    def clean(self):
        cleaned_data = super().clean()
        if not self.data.get('logged_user').is_authenticated:
            raise forms.ValidationError(_('You are not logged in.'))

        logged_user_id = self.data.get('logged_user').id
        is_admin = self.data.get('logged_user').groups_id == 1 or self.data.get('logged_user').groups_id == 2
        manager_id = cleaned_data.get('project').manager.pk
        if self.instance.pk and not is_admin:
            raise forms.ValidationError(_('You are not allowed to edit Project.'))

        if self.instance is None and not is_admin:
            raise forms.ValidationError(_('You are not allowed to add Project Cost.'))

        if manager_id != logged_user_id and not is_admin:
            raise forms.ValidationError(_('You are not allowed to add income of other project manager.'))

        if self.instance.pk and self.instance.is_delete:
            raise forms.ValidationError(_("Data already deleted"))

        if cleaned_data.get('project') and cleaned_data.get('project').project_status == '5':
            raise forms.ValidationError(_("Cannot add for closed project"))

        if self.instance.pk and self.instance.project.project_status == '5':
            raise forms.ValidationError(_("Cannot edit for closed project"))

        if self.instance.pk and 'billing_to' not in cleaned_data:
            raise forms.ValidationError({'billing_to': [_("Billing to is required."),]})

        if self.instance.pk and 'billing_title' not in cleaned_data:
            raise forms.ValidationError({'billing_title': [_("Billing title is required."),]})

        if self.instance.pk and 'price' not in cleaned_data:
            raise forms.ValidationError({'price': [_("Price is required."),]})

        if self.instance.pk and 'project' not in cleaned_data:
            raise forms.ValidationError({'project': [_("Project is required."),]})

        if 'issue_date' in cleaned_data and not cleaned_data.get('issue_date') and not utils.is_valid_date(cleaned_data.get('issue_date')):
            raise forms.ValidationError({'issue_date': [_('Issue Date is not valid.'),]})

        # if cleaned_data.get('paid_date') and cleaned_data.get('issue_date') and cleaned_data.get('paid_date') < cleaned_data.get('issue_date'):
        #     raise forms.ValidationError({'paid_date': [_('Paid Date must be later than the issue date.'),]})

        return cleaned_data

    def save(self, commit=True):
        model = super().save(commit=False)
        with transaction.atomic():
            if not model.income_code:
                total_income = ProjectIncome.objects.get_total_data()
                while True:
                    try:
                        income_code = "INC-" + str(total_income + 1)
                        if ProjectIncome.objects.filter(income_code=income_code).exists():
                            total_income += 1
                            continue
                        else:
                            model.income_code = income_code
                            break
                    except Exception as e:
                        total_income += 1
                        continue
            if commit:
                model.save()

            project = self.instance.project  # Assuming `self.instance` is a ProjectIncome instance
            usecase.calculate_project_earning(project.pk)
            usecase.calculate_reward_distribution(project.pk)

        if model.pk:
            ProjectLog.objects.save_log(model.project, self.data.get('logged_user'), 'Income updated')
        else:
            ProjectLog.objects.save_log(model.project, self.data.get('logged_user'), 'Income added')


        return model

class ProjectCostForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        for field in self.Meta.required:
            self.fields[field].required = True

    class Meta:
        model = ProjectCost
        fields = '__all__'
        exclude = ('cost_code',)
        required = ['project', 'issue_date', 'receipt_title', 'paid_to', 'price',]

    def clean(self):
        cleaned_data = super().clean()
        if not self.data.get('logged_user').is_authenticated:
            raise forms.ValidationError(_('You are not logged in.'))

        logged_user_id = self.data.get('logged_user').id
        is_admin = self.data.get('logged_user').groups_id == 1 or self.data.get('logged_user').groups_id == 2
        manager_id = cleaned_data.get('project').manager.pk
        if self.instance.pk and not is_admin:
            raise forms.ValidationError(_('You are not allowed to edit Project.'))

        if self.instance is None and not is_admin:
            raise forms.ValidationError(_('You are not allowed to add Project Cost.'))

        if manager_id != logged_user_id and not is_admin:
            raise forms.ValidationError({'manager': [_('You are not allowed to add cost of other project manager.'),]})

        if self.instance.pk and self.instance.is_delete:
            raise forms.ValidationError(_("Data already deleted"))

        if cleaned_data.get('project') and cleaned_data.get('project').project_status == '5':
            raise forms.ValidationError(_("Cannot add for closed project"))

        if self.instance.pk and self.instance.project.project_status == '5':
            raise forms.ValidationError(_("Cannot edit for closed project"))

        if cleaned_data.get('tax_category') and not utils.valid_enum(cleaned_data.get('tax_category'), ProjectCost.TAX_CATEGORY):
            raise forms.ValidationError({'tax_category': [_("Invalid tax category!"),]})

        return cleaned_data

    def save(self, commit=True):
        model = super().save(commit=False)
        with transaction.atomic():
            if not model.cost_code:
                total_cost = ProjectCost.objects.get_total_data()
                while True:
                    try:
                        cost_code = "CST" + "{:04d}".format(total_cost + 1)
                        if ProjectCost.objects.filter(cost_code=cost_code).exists():
                            total_cost += 1
                            continue
                        else:
                            model.cost_code = cost_code
                            break
                    except Exception as e:
                        total_cost += 1
                        continue
            if commit:
                model.save()

            project = self.instance.project  # Assuming `self.instance` is a ProjectIncome instance
            usecase.calculate_project_earning(project.pk)
            usecase.calculate_reward_distribution(project.pk)

        if model.pk:
            ProjectLog.objects.save_log(model.project, self.data.get('logged_user'), 'Cost updated')
        else:
            ProjectLog.objects.save_log(model.project, self.data.get('logged_user'), 'Cost added')


        return model

class PaySlipRewardForm(forms.ModelForm):

    class Meta:
        model = PaySlipReward
        fields = ("note",)


class PaySlipStatementForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        for field in self.Meta.required:
            self.fields[field].required = True

    other_rewards = forms.JSONField(required=False)
    pay_slip_ids = utils.CommaSeparatedField(required=False)

    class Meta:
        model = PaySlipStatement
        fields = ('recipient', 'title', 'period', 'withdraw_date', 'pay_method', 'total_payment')
        required = ['recipient', 'title', 'period', 'withdraw_date', 'pay_method', 'total_payment', 'pay_slip_ids']

    def clean(self):
        cleaned_data = super().clean()
        if not self.data.get('logged_user').is_authenticated:
            raise forms.ValidationError(_('You are not logged in.'))

        if 'other_rewards' in cleaned_data and cleaned_data.get('other_rewards'):
            is_id_exist = utils.check_key_in_objects('item', cleaned_data.get('other_rewards'))
            is_percentage_exist = utils.check_key_in_objects('money', cleaned_data.get('other_rewards'))
            is_deduction_items_exist = utils.check_value_in_objects(constants.DEDUCTION_ITEMS , cleaned_data.get('other_rewards'))
            is_consumption_tax_exist = utils.check_value_in_objects(constants.CONSUMPTION_TAX , cleaned_data.get('other_rewards'))
            if not is_id_exist:
                raise forms.ValidationError({'other_rewards': [_('Item is required.'),]})
            if not is_percentage_exist:
                raise forms.ValidationError({'other_rewards': [_('Money is required.'),]})
            if not is_deduction_items_exist:
                raise forms.ValidationError({'other_rewards': [_('Invalid deduction item.'),]})
            if not is_consumption_tax_exist:
                raise forms.ValidationError({'other_rewards': [_('Invalid consumption tax.'),]})

            for data in cleaned_data.get('other_rewards'):
                if not utils.is_number(data.get('money')):
                    raise forms.ValidationError({'other_rewards': [_(f"{data['item']} money value is not numeric"),]})

        if 'pay_slip_ids' in cleaned_data and cleaned_data['pay_slip_ids']:
            selected_ids = [str(item) for item in PaySlipReward.objects.filter(pk__in=cleaned_data['pay_slip_ids'], is_paid=False).values_list('id', flat=True)]
            not_in_selected_ids = [item for item in cleaned_data['pay_slip_ids'] if item not in selected_ids]
            if not_in_selected_ids:
                invalid_ids = ', '.join([to_global_id('PaySlipRewardNode', id) for id in not_in_selected_ids])
                raise forms.ValidationError({'pay_slip_ids': [_(f'Pay slip {invalid_ids} not found.'),]})
            # for pay_slip_id in cleaned_data['pay_slip_ids']:
            #     pay_slip = PaySlipReward.objects.filter(pk=pay_slip_id, is_paid=False).first()
            #     if not pay_slip:
            #         raise forms.ValidationError({'pay_slip_ids': [_(f'Pay slip {to_global_id(pay_slip_id)} not found.'),]})

        if 'total_payment' in cleaned_data:
            if cleaned_data.get('total_payment') and not utils.is_number(cleaned_data.get('total_payment')):
                raise forms.ValidationError({'total_payment': [_('Total Payment is not numeric.'),]})

        if 'withdraw_date' in cleaned_data and not cleaned_data['withdraw_date'] and not utils.is_valid_date(cleaned_data.get('withdraw_date')):
            raise forms.ValidationError({'withdraw_date': [_('Withdraw Date is not valid.'),]})

        return cleaned_data

    def save(self, commit=True):
        model = super().save(commit=False)
        with transaction.atomic():
            pay_slip_ids = self.cleaned_data.get('pay_slip_ids')
            if commit:
                model.save()

            if 'total_payment' in self.cleaned_data and self.cleaned_data['total_payment']:
                if int(self.cleaned_data['total_payment']) < 0:
                    PaySlipReward.objects.create(
                        reward_source_type=3,
                        reward_source_id=model.pk,
                        recipient=model.recipient,
                        pay_date=datetime.now(),
                        billing_title="前期繰越分",
                        amount=utils.rounddown(self.cleaned_data['total_payment']),
                        # note='前期繰越分',
                        is_paid=False
                    )


            if 'other_rewards' in self.cleaned_data and self.cleaned_data.get('other_rewards'):
                other_reward_list = self.cleaned_data.get('other_rewards')
                for data in other_reward_list:
                    PaySlipStatementReward.objects.update_or_create(
                        statement=model,
                        item=data['item'],
                        defaults={
                            'amount': utils.rounddown(data['money']),
                            'statement_type': '2',
                        }
                    )

            if pay_slip_ids:
                payslip = PaySlipReward.objects.filter(pk__in=pay_slip_ids)
                for pay in payslip:
                    segment_name = "Non segment"
                    if 1 == pay.reward_source_type:
                        segment_name = pay.project.segment.segment_name
                    if 'ライブ' in segment_name or 'live' in segment_name.lower():
                        PaySlipStatementReward.objects.update_or_create(
                            statement=model,
                            item=f"{pay.project.project_name}",
                            defaults={
                                'amount': utils.rounddown(pay.amount),
                                'statement_type': '1',
                                'reward_type': pay.reward_source_type,
                                'reward_source_id': pay.reward_source_id,
                            }
                        )
                    elif 2 == pay.reward_source_type:
                        PaySlipStatementReward.objects.update_or_create(
                            statement=model,
                            item=f"{pay.sale_good.product_detail.product_name}",
                            defaults={
                                'amount': utils.rounddown(pay.amount),
                                'statement_type': '2',
                                'reward_type': pay.reward_source_type,
                                'reward_source_id': pay.reward_source_id,
                            }
                        )
                    elif 3 == pay.reward_source_type:
                        PaySlipStatementReward.objects.update_or_create(
                            statement=model,
                            item=f"{pay.billing_title}",
                            defaults={
                                'amount': utils.rounddown(pay.amount),
                                'statement_type': '2',
                                'reward_type': pay.reward_source_type,
                                'reward_source_id': pay.reward_source_id,
                            }
                        )
                payslip.update(is_paid=True)
            model.payslip_id_list = ','.join(pay_slip_ids) if pay_slip_ids else None
            model.save()
        return model

class DistributionCompanyMappingForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        for field in self.Meta.required:
            self.fields[field].required = True

    class Meta:
        model = DistributionCompany
        fields = ('accounting_percentage', 'distributor_percentage', 'third_recipient_percentage')
        required = ['accounting_percentage', 'distributor_percentage', 'third_recipient_percentage']

    def save(self, commit = True):
        model = super().save(commit)
        Project.objects.filter(pk=model.project.pk).update(need_recalculate_reward=True)
        ProjectLog.objects.save_log(model.project, self.data.get('logged_user'), 'Distribution Company updated')

        return model

class DistributionRecipientMappingForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        for field in self.Meta.required:
            self.fields[field].required = True

    class Meta:
        model = DistributionRecipient
        fields = ('recipient', 'percentage')
        required = ['recipient', 'percentage']

    def save(self, commit = True):
        model = super().save(commit)
        Project.objects.filter(pk=model.project.pk).update(need_recalculate_reward=True)
        ProjectLog.objects.save_log(model.project, self.data.get('logged_user'), 'Distribution Recipient updated')

        return model

