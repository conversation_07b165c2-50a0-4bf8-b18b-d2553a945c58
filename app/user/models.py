from django.db import models
from django.contrib.auth.models import AbstractUser, Group
from django.utils.translation import gettext as _
from django.contrib.auth.models import BaseUserManager
from django.contrib.auth.models import UserManager as DefaultUserManager


# Create your models here.
class UserManager(DefaultUserManager):
    def get_queryset(self):
        return super(UserManager, self).get_queryset().filter(is_delete=False)
    
    def create_superuser(self, username, email=None, password=None, **extra_fields):
        return super().create_superuser(username, email, password, **extra_fields)

class User(AbstractUser):
    full_name = models.CharField(_("full name"), max_length=150, blank=True)
    groups = models.ForeignKey(Group, blank=True, null=True, on_delete=models.CASCADE)
    email = models.EmailField(_('email address'), unique=True)
    is_delete = models.BooleanField(default=False)

    objects = UserManager()

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['groups_id', 'username']

    class Meta:
        verbose_name = _('User')
        verbose_name_plural = _('Users')
        ordering = ['email']

    def is_manager(self):
        if not self.groups:
            return False
        return self.groups.name == 'manager'

    def is_admin(self):
        if not self.groups:
            return False
        return self.groups.name == 'admin' or self.groups.name == 'super_admin'

    def is_eligible_manager(self, manager_id):
        if not self.pk:
            return False
        return self.pk == manager_id

    def get_full_name(self):
        return '%s %s' % (self.first_name, self.last_name)

    def get_short_name(self):
        return self.first_name

    def __str__(self):
        return self.username
