from django.contrib import admin
from django.contrib.auth.admin import UserAdmin

from app.user.models import User

# Register your models here.
@admin.register(User)
class CustomUserAdmin(UserAdmin):
    list_display = ('username', 'email', 'get_role')
    list_per_page = 50
    filter_horizontal = ()

    @admin.display(ordering='groups__name', description='User role')
    def get_role(self, obj):
        if hasattr(obj, 'groups') and obj.groups:
            return obj.groups.name
        return 'No Role'
