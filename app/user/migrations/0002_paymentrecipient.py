# Generated by Django 4.2.13 on 2024-05-19 01:55

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentRecipient',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('recipient_name', models.CharField(blank=True, max_length=255, null=True)),
                ('billing_name', models.CharField(blank=True, max_length=255, null=True)),
                ('registered_number', models.CharField(blank=True, max_length=255, null=True)),
                ('registered_date', models.DateField(blank=True, null=True)),
                ('user', models.ManyToManyField(blank=True, null=True, related_name='payment_recipient', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'Payment Recipient',
                'verbose_name_plural': 'Payment Recipients',
            },
        ),
    ]
