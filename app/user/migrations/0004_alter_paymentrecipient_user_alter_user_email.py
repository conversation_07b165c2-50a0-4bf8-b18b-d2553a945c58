# Generated by Django 4.2.13 on 2024-05-19 05:58

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0003_alter_user_options'),
    ]

    operations = [
        migrations.AlterField(
            model_name='paymentrecipient',
            name='user',
            field=models.ManyToManyField(blank=True, related_name='payment_recipient', to=settings.AUTH_USER_MODEL, verbose_name='User'),
        ),
        migrations.AlterField(
            model_name='user',
            name='email',
            field=models.EmailField(max_length=254, unique=True, verbose_name='email address'),
        ),
    ]
