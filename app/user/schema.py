import random
import string
from django.db import IntegrityError
import graphene
from graphene_django import DjangoConnectionField, DjangoObjectType
from graphql import GraphQLError
import graphql_jwt
from graphene import relay
from graphql_relay import from_global_id
from graphql_jwt.decorators import login_required
from graphene_django.forms.mutation import DjangoModelFormMutation

from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group
from django.core.exceptions import ValidationError
from django.core.validators import validate_email
from django.contrib.auth.password_validation import validate_password


from app.common.utils import Responser
from app.master_data.models import MstPaymentRecipient as PaymentRecipient
from app.user.forms import RegisterForm


class PaymentRecipientNode(DjangoObjectType):
    pk = graphene.Int(source='pk')

    class Meta:
        model = PaymentRecipient
        fields = "__all__"
        filter_fields = {
            'registered_number': ['exact'],
            'recipient_name': ['exact', 'icontains'],
        }
        interfaces = (graphene.relay.Node, )

class UserNode(DjangoObjectType):
    pk = graphene.Int(source='pk')
    is_accounting_staff = graphene.Boolean()
    first_name = graphene.String()
    last_name = graphene.String()

    class Meta:
        model = get_user_model()
        fields = "__all__"
        exclude_fields = [
            'username','is_staff','is_active','is_superuser','last_login',]
        filter_fields = {
            'email': ['exact', 'icontains'],
        }
        interfaces = (graphene.relay.Node, )

    def resolve_is_accounting_staff(self, info):
        if not self.groups:
            return False
        return self.groups.name == 'admin'

    def resolve_first_name(self, info):
        return self.first_name

    def resolve_last_name(self, info):
        return self.last_name


class ManagerPermissionNode(DjangoObjectType):
    recipient_list = DjangoConnectionField(PaymentRecipientNode)

    class Meta:
        model = get_user_model()
        fields = "__all__"
        filter_fields = {
            'username': ['exact', 'icontains'],
            'email': ['exact', 'icontains'],
        }
        interfaces = (graphene.relay.Node, )

    def resolve_recipient_list(self, info, **kwargs):
        return self.payment_recipient


class Query(graphene.ObjectType):
    user_info = graphene.Field(UserNode)
    manager_permission = graphene.Field(ManagerPermissionNode, manager_id=graphene.ID(required=True))
    all_manager = DjangoConnectionField(UserNode, manager_id=graphene.ID())
    available_permission = DjangoConnectionField(PaymentRecipientNode)

    @login_required
    def resolve_user_info(self, info, **kwargs):
        return info.context.user

    @login_required
    def resolve_all_manager(self, info, **kwargs):
        if 'manager_id' in kwargs:
            return get_user_model().objects.filter(pk=kwargs.get('manager_id'))
        return get_user_model().objects.filter(groups__name='manager').order_by('-date_joined')

    @login_required
    def resolve_manager_permission(self, info, **kwargs):
        if not info.context.user.is_admin():
            raise GraphQLError("You are not allowed to access this data")

        manager_id = kwargs.get("manager_id")
        user_id=from_global_id(manager_id)[1]
        return get_user_model().objects.filter(pk=user_id).first()

    @login_required
    def resolve_available_permission(self, info, **kwargs):
        if not info.context.user.is_admin():
            raise GraphQLError("You are not allowed to access this data")

        return PaymentRecipient.objects.exclude(user=info.context.user).order_by('-created_at')

class RegisterPaymentRecipientMutation(relay.ClientIDMutation):
    class Input:
        manager_id = graphene.ID(required=True)
        payment_recipient_list = graphene.List(graphene.ID)

    manager_permission = graphene.Field(ManagerPermissionNode)

    @login_required
    def mutate_and_get_payload(root, info, **input):
        if not info.context.user.is_admin():
            raise GraphQLError("You are not allowed to access this data")

        user = get_user_model().objects.filter(pk=from_global_id(input.get('manager_id'))[1]).first()
        if not user:
            raise GraphQLError("Manager not found")
        if not input.get('payment_recipient_list'):
            user.payment_recipient.clear()
            user.save()
            return RegisterPaymentRecipientMutation(manager_permission=user)

        recipient = PaymentRecipient.objects.filter(pk__in=[from_global_id(i)[1] for i in input.get('payment_recipient_list')])
        if not recipient:
            user.payment_recipient.clear()
        else:
            user.payment_recipient.set(recipient, clear=True)
        user.save()
        return RegisterPaymentRecipientMutation(manager_permission=user)

# class RegisterManagerMutation(DjangoModelFormMutation):
#     class Meta:
#         form_class = RegisterForm
#         return_field_name = 'manager'

#     manager = graphene.Field(UserNode)

#     @classmethod
#     @login_required
#     def perform_mutate(cls, form, info):
#         if not info.context.user.is_admin():
#             raise GraphQLError("You are not allowed to access this data")
#         return super().perform_mutate(form, info)

class RegisterManagerMutation(relay.ClientIDMutation):
    class Input:
        fullname = graphene.String(required=True)
        email = graphene.String(required=True)
        password = graphene.String(required=True)

    user = graphene.Field(UserNode)

    @login_required
    def mutate_and_get_payload(root, info, **input):
        if not info.context.user.is_admin():
            raise GraphQLError("You are not allowed to access this data")

        group = Group.objects.get(name='manager')
        if not input.get('fullname'):
            raise GraphQLError("Fullname must be filled")

        try:
            validate_email(input.get('email'))
        except ValidationError as e:
            raise GraphQLError("Invalid email format")

        if get_user_model().objects.filter(email=input.get('email')).exists():
            raise GraphQLError("Email already exists")

        user = get_user_model()(
            full_name=input.get('fullname'),
            email=input.get('email'),
            groups=group
        )

        try:
            validate_password(input.get('password'), user)
        except ValidationError as e:
            raise GraphQLError("Invalid password format")

        user.username = input.get('email').split("@", 1)[0].join(random.choices(string.ascii_uppercase + string.digits, k=5))

        user.set_password(input.get('password'))
        try:
            user.save()
        except IntegrityError as e:
            raise GraphQLError("Email already exists")
        return RegisterManagerMutation(user=user)

class DeleteManagerMutation(graphene.Mutation):
    class Arguments:
        manager_id = graphene.ID(required=True)

    Output = Responser

    @login_required
    def mutate(root, info, manager_id):
        if not info.context.user.is_admin():
            raise GraphQLError("You are not allowed to access this data")

        user = get_user_model().objects.filter(pk=from_global_id(manager_id)[1]).first()
        if not user:
            return Responser(result='User not found')
        user.is_delete = True
        user.is_active = False
        user.save()
        return Responser(result='Success')

class Mutation(graphene.ObjectType):
    token_auth = graphql_jwt.ObtainJSONWebToken.Field()
    verify_token = graphql_jwt.Verify.Field()
    refresh_token = graphql_jwt.Refresh.Field()
    register_manager = RegisterManagerMutation.Field()
    delete_manager = DeleteManagerMutation.Field()
    register_manager_permission = RegisterPaymentRecipientMutation.Field()
    delete_token_cookie = graphql_jwt.DeleteJSONWebTokenCookie.Field()

    # Long running refresh tokens
    delete_refresh_token_cookie = graphql_jwt.DeleteRefreshTokenCookie.Field()