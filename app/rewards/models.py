from django.db import models
from django.utils.translation import gettext_lazy as _

from app.common.utils import DeletedManager, TimeStampedModel
from app.goods.models import SaleGoodDetail
from app.master_data.models import ArtistCategoryGroup, MstDistributionPattern, MstDistributionPatternDetail, ProductCategoryGroup

# Create your models here.
class RewardDistributionMapping(TimeStampedModel):
    PAYEE_TYPE_CHOICES = (
        (1, 'Company'),
        (2, 'Recipient'),
    )
    MAP_REWARD_LAYER = (
        (1, 'Accounting to Accounting'),
        (2, 'Accounting to Distributor'),
        (3, 'Distributor to Third level'),
        (4, 'Third level to Recipient'),
    )
    parent_id = models.BigIntegerField(_("Parent ID"), blank=True, null=True)
    target_month = models.DateField(_("Target Month"), blank=True, null=True)
    product_category_group = models.ForeignKey(
        ProductCategoryGroup,
        on_delete=models.PROTECT,
        related_name="reward_distribution_mappings",
        verbose_name=_("Product Category Group"),
        db_constraint=False, blank=True, null=True,
        error_messages={
            "protected": _("This product category group is protected and cannot be deleted.")
        }
    )
    artist_group = models.ForeignKey(
        ArtistCategoryGroup,
        on_delete=models.PROTECT,
        related_name="reward_distribution_mappings",
        verbose_name=_("Artist Group"),
        db_constraint=False, blank=True, null=True,
        error_messages={
            "protected": _("This artist group is protected and cannot be deleted.")
        }
    )
    source_dist_pattern = models.ForeignKey(
        MstDistributionPattern,
        on_delete=models.DO_NOTHING,
        related_name="reward_distribution_mappings",
        verbose_name=_("Source Distribution Pattern"),
        db_constraint=False, blank=True, null=True
    )
    source_dist_pattern_detail = models.ForeignKey(
        MstDistributionPatternDetail,
        on_delete=models.DO_NOTHING,
        related_name="reward_distribution_mappings",
        verbose_name=_("Source Distribution Pattern Detail"),
        db_constraint=False, blank=True, null=True
    )
    source_sale_data = models.ForeignKey(
        SaleGoodDetail,
        on_delete=models.DO_NOTHING,
        related_name="reward_distribution_mappings",
        verbose_name=_("Source Sale Data"),
        db_constraint=False, blank=True, null=True
    )
    accounting_company_id = models.BigIntegerField(_("Accounting Company ID"), blank=True, null=True)
    accounting_company_name = models.CharField(_("Accounting Company Name"), max_length=100, blank=True, null=True)
    distributor_company_id = models.BigIntegerField(_("Distributor Company ID"), blank=True, null=True)
    distributor_company_name = models.CharField(_("Distributor Company Name"), max_length=100, blank=True, null=True)
    third_level_company_id = models.BigIntegerField(_("Third Level Company ID"), blank=True, null=True)
    third_level_company_name = models.CharField(_("Third Level Company Name"), max_length=100, blank=True, null=True)
    source_payer_id = models.BigIntegerField(_("Source Payer ID"), blank=True, null=True)
    source_payee_id = models.BigIntegerField(_("Source Payee ID"), blank=True, null=True)
    mapping_reward_layer = models.IntegerField(_("Mapping Reward Layer"), choices=MAP_REWARD_LAYER, blank=True, null=True)
    payee_type = models.IntegerField(_("Payee Type"), choices=PAYEE_TYPE_CHOICES, blank=True, null=True)
    payer_name = models.CharField(_("Payer Name"), max_length=100, blank=True, null=True)
    payee_name = models.CharField(_("Payee Name"), max_length=100, blank=True, null=True)
    distributed_price = models.BigIntegerField(_("Distributed Price"), blank=True, null=True, default=0)
    net_profit = models.BigIntegerField(_("Net Profit"), blank=True, null=True, default=0)

    objects = DeletedManager()

    def __str__(self):
        return f"{self.product_category_group} - {self.artist_group}"

    class Meta:
        verbose_name = _("Reward Distribution Mapping")
        verbose_name_plural = _("Reward Distribution Mappings")
        ordering = ["-created_at"]