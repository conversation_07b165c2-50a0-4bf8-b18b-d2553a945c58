from django.conf import settings
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render

from app.common import utils
from app.rewards.usecase import export_sale_reward_mapping

# Create your views here.
def export_sale_reward(request):
    if not settings.DEBUG:
        access_token = request.GET.get('accessToken')
        user = utils.get_auth_user(access_token)
        if not user and not user.is_admin():
            return JsonResponse({'error': 'Unauthorized'}, status=401)

    target_month = request.GET.get('target_month')
    if not target_month:
        return JsonResponse({'error': 'Target month is required'}, status=400)

    target_month = utils.convert_to_date(target_month)
    buffer = export_sale_reward_mapping(target_month)

    response = HttpResponse(
        buffer.getvalue(),
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

    return response