from datetime import datetime
from io import BytesIO
import logging

from django.core.files.base import ContentFile
import pandas as pd
from app.common import usecase, utils
from app.rewards.models import RewardDistributionMapping

logger = logging.getLogger(__name__)

def export_sale_reward_mapping(target_month: datetime):
    """
    Export Sale Reward Mapping
    """
    logger.info(f"Exporting sale reward mapping for {target_month}")
    data_dict_of_reward_map = {
        "対象月": [],
        "商品カテゴリグループ": [],
        "アーティストグループ": [],
        "債務会社": [],
        "債権会社": [],
        "分配金額（税込）": [],
        "取り分金額（税込）": [],
        "layer": [],
    }

    distribution_mapping = RewardDistributionMapping.objects.filter(target_month__month=target_month.month, target_month__year=target_month.year) \
        .filter(mapping_reward_layer=1) \
        .order_by('artist_group__id', 'product_category_group__id')

    logger.info(f"Found {distribution_mapping.count()} mappings for {target_month}")

    for mapping in distribution_mapping.all():
        data_dict_of_reward_map["対象月"].append(utils.convert_to_str(mapping.target_month, "%Y-%m"))
        if mapping.product_category_group:
            data_dict_of_reward_map["商品カテゴリグループ"].append(mapping.product_category_group.name)
        else:
            data_dict_of_reward_map["商品カテゴリグループ"].append("")
        if mapping.artist_group:
            data_dict_of_reward_map["アーティストグループ"].append(mapping.artist_group.name)
        else:
            data_dict_of_reward_map["アーティストグループ"].append("")

        data_dict_of_reward_map["債務会社"].append(mapping.payer_name)
        data_dict_of_reward_map["債権会社"].append(mapping.payee_name)
        distributed_price = mapping.distributed_price if mapping.distributed_price else 0
        data_dict_of_reward_map["分配金額（税込）"].append(utils.format_currency_value(distributed_price))
        net_profit = mapping.net_profit if mapping.net_profit else 0
        data_dict_of_reward_map["取り分金額（税込）"].append(utils.format_currency_value(net_profit))
        data_dict_of_reward_map["layer"].append(mapping.mapping_reward_layer)

        distribution_mapping_child = RewardDistributionMapping.objects.filter(parent_id=mapping.pk) \
            .filter(mapping_reward_layer__in=[2,3,4]) \
            .order_by('mapping_reward_layer')

        for mapping_child in distribution_mapping_child.all():
            data_dict_of_reward_map["対象月"].append(utils.convert_to_str(mapping_child.target_month, "%Y-%m"))
            if mapping_child.product_category_group:
                data_dict_of_reward_map["商品カテゴリグループ"].append(mapping_child.product_category_group.name)
            else:
                data_dict_of_reward_map["商品カテゴリグループ"].append("")
            if mapping_child.artist_group:
                data_dict_of_reward_map["アーティストグループ"].append(mapping_child.artist_group.name)
            else:
                data_dict_of_reward_map["アーティストグループ"].append("")

            data_dict_of_reward_map["債務会社"].append(mapping_child.payer_name)
            data_dict_of_reward_map["債権会社"].append(mapping_child.payee_name)
            distributed_price = mapping_child.distributed_price if mapping_child.distributed_price else 0
            data_dict_of_reward_map["分配金額（税込）"].append(utils.format_currency_value(distributed_price))
            net_profit = mapping_child.net_profit if mapping_child.net_profit else 0
            data_dict_of_reward_map["取り分金額（税込）"].append(utils.format_currency_value(net_profit))
            data_dict_of_reward_map["layer"].append(mapping_child.mapping_reward_layer)

    df_from_dict = pd.DataFrame(data_dict_of_reward_map)
    buffer = BytesIO()
    writer = pd.ExcelWriter(buffer, engine='xlsxwriter', engine_kwargs={'options': {'strings_to_numbers': True}})
    df_from_dict.T.reset_index().T.to_excel(writer, header=False, index=False)
    writer.close()

    return buffer

def send_reward_distribution_report(target_month: datetime, email):
    """
    Send Reward Distribution Report
    """
    logger.info(f"Sending reward distribution report for {target_month} to {email}")
    buffer = export_sale_reward_mapping(target_month)
    # Send the buffer to the recipient

    recipient_email = [email]
    if isinstance(email, list):
        recipient_email = email

    if recipient_email:
        usecase.send_email_with_attachment(
            to=recipient_email,
            subject=f"【分配計算システム | 分配金集計データ】",
            message=f"分配金集計データのExcelエクスポートが完了しました。\n添付のExcelファイルをご確認ください。",
            attachment=ContentFile(buffer.getvalue(), f"aggregated_distribution_data_{target_month.strftime('%Y-%m')}.xlsx"),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

