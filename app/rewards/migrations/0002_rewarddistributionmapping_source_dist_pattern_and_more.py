# Generated by Django 4.2.13 on 2025-03-01 07:40

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('master_data', '0023_artistcategorygroup_productcategorygroup_and_more'),
        ('goods', '0034_salegooddetail_original_sale'),
        ('rewards', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='rewarddistributionmapping',
            name='source_dist_pattern',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='reward_distribution_mappings', to='master_data.mstdistributionpattern', verbose_name='Source Distribution Pattern'),
        ),
        migrations.AddField(
            model_name='rewarddistributionmapping',
            name='source_dist_pattern_detail',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='reward_distribution_mappings', to='master_data.mstdistributionpatterndetail', verbose_name='Source Distribution Pattern Detail'),
        ),
        migrations.AddField(
            model_name='rewarddistributionmapping',
            name='source_sale_data',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='reward_distribution_mappings', to='goods.salegooddetail', verbose_name='Source Sale Data'),
        ),
    ]
