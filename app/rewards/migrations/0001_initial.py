# Generated by Django 4.2.13 on 2025-02-18 23:21

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('master_data', '0023_artistcategorygroup_productcategorygroup_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='RewardDistributionMapping',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('target_month', models.DateField(blank=True, null=True, verbose_name='Target Month')),
                ('source_payer_id', models.BigIntegerField(blank=True, null=True, verbose_name='Source Payer ID')),
                ('source_payee_id', models.BigIntegerField(blank=True, null=True, verbose_name='Source Payee ID')),
                ('payee_type', models.IntegerField(blank=True, choices=[(1, 'Company'), (2, 'REcipient')], null=True, verbose_name='Payee Type')),
                ('payer_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='Payer Name')),
                ('payee_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='Payee Name')),
                ('distributed_price', models.BigIntegerField(blank=True, default=0, null=True, verbose_name='Distributed Price')),
                ('net_profit', models.BigIntegerField(blank=True, default=0, null=True, verbose_name='Net Profit')),
                ('artist_group', models.ForeignKey(blank=True, db_constraint=False, error_messages={'protected': 'This artist group is protected and cannot be deleted.'}, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='reward_distribution_mappings', to='master_data.artistcategorygroup', verbose_name='Artist Group')),
                ('product_category_group', models.ForeignKey(blank=True, db_constraint=False, error_messages={'protected': 'This product category group is protected and cannot be deleted.'}, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='reward_distribution_mappings', to='master_data.productcategorygroup', verbose_name='Product Category Group')),
            ],
            options={
                'verbose_name': 'Reward Distribution Mapping',
                'verbose_name_plural': 'Reward Distribution Mappings',
                'ordering': ['-created_at'],
            },
        ),
    ]
