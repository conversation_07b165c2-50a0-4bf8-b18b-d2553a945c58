# Generated by Django 4.2.13 on 2025-05-08 01:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('rewards', '0004_alter_rewarddistributionmapping_mapping_reward_layer'),
    ]

    operations = [
        migrations.AddField(
            model_name='rewarddistributionmapping',
            name='accounting_company_id',
            field=models.BigIntegerField(blank=True, null=True, verbose_name='Accounting Company ID'),
        ),
        migrations.AddField(
            model_name='rewarddistributionmapping',
            name='accounting_company_name',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Accounting Company Name'),
        ),
        migrations.AddField(
            model_name='rewarddistributionmapping',
            name='distributor_company_id',
            field=models.BigIntegerField(blank=True, null=True, verbose_name='Distributor Company ID'),
        ),
        migrations.AddField(
            model_name='rewarddistributionmapping',
            name='distributor_company_name',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Distributor Company Name'),
        ),
        migrations.AddField(
            model_name='rewarddistributionmapping',
            name='third_level_company_id',
            field=models.BigIntegerField(blank=True, null=True, verbose_name='Third Level Company ID'),
        ),
        migrations.AddField(
            model_name='rewarddistributionmapping',
            name='third_level_company_name',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Third Level Company Name'),
        ),
        migrations.AlterField(
            model_name='rewarddistributionmapping',
            name='payee_type',
            field=models.IntegerField(blank=True, choices=[(1, 'Company'), (2, 'Recipient')], null=True, verbose_name='Payee Type'),
        ),
    ]
