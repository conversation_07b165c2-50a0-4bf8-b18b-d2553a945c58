import logging
from django.http import HttpResponse
import graphene
from app.rewards.usecase import send_reward_distribution_report
from app.user.models import User
from django_q.tasks import async_task


logger = logging.getLogger(__name__)

class ExportSaleRewardMapping(graphene.Mutation):
    class Arguments:
        target_month = graphene.Date(required=True)

    success = graphene.Boolean()

    def mutate(self, info, target_month):
        try:
            # Get buffer from usecase
            user_email = list(User.objects.filter(groups__name__in=['super_admin', 'admin']).values_list('email', flat=True))
            async_task(send_reward_distribution_report, target_month, user_email)
            # send_reward_distribution_report(target_month, user_email)

            return ExportSaleRewardMapping(success=True)
        except Exception as e:
            logger.error(f"Error exporting sale reward mapping: {e}")
            return ExportSaleRewardMapping(success=False)