
import django_filters
import graphene
from graphene_django import DjangoObjectType
from app.rewards.models import RewardDistributionMapping

class RewardDistributionMappingNode(DjangoObjectType):
    payee_type = graphene.String()

    class Meta:
        model = RewardDistributionMapping
        filter_fields = {
            'target_month': ['exact', 'icontains'],
        }
        interfaces = (graphene.relay.Node, )

    def resolve_payee_type(self, info):
        if self.payee_type == 1:
            return 'Company'
        elif self.payee_type == 2:
            return 'Recipient'
        else:
            return 'Undefined'