import graphene
from graphene_django.filter import DjangoFilterConnectionField
from graphql import GraphQLError
from graphql_jwt.decorators import login_required

from app.rewards.graphql.mutation import ExportSaleRewardMapping
from app.rewards.graphql.types import *


class Query(graphene.ObjectType):
    sale_goods_distribution_rewards = DjangoFilterConnectionField(RewardDistributionMappingNode, reward_target_month=graphene.Date(required=True))

    @login_required
    def resolve_sale_goods_distribution_rewards(self, info, **kwargs):
        if 'reward_target_month' not in kwargs:
            raise GraphQLError("Target month is required")
        year = kwargs.get('reward_target_month').year
        month = kwargs.get('reward_target_month').month
        return RewardDistributionMapping.objects.filter(target_month__year=year, target_month__month=month)\
            .order_by('source_sale_data__id', 'artist_group__id', 'product_category_group__id', 'mapping_reward_layer')\
            .all()

class Mutation(graphene.ObjectType):
    export_sale_reward_mapping = ExportSaleRewardMapping.Field()