from django.core.management import BaseCommand

from app.goods.usecase import generate_committee_report

class Command(BaseCommand):
    help = 'Generate committee report'

    def add_arguments(self, parser):
        parser.add_argument("--report_id", action="append", type=int)

    def handle(self, *args, **options):
        report_id = 138
        if 'report_id' in options:
            report_id = options.get('report_id')[0]

        generate_committee_report(report_id)