from django.core.management import BaseCommand

from app.common import utils
from app.goods.models import CommitteeReport
from app.goods.usecase import calculate_sale_good_report

class Command(BaseCommand):
    help = 'Register Product'

    def handle(self, *args, **options):
        # report = CommitteeReport.objects.create(
        #     category="GOKU",
        #     artist_id=123,
        #     title="test",
        #     period_start="2024-07-01",
        #     period_end="2024-09-01",
        #     report_date="2024-10-01",
        #     pay_date="2024-10-01",
        #     payslip_number="test",
        #     royalty_use_fee=1
        # )
        start_date = utils.convert_to_date("2024-07-01")
        end_date = utils.convert_to_date("2024-09-01")
        # print(f"report.pk: {report.pk}")
        calculate_sale_good_report(139, start_date, end_date, False)