from datetime import datetime
import re
from django.core.management import BaseCommand
from django.forms import model_to_dict
import numpy as np
import pandas as pd

from app.master_data.models import MstArtist, MstCompany, MstDistributionPatternGroup, MstPaymentRecipient, MstSegment
from app.goods.usecase import register_product
from app.project.models import ProductDetail
from app.user.models import User

class Command(BaseCommand):
    help = 'Register Product'

    def add_arguments(self, parser):
        parser.add_argument("--filepath", action="append", type=str)

    def handle(self, *args, **options):
        file_in = ''
        if 'filepath' in options and options['filepath']:
            file_in = options['filepath'][0]
        excel = pd.ExcelFile(file_in)
        excel = excel.parse(0).dropna(how='all').replace(np.nan, None, regex=True)
        register_product(excel, None, file_in.name)