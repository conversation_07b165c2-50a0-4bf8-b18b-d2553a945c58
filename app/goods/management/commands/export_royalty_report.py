from datetime import datetime
from django.core.management import BaseCommand

from app.common import utils
from app.goods.usecase import generate_royalty_report
from app.goods.models import RoyaltyReport

class Command(BaseCommand):
    help = 'Register Product'

    def add_arguments(self, parser):
        parser.add_argument("--target_date", action="append", type=str)

    def handle(self, *args, **options):
        start_date = utils.convert_to_date("2024-07-01")
        end_date = utils.convert_to_date("2024-09-30")
        filename = f"刀剣DL社プロデュース印税_2024年07月〜09月(5).pdf"
        # RoyaltyReport.objects.create(
        #     file_status='in_progress',
        #     file_name=filename
        # )
        args = {
			"start_date": start_date,
			"end_date": end_date,
			"pay_amount": "¥109,375",
			"pay_date": datetime.now(),
		}
        generate_royalty_report(filename, **args)