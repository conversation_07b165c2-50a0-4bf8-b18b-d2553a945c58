from datetime import datetime
import re
from django.core.management import BaseCommand
from django.forms import model_to_dict
import numpy as np
import pandas as pd

from app.common import utils
from app.master_data.models import MstArtist, MstCompany, MstDistributionPatternGroup, MstPaymentRecipient, MstSegment
from app.goods.usecase import generate_sale_report
from app.goods.models import ProductDetail
from app.user.models import User

class Command(BaseCommand):
    help = 'Register Product'

    def add_arguments(self, parser):
        parser.add_argument("--target_date", action="append", type=str)

    def handle(self, *args, **options):
        target_date = ''
        if options.get('target_date'):
            target_date = options.get('target_date')[0]

        file_name = f"sale_report_{target_date}_{utils.generate_epoch_time()}.xlsx"

		# convert string yyyy-mm-dd to datetime object
        target_date = datetime.strptime(target_date, '%Y-%m-%d')
        generate_sale_report(target_date, None, file_name)