import graphene
from graphene_django import DjangoConnectionField
from graphene_django.filter import DjangoFilterConnectionField
from graphql import GraphQLError

from app.goods.graphql.mutations import *
from app.goods.graphql.types import MonthlySaleV2Node, ProductDetailNode, RoyaltyReportNode, SaleGoodNode, SaleReportNode
from app.goods.models import ProductDetail, RoyaltyReport, SaleGood, SaleReport
from app.master_data.models import MstSaleRoute
from django.db.models import Sum, F, Max, ExpressionWrapper, FloatField
from app.goods.graphql.mutations import *
from app.goods.graphql.types import *
from graphql_jwt.decorators import login_required

from app.project.graphql.types import RewardDistributorPortionNode


class Query(graphene.ObjectType):
    all_product = DjangoFilterConnectionField(ProductDetailNode, id=graphene.ID())
    all_sale_data = DjangoConnectionField(SaleGoodNode, id=graphene.ID())
    monthly_sale_data = DjangoConnectionField(MonthlySaleV2Node, target_month=graphene.Date())
    monthly_sale_summary = graphene.List(SaleSummaryType, target_month=graphene.Date())
    sale_report_data = DjangoFilterConnectionField(SaleReportNode)
    royalty_report_data = DjangoFilterConnectionField(RoyaltyReportNode)
    committee_report_category = DjangoFilterConnectionField(CommitteeReportCatgeoryNode)
    committee_report_data = DjangoFilterConnectionField(CommitteeReportNode)
    committee_report_detail = graphene.Field(CommitteeReportDetailNode, id=graphene.ID(required=True))
    sale_good_distribution_price_list = DjangoFilterConnectionField(RewardDistributorPortionNode, target_date=graphene.Date(), new_field=graphene.String())

    @login_required
    def resolve_all_product(self, info, **kwargs):
        if 'id' in kwargs:
            return ProductDetail.objects.filter(pk=kwargs.get('id'))
        return ProductDetail.objects.all()

    @login_required
    def resolve_all_sale_data(self, info, **kwargs):
        if 'id' in kwargs:
            return SaleGood.objects.filter(pk=kwargs.get('id')).order_by('-updated_at')
        return SaleGood.objects.order_by('-updated_at').all()

    @login_required
    def resolve_monthly_sale_data(self, info, **kwargs):
        if 'target_month' not in kwargs:
            raise GraphQLError("Target month is required")
        target_month = kwargs['target_month'].month
        target_year = kwargs['target_month'].year
        return MstSaleRoute.objects \
            .filter(Q(sale_good_detail__target_month__month=target_month) & Q(sale_good_detail__target_month__year=target_year) & Q(sale_good_detail__is_delete=False)).prefetch_related('sale_good_detail').order_by(
                'sale_good_detail__sale'
            ) \
            .annotate(
                total_sales=Sum(F('sale_good_detail__sale'), output_field=models.DecimalField())
            )

    @login_required
    def resolve_monthly_sale_summary(self, info, **kwargs):
        if 'target_month' not in kwargs:
            raise GraphQLError("Target month is required")
        target_month = kwargs['target_month'].month
        target_year = kwargs['target_month'].year
        queryset = SaleGoodDetail.objects.filter(
            Q(is_delete=False) &
            Q(target_month__month=target_month) &
            Q(target_month__year=target_year)
        ).values(
            'report_type'
        ).annotate(
            sale_route_name=Max('sale_route__route_name'),
            target_month=Max('target_month'),
            created_at=Max('created_at'),
            total_sales=Sum(
                ExpressionWrapper(
                    F('original_sale'),
                    output_field=models.IntegerField()
                )
            )
        ).order_by('report_type')

        return [
            SaleSummaryType(
                report_type=item.get('report_type'),
                target_month=item.get('target_month'),
                sale_route_name=item.get('sale_route_name'),
                total_sales=item.get('total_sales'),
                created_at=item.get('created_at')
            ) for item in queryset
        ]

    @login_required
    def resolve_sale_report_data(self, info, **kwargs):
        return SaleReport.objects.all()

    @login_required
    def resolve_royalty_report_data(self, info, **kwargs):
        return RoyaltyReport.objects.all()

    @login_required
    def resolve_committee_report_category(self, info, **kwargs):
        return CommitteeReportCategory.objects.all()

    @login_required
    def resolve_committee_report_data(self, info, **kwargs):
        return CommitteeReport.objects.all()

    @login_required
    def resolve_committee_report_detail(self, info, **kwargs):
        committee_report = CommitteeReport.objects.filter(pk=kwargs.get('id'))
        if not committee_report.exists():
            raise GraphQLError("Committee report not found")
        return committee_report.first()

    @login_required
    def resolve_sale_good_distribution_price_list(self, info, **kwargs):
        reward_distributor = RewardDistributorPortion.objects.filter(Q(project__isnull=True)).order_by('sale_data__id', '-created_at').distinct('sale_data__id')
        if 'target_date' in kwargs:
            target_date = kwargs.get('target_date')
            reward_distributor = reward_distributor.filter(Q(sale_data__target_month__year=target_date.year) & Q(sale_data__target_month__month=target_date.month))
        # if 'first' in kwargs:
        #     reward_distributor = reward_distributor[:kwargs.get('first')]
        # if 'skip' in kwargs:
        #     reward_distributor = reward_distributor[kwargs.get('skip'):]

        return reward_distributor

class Mutation(graphene.ObjectType):
    create_product = CreateProduct.Field()
    delete_product = DeleteProductDetailData.Field()
    upload_sales_good = UploadSalesGoodMutation.Field()
    export_sales_good = ExportSaleGoodMutation.Field()
    generate_sale_good_reward = GenerateSalesGoodRewardMutation.Field()
    sale_good_report_generation = SaleGoodReportGeneration.Field()
    sale_good_periodic_report_generation = SaleGoodPeriodicReportGeneration.Field()
    royalty_reward_generation = RoyaltyRewardGeneration.Field()
    create_committee_report = CreateCommitteeReport.Field()
    delete_committee_report = DeleteCommitteeReportData.Field()
    create_archive_streaming = CreateArchiveStreaming.Field()
    delete_archive_streaming = DeleteArchiveStreaming.Field()
    create_rakuten_stream_service = CreateRakutenStreamService.Field()
    delete_rakuten_stream_service = DeleteRakutenStreamService.Field()
    regenerate_committee_report = RegenerateCommitteeReportMutation.Field()
    upload_product = UploadProductMutation.Field()
    export_product = ExportProductMutation.Field()
    create_gacha_information = CreateGachaInformation.Field()
    delete_gacha_information = DeleteGachaInformation.Field()