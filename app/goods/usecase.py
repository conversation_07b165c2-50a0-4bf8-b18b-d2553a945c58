from datetime import datetime
import json
from django.core.mail import EmailMessage
from io import BytesIO
import itertools
import logging
import os
import textwrap
import budoux
from django.conf import settings
from django.core.mail import send_mail
from dateutil.rrule import rrule, MONTHLY
from django.db.models.functions import ExtractYear, ExtractMonth, Cast
from django.core.files.base import ContentFile
from django.db import models
from django.db.models import Q, ExpressionWrapper
from django.db import transaction
from django.db.models import Sum, F
from django.db.models.functions import Cast
from django.forms import model_to_dict
import numpy as np
import pandas as pd
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import portrait, A4, landscape
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.cidfonts import UnicodeCIDFont
from reportlab.lib import colors
from reportlab.platypus import Table, TableStyle, Paragraph
from reportlab.lib.units import mm
from reportlab.lib.styles import getSampleStyleSheet

from app.common import usecase, utils
from app.goods import committee_report_builder
from app.goods.models import CommitteeReport, GachaInformation, ProductDetail, ReportArchiveStreaming, ReportRakutenStreamService, RoyaltyReport, SaleGoodDetail, SaleGoodReport, SaleReport
from app.master_data.models import MediaTitle, MstArtist, MstCompany, MstDistributionPatternGroup, MstSaleRoute, MstSegment, ProductCategoryGroup
from django_q.tasks import async_task

from app.user.models import User

media_directory = settings.MEDIA_ROOT
logger = logging.getLogger(__name__)
legal = (215*mm,330*mm)

def generate_sale_report(start_date: datetime, end_date: datetime, filename: str):
    file_name = filename
    formatted_start_date = start_date.strftime("%Y-%m")
    if file_name is None and end_date is None:
        file_name = f"sale_report_{formatted_start_date}_{utils.generate_epoch_time()}.xlsx"
    elif file_name and end_date:
        formatted_end_date = end_date.strftime("%Y-%m")
        file_name = f"sale_report_{formatted_start_date}_{formatted_end_date}_{utils.generate_epoch_time()}.xlsx"

    # start_date = start_date.replace(day=1)
    start_date_month = start_date.month
    start_date_year = start_date.year
    end_date_month = None
    end_date_year = None

    if end_date:
        end_date_month = end_date.month
        end_date_year = end_date.year

    """
    column_dict = {
        "発売元": [],
        "Issue date": [],
        "Prod category": [],
        "Prod id": [],
        "Prod name": [],
        "Artist": [],
        "daiki": [],
        "price after tax": [],
        "price": [],
        "ダイキ ダイキ（amazon)": [],
        "経理報告書": [],
        "Live": [],
        "Silkroad Store": [],
        "amazon": [],
        "当月販売数": [],
    }
    """
    column_dict = {
        "発売元": [],
        "発売日": [],
        "区分": [],
        "規格番号": [],
        "タイトル": [],
        "アーティスト": [],
        "ダイキ卸": [],
        "税抜 価格": [],
        "税込 価格 (10%）": [],
        "ダイキ ダイキ（amazon)(数量)": [],
        "経理報告書(数量)": [],
        "Live(数量)": [],
        "Silkroad Store(数量)": [],
        "Amazon(数量)": [],
        "当月販売数": [],
        "ダイキ(税抜金額)": [],
        "経理報告書 (税込金額)": [],
        "ライブ(税込金額)": [],
        "Silkroad Store(税込金額)": [],
        "Amazon(税込金額)": [],
    }

    product_list = ProductDetail.objects \
        .filter(Q(sale_good_detail__target_month__year=start_date_year) & Q(sale_good_detail__target_month__month=start_date_month))

    if end_date:
        product_list = ProductDetail.objects \
        .filter(Q(sale_good_detail__target_month__year__gte=start_date_year) & Q(sale_good_detail__target_month__month__gte=start_date_month)) \
        .filter(Q(sale_good_detail__target_month__year__lte=end_date_year) & Q(sale_good_detail__target_month__month__lte=end_date_month))

    product_list = product_list.filter(Q(segment__segment_name__startswith='パッケージ')) \
        .order_by('artist__artist_name', 'product_category', '-created_at') \
        .distinct()
    # sale_good_details = SaleGoodDetail.objects.filter(target_month__month=month)
    # total_price_per_product = sale_good_details.values('product_id').annotate(
    #     total_price=Sum(F('price') * F('quantity'), output_field=models.DecimalField())
    # )

    if not product_list:
        logger.info(f"No product found for month {start_date_month}")
        SaleReport.objects.filter(Q(target_month__year=start_date_month) & Q(target_month__month=start_date_month) & Q(file_name=file_name)) \
            .update(file_status='done', file_name=None)
        return

    logger.info(f"start generate sale report {file_name}")

    for product in product_list:
        logger.info(f"product: {utils.model_to_dict(instance=product, fields=[field.name for field in product._meta.fields])}")
        total_price_per_report_type = SaleGoodDetail.objects \
            .filter(Q(product_detail=product) & Q(target_month__year=start_date_year) & Q(target_month__month=start_date_month) & Q(is_delete=False))

        if end_date:
            total_price_per_report_type = SaleGoodDetail.objects \
                .filter(Q(product_detail=product) & Q(target_month__year__gte=start_date_year) & Q(target_month__month__gte=start_date_month)) \
                .filter(Q(target_month__year__lte=end_date_year) & Q(target_month__month__lte=end_date_month) & Q(is_delete=False))

        total_price_per_report_type = total_price_per_report_type.values('report_type') \
        .annotate(
            total_price=Sum(F('sale'), output_field=models.FloatField()),
            total_sale=Sum(F('quantity'), output_field=models.FloatField())
        )

        total_price_dict = {item.get('report_type'): item.get('total_price') for item in total_price_per_report_type}
        total_sale_dict = {item.get('report_type'): item.get('total_sale') for item in total_price_per_report_type}
        logger.info(f"total_sale_dict: {total_sale_dict}")

        column_dict["発売元"].append(" ")
        column_dict["発売日"].append(product.issue_date)
        column_dict["区分"].append(product.product_category)
        column_dict["規格番号"].append(product.product_id)
        column_dict["タイトル"].append(product.product_name)
        column_dict["アーティスト"].append(product.artist.artist_name)
        daiki_sale_product = product.daiki_price if product.daiki_price else 0
        daiki_sale_product = utils.rounddown(utils.to_float(daiki_sale_product))
        column_dict["ダイキ卸"].append(daiki_sale_product)
        product_price = product.price if product.price else 0
        price_before_tax = utils.rounddown(utils.to_int(product_price) * 1.1)
        column_dict["税抜 価格"].append(utils.to_int(product_price))
        column_dict["税込 価格 (10%）"].append(price_before_tax)
        # total price per report type
        total_sale = 0
        daiki_sale = total_sale_dict.get("daiki") if total_sale_dict.get("daiki") else 0
        total_sale += daiki_sale
        column_dict["ダイキ ダイキ（amazon)(数量)"].append(daiki_sale)

        accounting_sale = total_sale_dict.get("経理報告書") if total_sale_dict.get("経理報告書") else 0
        total_sale += accounting_sale
        column_dict["経理報告書(数量)"].append(accounting_sale)

        live_sale = total_sale_dict.get("物販") if total_sale_dict.get("物販") else 0
        total_sale += live_sale
        column_dict["Live(数量)"].append(live_sale)

        silkroad_sale = total_sale_dict.get("Silkroad Store") if total_sale_dict.get("Silkroad Store") else 0
        total_sale += silkroad_sale
        column_dict["Silkroad Store(数量)"].append(silkroad_sale)

        amazon_sale = total_sale_dict.get("Amazon") if total_sale_dict.get("Amazon") else 0
        total_sale += amazon_sale
        column_dict["Amazon(数量)"].append(amazon_sale)

        column_dict["当月販売数"].append(total_sale)

        daiki_total_sale = utils.rounddown(daiki_sale_product * utils.to_int(daiki_sale))
        column_dict["ダイキ(税抜金額)"].append(daiki_total_sale)

        accounting_total_sale = utils.rounddown(utils.to_int(product_price) * utils.to_int(accounting_sale))
        column_dict["経理報告書 (税込金額)"].append(accounting_total_sale)

        live_total_sale = utils.rounddown(utils.to_int(product_price) * utils.to_int(live_sale))
        column_dict["ライブ(税込金額)"].append(live_total_sale)

        silkroad_total_sale = utils.rounddown(utils.to_int(product_price) * utils.to_int(silkroad_sale))
        column_dict["Silkroad Store(税込金額)"].append(silkroad_total_sale)

        amazon_total_sale = utils.rounddown(utils.to_int(product_price) * utils.to_int(amazon_sale))
        column_dict["Amazon(税込金額)"].append(amazon_total_sale)

    logger.info(f'column_dict: {column_dict}')
    df_from_dict = pd.DataFrame(column_dict)
    file_path = os.path.join(media_directory, 'sale_report', file_name)
    logger.info(f'file_path: {file_path}')

    buffer = BytesIO()
    writer = pd.ExcelWriter(buffer, engine='xlsxwriter', engine_kwargs={'options': {'strings_to_numbers': True}})
    df_from_dict.T.reset_index().T.to_excel(writer, header=False, index=False)
    writer.close()

    excel_file = ContentFile(buffer.getvalue())

    report_row = SaleReport.objects.filter(Q(target_month__year=start_date_year) & Q(target_month__month=start_date_month) & Q(file_name=file_name))
    if end_date:
        report_row = report_row.filter(Q(target_month_end__year__gte=end_date_year) & Q(target_month_end__month__gte=end_date_month))
    report_row = report_row.first()
    if report_row:
        if report_row.report_file:
            report_row.report_file.delete()
        report_row.report_file.save(f"{file_name}", excel_file, save=False)
        report_row.file_name = report_row.report_file_name()
        report_row.file_status = 'done'
        report_row.save()

def generate_royalty_report(filename: str, **kwargs):
    if kwargs.get('start_date') is None or kwargs.get('end_date') is None:
        raise Exception("start_date and end_date are required.")
    # os.makedirs(f'{media_directory}/royalty_report', exist_ok=True)

    start_date = kwargs.get('start_date')
    end_date = kwargs.get('end_date')
    pay_amount = kwargs.get('pay_amount') if kwargs.get('pay_amount') else 0
    pay_date = kwargs.get('pay_date') if kwargs.get('pay_date') else datetime.now()

    formatted_start_date = start_date.strftime("%Y年%m月")
    formatted_end_date = end_date.strftime("%Y年%m月")
    general_format_end_date = end_date.strftime("%Y-%m-%d")
    formatted_pay_date = pay_date.strftime("%Y年%m月%d日")
    months = [dt.strftime("%B") for dt in rrule(MONTHLY, dtstart=start_date, until=end_date)]
    number_months = [dt.strftime("%m") for dt in rrule(MONTHLY, dtstart=start_date, until=end_date)]
    print(f"months: {months}")

    pdf_buffer = BytesIO()
    page_canvas = canvas.Canvas(pdf_buffer, pagesize=portrait(A4))
    page_canvas.setTitle(filename)
    pdfmetrics.registerFont(UnicodeCIDFont("HeiseiMin-W3"))
    styles = getSampleStyleSheet()
    styleN = styles['Normal']
    styleN.wordWrap = 'CJK'
    styleN.fontName = "HeiseiMin-W3"
    styleN.fontSize = 8

    product_list = ProductDetail.objects \
        .filter(
            (Q(sale_good_detail__target_month__year__gte=start_date.year, sale_good_detail__target_month__month__gte=start_date.month) &
            Q(sale_good_detail__target_month__year__lte=end_date.year, sale_good_detail__target_month__month__lte=end_date.month)) &
            Q(shown_report=True) &
            Q(media_title__isnull=False)) \
        .order_by('product_category', 'product_id') \
        .distinct()
    package_data_list = []
    total_payment_package = 0
    total_amount_all_month = 0
    total_amount_per_month = {}
    for month in number_months:
        total_amount_per_month[month] = 0

    product_height = 0
    logger.info(f"total_payment_package: {total_payment_package}")
    for product in product_list:
        product_height -= 5
        # product_name_modulo = len(product.product_name) % 150
        # if product_name_modulo > 1:
        #     product_height -= (15 * product_name_modulo)
        product_price = utils.rounddown(utils.to_int(product.price) / 1.1) if product.price else 0
        product_price = utils.roundup_int(product_price)
        package_data = {
            "issue_date": utils.convert_to_str(product.issue_date, "%Y/%m/%d") if product.issue_date else "-",
            "production_name": Paragraph(product.product_name, styleN),
            "production_category": product.product_category,
            "prod_code": product.product_id,
            "prod_price": f"{utils.format_currency_value(product_price)}",
        }
        for month in number_months:
            package_data[f"{month}月"] = 0

        starter_royalty = product.media_title.starter_royalty if product.media_title and product.media_title.starter_royalty else 2.0
        advance_royalty = product.media_title.advance_royalty if product.media_title and product.media_title.advance_royalty else 0.0
        switch_sale_amount = product.media_title.switch_sale_amount if product.media_title and product.media_title.switch_sale_amount else 0.0
        royalty = starter_royalty

        package_data.update({
            "sale_quantity": 0,
            "royalty_percentage": f"{royalty}%",
            "payment": 0,
        })
        sale_good_details = SaleGoodDetail.objects \
            .filter(
                Q(product_detail=product) &
                (Q(target_month__year__gte=start_date.year, target_month__month__gte=start_date.month) &
                Q(target_month__year__lte=end_date.year, target_month__month__lte=end_date.month))
            )
        total_quantity = sale_good_details.values('target_month').annotate(
            total_quantity=Sum(F('quantity'), output_field=models.IntegerField())
        ).order_by('target_month')

        total_sale_dict = {item.get('target_month').strftime("%m"): item.get('total_quantity') for item in total_quantity}
        total_quantity_per_period = 0
        total_sale_amount_per_product = SaleGoodDetail.objects \
            .filter(
            Q(product_detail__media_title=product.media_title) &
            (Q(target_month__year__lte=end_date.year) & Q(target_month__month__lte=end_date.month))
            ) \
            .aggregate(total_quantity=Sum('quantity'))
        total_sale_amount_per_product = total_sale_amount_per_product.get('total_quantity') if total_sale_amount_per_product else 0
        for month in number_months:
            if total_sale_dict.get(month):
                package_data[f"{month}月"] = total_sale_dict.get(month)
                total_quantity_per_period += total_sale_dict.get(month)
                total_amount_per_month[month] += total_sale_dict.get(month)
            else:
                package_data[f"{month}月"] = 0
        package_data["sale_quantity"] = total_quantity_per_period
        total_amount_all_month += total_quantity_per_period

        logger.info(f"total_quantity_per_period: {total_quantity_per_period} | switch_sale_amount: {utils.to_int(switch_sale_amount)}")
        if total_sale_amount_per_product > switch_sale_amount:
            royalty = advance_royalty

            package_data["royalty_percentage"] = f"{starter_royalty}%"
            net_payment = utils.to_float(product_price) * utils.to_float(total_quantity_per_period) * ((starter_royalty / 100) if starter_royalty > 0.0 else 0)
            net_roundup_payment = utils.rounddown(net_payment)
            package_data["payment"] = f"{utils.format_currency_value(net_roundup_payment)}"
            package_data_list.append(list(package_data.values()))
            logger.info(f"net_payment: {net_payment}|net_roundup_payment: {net_roundup_payment}")

        package_data["royalty_percentage"] = f"{royalty}%"
        # print(f"product_price: {product_price}|total_quantity_per_period: {total_quantity_per_period}|royalty: {royalty}")
        net_payment = utils.to_float(product_price) * utils.to_float(total_quantity_per_period) * ((royalty / 100) if royalty > 0.0 else 0)
        net_roundup_payment = utils.rounddown(net_payment)
        package_data["payment"] = f"{utils.format_currency_value(net_roundup_payment)}"
        total_payment_package += net_roundup_payment
        package_data_list.append(list(package_data.values()))
        logger.info(f"total_payment_package: {total_payment_package}|net_payment: {net_payment}|net_roundup_payment: {net_roundup_payment}")

    # start page 1
    page_canvas.setFont("HeiseiMin-W3", 10)
    page_canvas.setFillColor(colors.black)

    start_x0 = 30
    height = 770
    page_canvas.drawString(start_x0, height, "プロデュース印税報告書")
    page_canvas.line(start_x0, height - 5, 560, height - 5)

    page_canvas.drawString(start_x0, height - 40, "株式会社ドリームライン　御中")
    page_canvas.drawRightString(start_x0 + 525, height - 40, f"対象期間：{formatted_start_date} 〜 {formatted_end_date}")
    page_canvas.line(start_x0, height - 45, 200, height - 45)

    page_canvas.drawString(start_x0, height - 60, "登録番号：T5010901046540")
    page_canvas.drawRightString(start_x0 + 525, height - 60, f"締め日：{utils.get_last_date(general_format_end_date) if utils.get_last_date(general_format_end_date) else ''}")

    total_before_tax = sum([total_payment_package, utils.to_int(pay_amount)])
    total_tax = utils.rounddown(total_before_tax * (10 / 100))
    total_payment = total_before_tax + total_tax
    payment_info_table_data = [
        ["項目", "発生金額", "備考"],
        ["パッケージ", f"{utils.format_currency_value(total_payment_package)}", ""],
        ["楽曲配信", f"{utils.format_currency_value(pay_amount)}", ""],
        ["合計（税抜き）", f"{utils.format_currency_value(total_before_tax)}", "Tax: 10%対象"],
        ["消費税", f"{utils.format_currency_value(total_tax)}", "10%"],
        ["お振込金額 ", f"{utils.format_currency_value(total_payment)}", ""],
    ]

    payment_info_table = Table(payment_info_table_data, colWidths=[100, 150, 275])
    style = [
        ("FONT", (0, 0), (-1, -1), "HeiseiMin-W3", 10),
        ("BACKGROUND", (0, 0), (-1, 0), "#DCDCDC"),
        ("ALIGN", (1, 1), (1, -1), "LEFT"),
        ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),
        ('GRID', (0,0), (-1,-2), 0.25, colors.black),
        ('GRID', (0,-1), (-2,-1), 0.25, colors.black),
        ("BACKGROUND", (0, -1), (-2, -1), "#BFBFBF"),
    ]
    payment_info_table.setStyle(TableStyle(style))
    payment_info_table.wrapOn(page_canvas, start_x0, height - 200)
    payment_info_table.drawOn(page_canvas, start_x0, height - 200)

    page_canvas.drawRightString(start_x0 + 525, height - 375, f"お振込日：{formatted_pay_date}")
    page_canvas.line(start_x0, height - 380, 560, height - 380)

    page_canvas.drawRightString(start_x0 + 525, height - 400, "株式会社ユークリッド・エージェンシー")
    page_canvas.drawRightString(start_x0 + 525, height - 420, "プロデュース事業部")

    bank_info_table_data = [
        ["お振込口座"],
        ["お振込口座：　三井住友銀行"],
        ["支店名：　恵比寿支店"],
        ["口座種類：　普通口座"],
        ["口座番号：　9096562"],
        ["口座名義：　株式会社ドリームライン"],
    ]

    bank_info_table = Table(bank_info_table_data, colWidths=[275])
    style = [
        ("FONT", (0, 0), (-1, -1), "HeiseiMin-W3", 10),
        ("ALIGN", (0, 0), (-1, 0), "CENTER"),
        ("BACKGROUND", (0, 0), (-1, 0), "#DCDCDC"),
        ("ALIGN", (1, 1), (1, -1), "LEFT"),
        ('GRID', (0,0), (-1,-1), 0.25, colors.black),
    ]
    bank_info_table.setStyle(TableStyle(style))
    bank_info_table.wrapOn(page_canvas, start_x0 + 245, height - 600)
    bank_info_table.drawOn(page_canvas, start_x0 + 245, height - 600)

    # end page 1
    page_canvas.showPage()

    # start page 2
    page_canvas.setPageSize(landscape(A4))
    page_canvas.setFont("HeiseiMin-W3", 10)
    page_canvas.setFillColor(colors.black)

    start_x0 = 50
    height = 570
    page_canvas.drawString(start_x0, height, "株式会社ドリームライン")

    page_canvas.drawString(start_x0, height - 20, "印税種別")
    page_canvas.drawString(start_x0 + 75, height - 20, "プロデュース印税")
    page_canvas.drawString(start_x0, height - 30, "対象期間")
    page_canvas.drawString(start_x0 + 75, height - 30, f"{formatted_start_date} 〜 {formatted_end_date}")

    royalty_info_table_data = [
        ["", "印税率/枚数", ""],
        ["区分", "2%", "2.5%"],
        ["Single", f"{utils.format_amount(100000)}", f"{utils.format_amount(100001)}〜"],
        ["Album", f"{utils.format_amount(25000)}", f"{utils.format_amount(25001)}〜"],
        ["Blu-ray", f"{utils.format_amount(40000)}", f"{utils.format_amount(40001)}〜"],
        ["DVD", f"{utils.format_amount(40000)}", f"{utils.format_amount(40001)}〜"],
    ]

    royalty_info_table = Table(royalty_info_table_data, colWidths=[50, 50, 50])
    style = [
        ("FONT", (0, 0), (-1, -1), "HeiseiMin-W3", 5),
        ("BACKGROUND", (0, 1), (-1, 1), "#DCDCDC"),
        ("ALIGN", (1, 1), (1, -1), "LEFT"),
        ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),
        ('GRID', (0,1), (-1,-1), 0.25, colors.black),
        ('GRID', (1,0), (-1,0), 0.25, colors.black),
        ('SPAN', (1,0),(-1,0),),
    ]
    royalty_info_table.setStyle(TableStyle(style))
    royalty_info_table.wrapOn(page_canvas, start_x0 + 225, height - 70)
    royalty_info_table.drawOn(page_canvas, start_x0 + 225, height - 70)

    modulo_package_data_list, remainder = divmod(len(package_data_list), 25)
    logger.info(f"modulo_package_data_list: {modulo_package_data_list}|remainder: {remainder}")
    start_idx = 0
    end_idx = 25
    if remainder > 1:
        modulo_package_data_list += 1
    package_data_height = 25
    for i in range(0, modulo_package_data_list):
        is_last_element = i == modulo_package_data_list - 1
        logger.info(f"is_last_element: {is_last_element}")

        package_table_header = ["発売日", "作品タイトル", "区分", "規格番号", "価格\n(税抜)"]
        for month in number_months:
            package_table_header.append(f"{month}月")
        package_table_header.extend(["販売数計", "印税率", "発生金額"])

        package_table_data = [
            # ["Issue Date", "Production name", "Production category", "Prod code", "Prod price", "July", "August", "September", "Sale quantity", "Royalty percentage", "Payment"],
            # ["6/29/2022", "PRD-A", "Blu-ray", "EMPB-5011", "¥8,500", "100(200)", "", "", "0", "2.0%", "¥0"],
            # ["", "", "", "", "", "", "", "", "Total before tax", "", "¥66,875"],
            package_table_header
        ]

        sliced_package_data_list = package_data_list[start_idx:end_idx]
        package_table_data.extend(sliced_package_data_list)
        start_idx += 25
        end_idx += 25

        style = [
            ("FONT", (0, 0), (-1, -1), "HeiseiMin-W3", 7),
            ("ALIGN", (0, 0), (-1, 0), "CENTER"),
            ("ALIGN", (1, 1), (1, -1), "LEFT"),
            ("ALIGN", (5, 1), (-2, -1), "RIGHT"),
            ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),
            ('GRID', (0,0), (-1,-1), 0.1, colors.black),
        ]
        if is_last_element:
            last_row = ["", "", "", "", ""]
            for month in number_months:
                last_row.append(utils.format_amount(total_amount_per_month.get(month)))
            last_row.extend([utils.format_amount(total_amount_all_month), "", f"{utils.format_currency_value(total_payment_package)}"])
            package_table_data.append(last_row)

            style = [
                ("FONT", (0, 0), (-1, -1), "HeiseiMin-W3", 8),
                ("ALIGN", (0, 0), (-1, 0), "CENTER"),
                ("ALIGN", (1, 1), (1, -1), "LEFT"),
                ("ALIGN", (0, 1), (1, -1), "CENTER"),
                ("ALIGN", (3, 1), (4, -1), "CENTER"),
                ("ALIGN", (5, 1), (-2, -1), "RIGHT"),
                ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),
                ('GRID', (0,0), (-1,-2), 0.1, colors.black),
                ('GRID', ((-3+(-1 *len(number_months))),-1), (-1,-1), 0.1, colors.black),
                # ('SPAN', (-2,-1),(-3,-1),),
                # ("ALIGN", (0, -1), (9, -1), "RIGTH"),
            ]

        colWidths = [60, 290, 60, 70, 55]
        colWidths_month = [30 for i in range(len(number_months))]
        colWidths.extend(colWidths_month)
        colWidths.extend([40, 30, 60])

        package_table = Table(
            package_table_data, colWidths=colWidths, rowHeights=None,
            repeatRows=1, splitByRow=1
        )
        package_table.setStyle(TableStyle(style))
        if i == 0:
            package_data_height = 20
        if len(sliced_package_data_list) < 25 and len(sliced_package_data_list) > 15:
            package_data_height += len(sliced_package_data_list) * 2
        elif len(sliced_package_data_list) < 15:
            package_data_height += len(sliced_package_data_list) * 10
        package_table.wrapOn(page_canvas, start_x0, package_data_height)
        package_table.drawOn(page_canvas, start_x0, package_data_height)
        package_data_height = 25

        # end page 2
        page_canvas.showPage()

    # save pdf
    page_canvas.save()

    pdf_buffer.seek(0)
    pdf_value = pdf_buffer.getvalue()
    pdf_file = ContentFile(pdf_value)
    # pdf_response = FileResponse(pdf_value, filename=f"{filename}.pdf")

    royalty_report = RoyaltyReport.objects.filter(file_name=filename).first()
    if not royalty_report:
        pdf_buffer.close()
        return

    if royalty_report and hasattr(royalty_report, 'report_file'):
        royalty_report.report_file.delete()
    royalty_report.report_file.save(filename, pdf_file, save=False)
    royalty_report.file_status = 'done'
    royalty_report.save()

    pdf_buffer.close()

def calculate_sale_good_report(report_id, period_start: datetime, period_end: datetime, is_generate=True):
    report_data = CommitteeReport.objects.filter(pk=report_id)
    if not report_data.exists():
        logger.info(f"Report not found for id: {report_id}")
        return

    report_data = report_data.first()

    start_year = period_start.year
    start_month = period_start.month

    end_year = period_end.year
    end_month = period_end.month

    sale_good_details = SaleGoodDetail.objects.filter(
        Q(target_month__year__gte=start_year, target_month__month__gte=start_month) &
        Q(target_month__year__lte=end_year, target_month__month__lte=end_month)
    )

    sale_good_detail_grouped = sale_good_details \
        .order_by('target_month') \
        .values('sale_route__route_name', 'product_detail__product_category', 'product_detail__product_id', 'product_detail__product_name') \
        .annotate(
            total_sales=Sum(F('sale'), output_field=models.FloatField())
        )

    print(f"sale_good_detail_grouped: {sale_good_detail_grouped.query}")

    number_months = [f"{dt.year}|{dt.month}" for dt in rrule(MONTHLY, dtstart=period_start, until=period_end)]

    for sale_good_detail_data in sale_good_detail_grouped:
        sale_route = MstSaleRoute.objects.filter(route_name=sale_good_detail_data.get('sale_route__route_name')).first()
        product_detail = ProductDetail.objects.filter(
            product_category=sale_good_detail_data.get('product_detail__product_category'),
            product_id=sale_good_detail_data.get('product_detail__product_id'),
            product_name=sale_good_detail_data.get('product_detail__product_name')
        ).first()
        sale_good_report, created = SaleGoodReport.objects.get_or_create(
            committee_report=report_data,
            sale_route=sale_route,
            product_detail=product_detail,
            defaults={
                'first_sale_amount': 0,
                'second_sale_amount': 0,
                'third_sale_amount': 0,
                'total_sales_amount': 0,
                'first_sale_price': 0,
                'second_sale_price': 0,
                'third_sale_price': 0,
                'total_sales_price': 0,
                'fee': 0,
                'payment': 0,
            }
        )
        sale_data = SaleGoodDetail.objects.filter(
            sale_route__route_name=sale_good_detail_data.get('sale_route__route_name'),
            product_detail__product_category=sale_good_detail_data.get('product_detail__product_category'),
            product_detail__product_id=sale_good_detail_data.get('product_detail__product_id'),
            product_detail__product_name=sale_good_detail_data.get('product_detail__product_name'),
        ) \
        .annotate(
            year=ExtractYear('target_month'),
            month=ExtractMonth('target_month')
        ) \
        .values('year', 'month', 'sale_route', 'product_detail') \
        .annotate(
            quantity_amount=Sum(F('quantity'), output_field=models.IntegerField()),
            sale_amount=Sum(F('sale'), output_field=models.FloatField())
        ).order_by('year', 'month')

        print(f"sale_data: {sale_data.query}")

        total_quantity_amount = 0
        total_sale_amount = 0
        quantity_amount_per_month = []
        sale_amount_per_month = []
        sale_data_map = {f"{data.get('year')}|{data.get('month')}": data for data in sale_data}
        for i, month in enumerate(number_months):
            # if not utils.has_index(sale_data, i):
            #     quantity_amount_per_month.append(0)
            #     sale_amount_per_month.append(0)
            #     continue

            # if sale_data[i].get('month') != (int(month)+1):
            #     quantity_amount_per_month.append(0)
            #     sale_amount_per_month.append(0)
            #     continue
            if not sale_data_map.get(month):
                quantity_amount_per_month.append(0)
                sale_amount_per_month.append(0)
                continue

            quantity_amount_per_month_value = sale_data_map.get(month).get('quantity_amount') if sale_data_map.get(month) else 0
            quantity_amount_per_month.append(quantity_amount_per_month_value)
            total_quantity_amount += quantity_amount_per_month_value

            sale_amount_per_month_value = sale_data_map.get(month).get('sale_amount') if sale_data_map.get(month) else 0
            sale_amount_per_month.append(sale_amount_per_month_value)
            total_sale_amount += sale_amount_per_month_value

        for data in sale_data:
            quantity_amount = data.get('quantity_amount') if data.get('quantity_amount') else 0
            sale_amount = data.get('sale_amount') if data.get('sale_amount') else 0
            fee = total_sale_amount * 0.3
            payment_amount = total_sale_amount - fee

            sale_good_report.first_sale_amount = quantity_amount_per_month[0]
            sale_good_report.second_sale_amount = quantity_amount_per_month[1]
            sale_good_report.third_sale_amount = quantity_amount_per_month[2]
            sale_good_report.total_sales_amount = total_quantity_amount
            sale_good_report.first_sale_price = sale_amount_per_month[0]
            sale_good_report.second_sale_price = sale_amount_per_month[1]
            sale_good_report.third_sale_price = sale_amount_per_month[2]
            sale_good_report.total_sales_price = total_sale_amount
            sale_good_report.fee = utils.rounddown(fee)
            sale_good_report.payment = utils.rounddown(payment_amount)
            sale_good_report.save()

        logger.info(f"sale_data: {sale_data}")

        if is_generate:
            async_task(generate_committee_report, report_id)

def generate_committee_report(report_id):
    report_data = CommitteeReport.objects.filter(pk=report_id)
    if not report_data.exists():
        logger.info(f"Report not found for id: {report_id}")
        return
    try:

        report_data = report_data.first()
        sale_good_reports = SaleGoodReport.objects.filter(committee_report=report_data)

        month_list = list(rrule(MONTHLY, dtstart=report_data.period_start, until=report_data.period_end))

        filename = f"committee_report"
        company_name = ""
        register_number = ""
        period_start = report_data.period_start.strftime("%Y年%m月")
        period_end = report_data.period_end.strftime("%m月")
        datenow = datetime.now().date().strftime("%Y年%m月%d日")
        report_date = report_data.report_date.strftime("%Y年%m月%d日") if report_data.report_date else datenow
        if report_data.category == 'GOKU':
            filename = f"GOKU委員会報告書({datenow}報告)"
            register_number = "T1013201015921"
            company_name = "株式会社ネルケプランニング"
        elif report_data.category == 'ふしぎ遊戯':
            filename = f"ふしぎ遊戯委員会報告書({datenow}報告)"
            register_number = "T1013201015921"
            company_name = "株式会社ネルケプランニング"
        elif report_data.category == '犬夜叉':
            filename = f"犬夜叉委員会報告書({datenow}報告)"
            register_number = "T1013201015921"
            company_name = "株式会社ネルケプランニング"
        elif report_data.category == '御茶ノ水ロック':
            filename = f"御茶ノ水ロック委員会報告書({datenow}報告)"
            register_number = "T6010001072478"
            company_name = "ポリゴンマジック株式会社"
        else:
            logger.info(f"Category not found for {report_data.category}")
            return

        parser = budoux.load_default_japanese_parser()
        pdf_buffer = BytesIO()
        page_canvas = canvas.Canvas(pdf_buffer, pagesize=portrait(A4))
        page_canvas.setTitle(filename)
        pdfmetrics.registerFont(UnicodeCIDFont("HeiseiMin-W3"))
        styles = getSampleStyleSheet()
        styleN = styles['Normal']
        styleN.wordWrap = 'CJK'
        styleN.fontName = "HeiseiMin-W3"
        styleN.fontSize = 8
        # start page 1
        page_canvas.setFont("HeiseiMin-W3", 10)
        page_canvas.setFillColor(colors.black)

        start_x0 = 30
        start_x_center = page_canvas._pagesize[0] / 2
        start_x1 = 45
        height = 770
        height_y1 = 570
        page_canvas.setFont("HeiseiMin-W3", 32)
        page_canvas.drawCentredString(start_x_center, height - 30, f"『{report_data.artist.artist_name}』")

        page_canvas.setFont("HeiseiMin-W3", 12)
        page_canvas.drawCentredString(start_x_center, height - 70 , f"主催：ネルケプランニング・ユークリッド・エージェンシー")

        report_title = parser.parse(report_data.title)
        for i, title in enumerate(report_title):
            page_canvas.setFont("HeiseiMin-W3", 32)
            page_canvas.drawCentredString(start_x_center, height - 180 - (i * 35), f"{title}")

        page_canvas.setFont("HeiseiMin-W3", 12)
        page_canvas.drawCentredString(start_x_center, height - 360, f"{datenow}")

        page_canvas.setFont("HeiseiMin-W3", 16)
        page_canvas.drawCentredString(start_x_center, height - 385, "株式会社ユークリッド・エージェンシー")

        page_canvas.showPage()

        page_canvas.setFont("HeiseiMin-W3", 16)
        page_canvas.setFillColor(colors.black)
        page_canvas.drawCentredString(start_x_center, height, f"『{report_data.artist.artist_name}』収支報告")

        page_canvas.setFont("HeiseiMin-W3", 10)
        page_canvas.drawString(start_x0, height - 30, f"{company_name}　御中")
        page_canvas.line(start_x0, height - 35, 200, height - 35)
        page_canvas.drawString(start_x0, height - 45, f"Register No：{register_number}")

        page_canvas.drawRightString(start_x0 + 525, height - 30, f"対象期間：{period_start}〜{period_end}")
        page_canvas.drawRightString(start_x0 + 525, height - 45, f"締め日：{report_data.period_end.strftime('%Y年%m月%d日')}")
        page_canvas.drawRightString(start_x0 + 525, height - 60, f"報告書：{report_date}")
        page_canvas.drawRightString(start_x0 + 525, height - 75, f"支払予定日：{report_data.pay_date.strftime('%Y年%m月%d日')}")

        """
        Fill table Silkroad Store
        """
        silk_road_product_table_data = [
            ["商品コード", "商品名", "出荷数（戻り数控除済み）", "", "", "", "","単価", "売上金額", "", "", "", "", ""],
            ["", "", "{1st month}月", "{2nd month}月", "{3rd month}月", "販売数合計", "","（税込）", "{1st month}月", "{2nd month}月", "{3rd month}月", "売上金額合計", "販売事業部手数料（30%）", "制作委員会様入金額"],
            # ["{prod code}", "{prod name}", "{GoodSale.1st sale amount}", "{GoodSale.2nd sale amount}", "{GoodSale.3rd sale amount}", "0"],
            # ["", "小計", "0", "0", "0", "0"],
        ]
        build_table_silkroad_store_kwargs = {
            "height_y1": height_y1,
            "silk_road_product_table_data": silk_road_product_table_data,
            "silk_road_table_data_height": height_y1 - 85,
            "total_include_tax": 0,
            "fee_include_tax": 0,
        }
        silk_road_report_builder = committee_report_builder.CommitteeReportBuilder(**build_table_silkroad_store_kwargs)
        if report_data.category == 'GOKU' or report_data.category == 'ふしぎ遊戯':
            silk_road_report_builder.build_table_silkroad_store(sale_good_reports, month_list)

        """
        Fill table Silkroad Store DVD
        """
        silk_road_product_table_data = [
            ["商品コード", "商品名", "出荷数（戻り数控除済み）", "", "", "", "","単価", "売上金額", "", "", "", "", ""],
            ["", "", "{1st month}月", "{2nd month}月", "{3rd month}月", "販売数合計", "","（税込）", "{1st month}月", "{2nd month}月", "{3rd month}月", "売上金額合計", "販売事業部手数料（30%）", "制作委員会様入金額"],
            # ["{prod code}", "{prod name}", "{GoodSale.1st sale amount}", "{GoodSale.2nd sale amount}", "{GoodSale.3rd sale amount}", "0"],
            # ["", "小計", "0", "0", "0", "0"],
        ]
        build_table_silkroad_store_dvd_kwargs = {
            "height_y1": height_y1,
            "silk_road_product_table_data": silk_road_product_table_data,
            "silk_road_table_data_height": height_y1 - 85,
            "total_include_tax": 0,
            "fee_include_tax": 0,
        }
        silk_road_dvd_report_builder = committee_report_builder.CommitteeReportBuilder(**build_table_silkroad_store_dvd_kwargs)
        if report_data.category == 'GOKU' or report_data.category == 'ふしぎ遊戯' or report_data.category == '犬夜叉':
            silk_road_dvd_report_builder.build_table_silkroad_store_dvd(sale_good_reports, month_list)

        """
        Fill table Amazon DVD
        """
        amazon_product_table_data = [
            ["商品コード", "商品名", "出荷数（戻り数控除済み）", "", "", "", "","単価", "売上金額", "", "", "", "", ""],
            ["", "", "{1st month}月", "{2nd month}月", "{3rd month}月", "販売数合計", "","（税込）", "{1st month}月", "{2nd month}月", "{3rd month}月", "売上金額合計", "販売事業部手数料（30%）", "制作委員会様入金額"],
            # ["{prod code}", "{prod name}", "{GoodSale.1st sale amount}", "{GoodSale.2nd sale amount}", "{GoodSale.3rd sale amount}", "0"],
            # ["", "小計", "0", "0", "0", "0"],
        ]
        build_table_amazon_dvd_kwargs = {
            "height_y1": height_y1,
            "amazon_product_table_data": amazon_product_table_data,
            "amazon_table_data_height": height_y1 - 85,
            "total_include_tax": 0,
            "fee_include_tax": 0,
        }
        amazon_dvd_report_builder = committee_report_builder.CommitteeReportBuilder(**build_table_amazon_dvd_kwargs)
        if report_data.category == 'GOKU' or report_data.category == 'ふしぎ遊戯':
            amazon_dvd_report_builder.build_table_amazon_dvd(sale_good_reports, month_list)

        """
        Fill table Archive GOKU
        """
        goku_archive_table_data = [
            ["対象月", "品名", "件数", "売上金額", "料率（％）", "支払額",],
        ]
        build_table_archive_goku_kwargs = {
            "height": height,
            "goku_archive_table_data": goku_archive_table_data,
            "archive_table_data_height": height - 140,
            "goku_total_payment": 0,
            "goku_total_sales": 0,
            "archive_streaming_total_sales": 0,
        }
        archive_goku_report_builder = committee_report_builder.CommitteeReportBuilder(**build_table_archive_goku_kwargs)
        archive_streaming_goku = ReportArchiveStreaming.objects.filter(
            committee_report=report_data, category='GOKU'
        )
        if (report_data.category == 'GOKU' or report_data.category == 'ふしぎ遊戯') and archive_streaming_goku.exists():
            archive_goku_report_builder.build_table_archive_goku(archive_streaming_goku)

        """
        Fill table Rakuten Showtime
        """
        rakuten_showtime_table_data = [
            ["配信月", "販売数", "",],
        ]
        build_table_rakuten_showtime_kwargs = {
            "height": height,
            "rakuten_showtime_table_data": rakuten_showtime_table_data,
            "rakuten_table_data_height": height - 150,
            "rakuten_showtime_sale_amount": [0,0,0],
            "rakuten_showtime_sale_price": [0,0,0],
            "rakuten_showtime_carryover_total": 0,
            "rakuten_showtime_total": 0,
        }
        rakuten_showtime_report_builder = committee_report_builder.CommitteeReportBuilder(**build_table_rakuten_showtime_kwargs)
        rakuten_showtime = ReportRakutenStreamService.objects.filter(
            committee_report=report_data, category='楽天ShowTime'
        )
        if (report_data.category == 'GOKU' or report_data.category == 'ふしぎ遊戯'):
            rakuten_showtime_report_builder.build_table_rakuten_showtime(rakuten_showtime, month_list)

        """
        Fill table Rakuten TV
        """
        rakuten_tv_table_data = [
            ["配信月", "販売数", "",],
        ]
        build_table_rakuten_tv_kwargs = {
            "height": height,
            "rakuten_tv_table_data": rakuten_tv_table_data,
            "rakuten_table_data_height": height - 175,
            "rakuten_tv_sale_amount": [0,0,0],
            "rakuten_tv_sale_price": [0,0,0],
            "rakuten_tv_carryover_total": 0,
            "rakuten_tv_total": 0,
        }
        rakuten_tv_report_builder = committee_report_builder.CommitteeReportBuilder(**build_table_rakuten_tv_kwargs)
        rakuten_tv = ReportRakutenStreamService.objects.filter(
            committee_report=report_data, category='Rakuten TV'
        )
        if (report_data.category == 'GOKU' or report_data.category == 'ふしぎ遊戯'):
            rakuten_tv_report_builder.build_table_rakuten_tv(rakuten_tv, month_list)

        """
        income info table
        """
        silkroad_store_income = silk_road_report_builder.kwargs.get('total_include_tax', 0)
        silkroad_store_dvd_income = silk_road_dvd_report_builder.kwargs.get('total_include_tax', 0)
        amazon_dvd_income = amazon_dvd_report_builder.kwargs.get('total_include_tax', 0)
        archive_goku_income = archive_goku_report_builder.kwargs.get('goku_total_payment', 0)
        rakuten_program_income = rakuten_showtime_report_builder.kwargs.get('rakuten_showtime_total', 0) + rakuten_tv_report_builder.kwargs.get('rakuten_tv_total', 0)
        total_all_income = silkroad_store_income + silkroad_store_dvd_income + amazon_dvd_income + archive_goku_income + rakuten_program_income
        page_canvas.drawString(start_x0, height - 110, f"■収入")
        income_info_table_data = [
            ["Silkroad Store グッズ売上", f"{utils.format_currency_value(silkroad_store_income)}", "Silkroad Store グッズ売上明細 参照"],
            ["Silkroad Store DVD売上", f"{utils.format_currency_value(silkroad_store_dvd_income)}", "Silkroad Store DVD売上明細　参照"],
            ["amazon DVD売上", f"{utils.format_currency_value(amazon_dvd_income)}", "amazon 売上明細　参照"],
            ["DMM　アーカイブ配信", f"{utils.format_currency_value(archive_goku_income)}", "アーカイブ配信詳細 参照"],
            ["楽天　動画配信　", f"{utils.format_currency_value(rakuten_program_income)}", "楽天動画明細　参照"],
            ["小計①", f"{utils.format_currency_value(total_all_income)}", "10%対象"],
        ]

        income_info_table = Table(income_info_table_data, colWidths=[175, 75, 175])
        style = [
            ("FONT", (0, 0), (-1, -1), "HeiseiMin-W3", 10),
            ('LINEBELOW', (0, -2), (-1, -2), 1, colors.black),
            ("ALIGN", (1, 1), (1, -1), "LEFT"),
            ('GRID', (0,0), (-1,-1), 0.25, colors.black),
        ]
        income_info_table.setStyle(TableStyle(style))
        income_info_table.wrapOn(page_canvas, start_x0 + 65, height - 210)
        income_info_table.drawOn(page_canvas, start_x0 + 65, height - 210)

        """
        expense info table
        """
        silkroad_store_expense = silk_road_report_builder.kwargs.get('fee_include_tax', 0)
        silkroad_store_dvd_expense = silk_road_dvd_report_builder.kwargs.get('fee_include_tax', 0)
        amazon_dvd_expense = amazon_dvd_report_builder.kwargs.get('fee_include_tax', 0)
        other_dvd_expense = report_data.royalty_use_fee
        total_all_expense = silkroad_store_expense + silkroad_store_dvd_expense + amazon_dvd_expense + other_dvd_expense
        page_canvas.drawString(start_x0, height - 260, f"■支出")
        expense_info_table_data = [
            ["Silkroad Store グッズ販売手数料", f"{utils.format_currency_value(silkroad_store_expense)}", "Silkroad Store 売上明細　参照"],
            ["Silkroad Store DVD販売手数料", f"{utils.format_currency_value(silkroad_store_dvd_expense)}", "Silkroad Store 売上明細　参照"],
            ["amazon DVD販売手数料", f"{utils.format_currency_value(amazon_dvd_expense)}", "amazon 売上明細　参照"],
            ["DVD制作費", f"{utils.format_currency_value(other_dvd_expense)}", "DVD制作費明細　参照"],
            ["小計②", f"{utils.format_currency_value(total_all_expense)}", "10%対象"],
        ]

        expense_info_table = Table(expense_info_table_data, colWidths=[175, 75, 175])
        style = [
            ("FONT", (0, 0), (-1, -1), "HeiseiMin-W3", 10),
            ('LINEBELOW', (0, -2), (-1, -2), 1, colors.black),
            ("ALIGN", (1, 1), (1, -1), "LEFT"),
            ('GRID', (0,0), (-1,-1), 0.25, colors.black),
        ]
        expense_info_table.setStyle(TableStyle(style))
        expense_info_table.wrapOn(page_canvas, start_x0 + 65, height - 340)
        expense_info_table.drawOn(page_canvas, start_x0 + 65, height - 340)

        """
        balance info table
        """
        total_balance_amount = total_all_income - total_all_expense
        total_net_balance = utils.rounddown(total_balance_amount / 1.1)
        consumption_tax_balance = utils.rounddown(total_balance_amount * 0.1)
        page_canvas.drawString(start_x0, height - 390, f"■収支")
        balance_info_table_data = [
            ["①-②（税抜き金額）", f"{utils.format_currency_value(total_net_balance)}", "10%対象"],
            ["消費税", f"{utils.format_currency_value(consumption_tax_balance)}", "10%"],
            ["支払い金額合計", f"{utils.format_currency_value(total_balance_amount)}", ""],
        ]

        balance_info_table = Table(balance_info_table_data, colWidths=[175, 75, 175])
        style = [
            ("FONT", (0, 0), (-1, -1), "HeiseiMin-W3", 10),
            ('LINEBELOW', (0, -2), (-1, -2), 1, colors.black),
            ("ALIGN", (1, 1), (1, -1), "LEFT"),
            ('GRID', (0,0), (-1,-1), 0.25, colors.black),
        ]
        balance_info_table.setStyle(TableStyle(style))
        balance_info_table.wrapOn(page_canvas, start_x0 + 65, height - 435)
        balance_info_table.drawOn(page_canvas, start_x0 + 65, height - 435)

        page_canvas.drawRightString(start_x0 + 525, height - 510, f"株式会社ユークリッド・エージェンシー")

        page_canvas.showPage()

        # start page 3 Silkroad Store
        if report_data.category == 'GOKU' or report_data.category == 'ふしぎ遊戯':
            page_canvas.setPageSize(landscape(A4))
            page_canvas.setFont("HeiseiMin-W3", 10)
            page_canvas.setFillColor(colors.black)

            page_canvas.drawString(start_x1, height_y1, f"■Silkroad Store Good Sale Slip　{period_start}〜{period_end}")

            """
            sale good silkroad product table
            """
            silk_road_product_table = Table(silk_road_report_builder.kwargs.get('silroad_product_table_data'), colWidths=[75, 150, 25, 25, 25, 35, 5, 40, 25, 25, 25, 60, 70, 60])
            style = [
                ("FONT", (0, 0), (-1, -1), "HeiseiMin-W3", 5),
                ("BACKGROUND", (0, 0), (5, 1), "#DCDCDC"),
                ("ALIGN", (1, 1), (1, -1), "LEFT"),
                ('SPAN', (0,0),(0,1),),
                ('SPAN', (1,0),(1,1),),
                ('SPAN', (2,0),(4,0),),
                ('GRID', (0,0), (-9,1), 0.25, colors.black),
                ('GRID', (0,1), (-9,-5), 0.25, colors.black),
                ('GRID', (1,-4), (-9,-4), 0.25, colors.black),

                ("BACKGROUND", (7, 0), (-4, 0), "#DCDCDC"),
                ("BACKGROUND", (7, 1), (-1, 1), "#DCDCDC"),
                ('SPAN', (8,0),(-4,0),),
                ('GRID', (7,0), (-4,0), 0.25, colors.black),
                ('GRID', (7,1), (-1,1), 0.25, colors.black),
                ('GRID', (7,2), (-1,-5), 0.25, colors.black),
                ('GRID', (8,-4), (-1,-4), 0.25, colors.black),

                ('GRID', (-4,-2), (-2,-1), 0.25, colors.black),
            ]
            silk_road_product_table.setStyle(TableStyle(style))
            silk_road_product_table.wrapOn(page_canvas, start_x1, silk_road_report_builder.kwargs.get('silk_road_table_data_height'))
            silk_road_product_table.drawOn(page_canvas, start_x1, silk_road_report_builder.kwargs.get('silk_road_table_data_height'))

            footer_info_height = height_y1 - 250

            page_canvas.drawString(start_x1 + 125, footer_info_height - 150, f"※送料・システム管理料などを含む")
            if report_data.gacha_information.exists():
                yutaka_price = GachaInformation.objects.filter(commitee_report=report_data).aggregate(
                    yutaka_price_1=Sum('sale_amount'),
                    yutaka_price_2=Sum('related_product'),
                    yutaka_price_3=Sum('badge_amount'),
                    yutaka_price_4=Sum('sticker_amount'),
                    yutaka_price_5=Sum('instax_amount')
                )
                # page_canvas.drawRightString(start_x1 + 770, footer_info_height - 165, f"【ガチャ販売総数（7〜9月分】　　　　　　　　　0個")
                page_canvas.drawRightString(start_x1 + 770, footer_info_height - 180, f"　　 内、喜矢武豊関連商品　　　{utils.format_currency_value(yutaka_price.get('yutaka_price_1'))}個")
                page_canvas.drawRightString(start_x1 + 770, footer_info_height - 195, f"《割合》")

                """
                sale good other info table
                """
                silk_road_other_info_table_data = [
                    ["喜矢武缶バッジ", "", f"{utils.format_currency_value(yutaka_price.get('yutaka_price_2'))} 個"],
                    ["ステッカー2種分", "", f"{utils.format_currency_value(yutaka_price.get('yutaka_price_3'))} 個"],
                    ["喜矢武チェキ", "", f"{utils.format_currency_value(yutaka_price.get('yutaka_price_4'))} 個"],
                ]

                silk_road_other_info_table = Table(silk_road_other_info_table_data)
                style = [
                    ("FONT", (0, 0), (-1, -1), "HeiseiMin-W3", 8),
                    # ("BACKGROUND", (0, 0), (-1, 0), "#DCDCDC"),
                    ("ALIGN", (1, 1), (1, -1), "LEFT"),
                    ('BOX', (0,0), (-1,-1), 0.25, colors.black),
                    ('LINEBELOW', (0,0), (-1,-1), 0.25, colors.black),
                ]
                silk_road_other_info_table.setStyle(TableStyle(style))
                silk_road_other_info_table.wrapOn(page_canvas, start_x1 + 640, footer_info_height - 250)
                silk_road_other_info_table.drawOn(page_canvas, start_x1 + 640, footer_info_height - 250)

            page_canvas.drawRightString(start_x1 + 770, footer_info_height - 285, f"{report_date}")
            page_canvas.drawRightString(start_x1 + 770, footer_info_height - 300, f"株式会社ユークリッド・エージェンシー")

            page_canvas.showPage()


        # start page 4 Silkroad Store DVD
        if report_data.category == 'GOKU' or report_data.category == 'ふしぎ遊戯' or report_data.category == '犬夜叉':
            # page_canvas.setPageSize(landscape(A4))
            page_canvas.setFont("HeiseiMin-W3", 10)
            page_canvas.setFillColor(colors.black)

            page_canvas.drawString(start_x1, height_y1, f"■「Silkroad Store」 「DVD」 Sale Slip　{period_start}〜{period_end}")

            """
            sale good silkroad product table
            """
            silk_road_product_table = Table(silk_road_dvd_report_builder.kwargs.get('silroad_product_table_data'), colWidths=[75, 150, 25, 25, 25, 35, 5, 40, 25, 25, 25, 60, 70, 60])
            style = [
                ("FONT", (0, 0), (-1, -1), "HeiseiMin-W3", 5),
                ("BACKGROUND", (0, 0), (5, 1), "#DCDCDC"),
                ("ALIGN", (1, 1), (1, -1), "LEFT"),
                ('SPAN', (0,0),(0,1),),
                ('SPAN', (1,0),(1,1),),
                ('SPAN', (2,0),(4,0),),
                ('GRID', (0,0), (-9,1), 0.25, colors.black),
                ('GRID', (0,1), (-9,-5), 0.25, colors.black),
                ('GRID', (1,-4), (-9,-4), 0.25, colors.black),

                ("BACKGROUND", (7, 0), (-4, 0), "#DCDCDC"),
                ("BACKGROUND", (7, 1), (-1, 1), "#DCDCDC"),
                ('SPAN', (8,0),(-4,0),),
                ('GRID', (7,0), (-4,0), 0.25, colors.black),
                ('GRID', (7,1), (-1,1), 0.25, colors.black),
                ('GRID', (7,2), (-1,-5), 0.25, colors.black),
                ('GRID', (8,-4), (-1,-4), 0.25, colors.black),

                ('GRID', (-4,-2), (-2,-1), 0.25, colors.black),
            ]
            silk_road_product_table.setStyle(TableStyle(style))
            silk_road_product_table.wrapOn(page_canvas, start_x1, silk_road_dvd_report_builder.kwargs.get('silk_road_table_data_height'))
            silk_road_product_table.drawOn(page_canvas, start_x1, silk_road_dvd_report_builder.kwargs.get('silk_road_table_data_height'))

            footer_info_height = height_y1 - 250
            page_canvas.drawString(start_x1 + 125, footer_info_height - 150, f"※送料・システム管理料などを含む")

            page_canvas.drawRightString(start_x1 + 770, footer_info_height - 285, f"{report_date}")
            page_canvas.drawRightString(start_x1 + 770, footer_info_height - 300, f"株式会社ユークリッド・エージェンシー")

            page_canvas.showPage()

        # start page 5 Amazon DVD
        if report_data.category == 'GOKU' or report_data.category == 'ふしぎ遊戯':
            # page_canvas.setPageSize(landscape(A4))
            page_canvas.setFont("HeiseiMin-W3", 10)
            page_canvas.setFillColor(colors.black)

            page_canvas.drawString(start_x1, height_y1, f"■amazon DVD Sale slip　{period_start}〜{period_end}")

            """
            sale good amazon product table
            """
            amazon_product_table = Table(amazon_dvd_report_builder.kwargs.get('amazon_product_table_data'), colWidths=[75, 150, 25, 25, 25, 35, 5, 40, 25, 25, 25, 60, 70, 60])
            style = [
                ("FONT", (0, 0), (-1, -1), "HeiseiMin-W3", 5),
                ("BACKGROUND", (0, 0), (5, 1), "#DCDCDC"),
                ("ALIGN", (1, 1), (1, -1), "LEFT"),
                ('SPAN', (0,0),(0,1),),
                ('SPAN', (1,0),(1,1),),
                ('SPAN', (2,0),(4,0),),
                ('GRID', (0,0), (-9,1), 0.25, colors.black),
                ('GRID', (0,1), (-9,-5), 0.25, colors.black),
                ('GRID', (1,-4), (-9,-4), 0.25, colors.black),

                ("BACKGROUND", (7, 0), (-4, 0), "#DCDCDC"),
                ("BACKGROUND", (7, 1), (-1, 1), "#DCDCDC"),
                ('SPAN', (8,0),(-4,0),),
                ('GRID', (7,0), (-4,0), 0.25, colors.black),
                ('GRID', (7,1), (-1,1), 0.25, colors.black),
                ('GRID', (7,2), (-1,-5), 0.25, colors.black),
                ('GRID', (8,-4), (-1,-4), 0.25, colors.black),

                ('GRID', (-4,-2), (-2,-1), 0.25, colors.black),
            ]
            amazon_product_table.setStyle(TableStyle(style))
            amazon_product_table.wrapOn(page_canvas, start_x1, amazon_dvd_report_builder.kwargs.get('amazon_table_data_height'))
            amazon_product_table.drawOn(page_canvas, start_x1, amazon_dvd_report_builder.kwargs.get('amazon_table_data_height'))

            footer_info_height = height_y1 - 250
            page_canvas.drawString(start_x1 + 125, footer_info_height - 150, f"※送料・システム管理料などを含む")
            page_canvas.drawRightString(start_x1 + 770, footer_info_height - 285, f"{report_date}")
            page_canvas.drawRightString(start_x1 + 770, footer_info_height - 300, f"株式会社ユークリッド・エージェンシー")

            page_canvas.showPage()

        # start page 6 Archive Streaming Distribution
        archive_streaming_other = ReportArchiveStreaming.objects.filter(
            committee_report=report_data, category='ふしぎ遊戯'
        )
        if (report_data.category == 'GOKU' or report_data.category == 'ふしぎ遊戯') and (archive_streaming_goku.exists() or archive_streaming_other.exists()):
            page_canvas.setPageSize(portrait(A4))
            page_canvas.setFont("HeiseiMin-W3", 10)
            page_canvas.setFillColor(colors.black)

            page_canvas.drawCentredString(start_x_center, height, f"アーカイブ配信明細")
            goku_total_sales = archive_goku_report_builder.kwargs.get('goku_total_sales', 0)
            archive_streaming_total_sales = archive_goku_report_builder.kwargs.get('archive_streaming_total_sales', 0)
            page_canvas.drawString(start_x0, height - 25, f"■合計金額")

            # page_canvas.drawString(start_x0 + 85, height - 25, f"{utils.format_currency_value(0)}")
            page_canvas.line(start_x0 + 85, height - 30, 200, height - 30)

            page_canvas.drawString(start_x0 + 235, height - 25, f"『{report_data.category}』Sales")
            # page_canvas.drawString(start_x0 + 335, height - 25, f"{utils.format_currency_value(goku_total_sales)}")
            page_canvas.line(start_x0 + 235, height - 30, 450, height - 30)

            page_canvas.drawString(start_x0, height - 50, f"内訳")

            """
            GOKU archive streaming table
            """
            page_canvas.drawString(start_x0, height - 60, f"◆『GOKÛ』")
            page_canvas.drawString(start_x0 + 335, height - 25, f"{utils.format_currency_value(goku_total_sales)}")

            goku_archive_table = Table(archive_goku_report_builder.kwargs.get('goku_archive_table_data'))
            style = [
                ("FONT", (0, 0), (-1, -1), "HeiseiMin-W3", 10),
                ("BACKGROUND", (0, 0), (-1, 0), "#DCDCDC"),
                ("ALIGN", (1, 1), (1, -1), "LEFT"),
                ('GRID', (0,0), (-1,-3), 0.25, colors.black),
                ('GRID', (4,-1), (-1,-1), 0.25, colors.black),
            ]
            goku_archive_table.setStyle(TableStyle(style))
            goku_archive_table.wrapOn(page_canvas, start_x0 + 70, archive_goku_report_builder.kwargs.get('archive_table_data_height'))
            goku_archive_table.drawOn(page_canvas, start_x0 + 70, archive_goku_report_builder.kwargs.get('archive_table_data_height'))

            """
            ふしぎ遊戯 archive streaming table
            """
            page_canvas.drawString(start_x0, height - 170, f"◆『ふしぎ遊戯』")

            other_archive_table_data = [
                ["対象月", "品名", "件数", "売上金額", "料率（％）", "支払額",],
            ]
            other_total_payment = 0
            archive_table_data_height = height - 250
            for archive in archive_streaming_other:
                other_data = []
                other_data.append(archive.month.strftime("%B"))
                other_data.append(archive.product_detail.product_name)
                other_data.append(utils.format_amount(archive.amount))
                other_data.append(utils.format_currency_value(archive.sales))
                other_data.append(utils.format_percentage(archive.rate))
                other_data.append(utils.format_currency_value(archive.total_payment))

                other_total_payment += archive.total_payment
                archive_streaming_total_sales += archive.sales

                other_archive_table_data.append(other_data)
                archive_table_data_height -= 10

            other_archive_table_data.append(["", "", "", "", "", ""])
            other_archive_table_data.append(["", "", "", "", "税抜き金額", f"{utils.format_currency_value(other_total_payment)}"])

            other_archive_table = Table(other_archive_table_data)
            style = [
                ("FONT", (0, 0), (-1, -1), "HeiseiMin-W3", 10),
                ("BACKGROUND", (0, 0), (-1, 0), "#DCDCDC"),
                ("ALIGN", (1, 1), (1, -1), "LEFT"),
                ('GRID', (0,0), (-1,-3), 0.25, colors.black),
                ('GRID', (4,-1), (-1,-1), 0.25, colors.black),
            ]
            other_archive_table.setStyle(TableStyle(style))
            other_archive_table.wrapOn(page_canvas, start_x0 + 70, archive_table_data_height)
            other_archive_table.drawOn(page_canvas, start_x0 + 70, archive_table_data_height)

            page_canvas.drawString(start_x0 + 85, height - 25, f"{utils.format_currency_value(archive_streaming_total_sales)}")

            archive_report_desc = [
                f"※DMM様より『{report_data.category}』と『ふしぎ遊戯』合わせてご報告頂きましたので、両舞台とも記載致しましたが",
                f"　この報告書にて収入と致しますのは、黄色く色付け致しました『{report_data.category}』分のみとなります。",
            ]
            if report_data.archive_description:
                archive_report_desc = parser.parse(report_data.archive_description)

            increment_archive_report_desc = 370
            for data in archive_report_desc:
                page_canvas.drawString(start_x0, archive_table_data_height - increment_archive_report_desc, data)
                increment_archive_report_desc += 12

            # page_canvas.drawString(start_x0, archive_table_data_height - 370, f"※DMM様より『{report_data.category}』と『ふしぎ遊戯』合わせてご報告頂きましたので、両舞台とも記載致しましたが")
            # page_canvas.drawString(start_x0, archive_table_data_height - 380, f"　この報告書にて収入と致しますのは、黄色く色付け致しました『{report_data.category}』分のみとなります。")

            page_canvas.drawRightString(start_x0 + 525, archive_table_data_height - 435, f"{report_date}")
            page_canvas.drawRightString(start_x0 + 525, archive_table_data_height - 445, f"株式会社ユークリッド・エージェンシー")

            page_canvas.showPage()

        # start page 7 Rakuten Streaming Distribution
        if (report_data.category == 'GOKU' or report_data.category == 'ふしぎ遊戯') and (rakuten_showtime.exists() or rakuten_tv.exists()):
            page_canvas.setPageSize(portrait(A4))
            page_canvas.setFont("HeiseiMin-W3", 10)
            page_canvas.setFillColor(colors.black)

            page_canvas.drawCentredString(start_x_center, height, f"『GOKÛ』")
            page_canvas.drawCentredString(start_x_center, height - 25, f"楽天 動画配信サービス")

            """
            Rakuten Show Time table
            """
            page_canvas.drawString(start_x0, height - 60, f"■楽天ShowTime")
            rakuten_showtime_table = Table(rakuten_showtime_report_builder.kwargs.get('rakuten_showtime_table_data'), colWidths=[75, 100, 100])
            style = [
                ("FONT", (0, 0), (-1, -1), "HeiseiMin-W3", 10),
                ("BACKGROUND", (0, 0), (-1, 0), "#DCDCDC"),
                ("ALIGN", (1, 1), (1, -1), "LEFT"),
                ("SPAN", (0, -1), (1, -1),),
                ("SPAN", (0, -2), (1, -2),),
                ('GRID', (0,0), (-1,-1), 0.25, colors.black),
            ]
            rakuten_showtime_table.setStyle(TableStyle(style))
            rakuten_showtime_table.wrapOn(page_canvas, start_x0 + 90, rakuten_showtime_report_builder.kwargs.get('rakuten_table_data_height') + 20)
            rakuten_showtime_table.drawOn(page_canvas, start_x0 + 90, rakuten_showtime_report_builder.kwargs.get('rakuten_table_data_height') + 20)

            """
            Rakuten TV table
            """
            page_canvas.drawString(start_x0, rakuten_showtime_report_builder.kwargs.get('rakuten_table_data_height') - 30, f"■Rakuten TV")
            rakuten_tv_table_data = rakuten_tv_report_builder.kwargs.get('rakuten_tv_table_data')
            total_sale_price = rakuten_showtime_report_builder.kwargs.get('rakuten_showtime_total') + rakuten_tv_report_builder.kwargs.get('rakuten_tv_total')
            total_carryover = rakuten_showtime_report_builder.kwargs.get('rakuten_showtime_carryover_total') + rakuten_tv_report_builder.kwargs.get('rakuten_tv_carryover_total')
            total_accumulation = 0
            if total_sale_price > 5000:
                total_accumulation = total_sale_price
            # rakuten_tv_table_data.append(["", "", ""])
            rakuten_tv_table_data.append(["", "", ""])
            rakuten_tv_table_data.append(["", "合計", f"{utils.format_currency_value(total_sale_price)}"])
            rakuten_tv_table_data.append(["", "支払い額", f"{utils.format_currency_value(total_accumulation)}"])

            rakuten_tv_table = Table(rakuten_tv_table_data, colWidths=[75, 100, 100])
            style = [
                ("FONT", (0, 0), (-1, -1), "HeiseiMin-W3", 10),
                ("BACKGROUND", (0, 0), (-1, 0), "#DCDCDC"),
                ("ALIGN", (1, 1), (1, -1), "LEFT"),
                ("SPAN", (0, -4), (1, -4),),
                ("SPAN", (0, -5), (1, -5),),
                ('GRID', (0,0), (-1,-4), 0.25, colors.black),
                ('GRID', (1,-2), (-1,-1), 0.25, colors.black),
            ]
            rakuten_tv_table.setStyle(TableStyle(style))
            rakuten_tv_table.wrapOn(page_canvas, start_x0 + 90, rakuten_tv_report_builder.kwargs.get('rakuten_table_data_height') + 35)
            rakuten_tv_table.drawOn(page_canvas, start_x0 + 90, rakuten_tv_report_builder.kwargs.get('rakuten_table_data_height') + 35)

            page_canvas.drawString(start_x0, rakuten_tv_report_builder.kwargs.get('rakuten_table_data_height') - 90, f"■楽天Show TimeとRakutenTVは¥5,000未満のため、楽天株式会社に請求書を発行しておりません。")
            page_canvas.drawRightString(start_x0 + 525, rakuten_tv_report_builder.kwargs.get('rakuten_table_data_height') - 125, f"{report_date}")
            page_canvas.drawRightString(start_x0 + 525, rakuten_tv_report_builder.kwargs.get('rakuten_table_data_height') - 140, f"株式会社ユークリッド・エージェンシー")

            page_canvas.showPage()

        # start page 8
        page_canvas.setPageSize(portrait(A4))
        page_canvas.setFont("HeiseiMin-W3", 10)
        page_canvas.setFillColor(colors.black)

        page_canvas.drawCentredString(start_x_center, height, f"『{report_data.artist.artist_name}』")
        page_canvas.drawCentredString(start_x_center, height - 25, f"DVD制作費用明細")

        page_canvas.drawRightString(start_x0 + 525, height - 50, f"{report_data.period_end.strftime('%Y-%m')} 月末締{report_data.report_date.strftime('%Y-%m')}月報告分")
        """
        expense info table
        """
        page_canvas.drawString(start_x0, height - 75, f"■内訳")
        royalty_fee_info_table_data = [
            ["著作権使用料", f"{report_data.payslip_number}", f"{utils.format_currency_value(report_data.royalty_use_fee)}"],
            ["制作費合計", "", f"{utils.format_currency_value(utils.rounddown((report_data.royalty_use_fee * 1.1)))}"],
            ["消費税(10%)", "", f"{utils.format_currency_value(utils.rounddown((report_data.royalty_use_fee * 0.1)))}"],
            ["合計", "", f"{utils.format_currency_value(report_data.royalty_use_fee)}"],
        ]

        royalty_fee_info_table = Table(royalty_fee_info_table_data)
        style = [
            ("FONT", (0, 0), (-1, -1), "HeiseiMin-W3", 10),
            ("BACKGROUND", (0, 0), (-1, 0), "#DCDCDC"),
            ("ALIGN", (1, 1), (1, -1), "LEFT"),
            ('GRID', (0,0), (-1,-1), 0.25, colors.black),
        ]
        royalty_fee_info_table.setStyle(TableStyle(style))
        royalty_fee_info_table.wrapOn(page_canvas, start_x0 + 65, height - 140)
        royalty_fee_info_table.drawOn(page_canvas, start_x0 + 65, height - 140)

        page_canvas.drawRightString(start_x0 + 525, height - 150, f"※金額は全て税込")
        page_canvas.drawRightString(start_x0 + 525, height - 325, f"{report_date}")
        page_canvas.drawRightString(start_x0 + 525, height - 335, f"株式会社ユークリッド・エージェンシー")

        page_canvas.showPage()

        # save pdf
        page_canvas.save()

        pdf_buffer.seek(0)
        pdf_value = pdf_buffer.getvalue()
        pdf_file = ContentFile(pdf_value)
        # pdf_response = FileResponse(pdf_value, filename=f"{filename}.pdf")

        report_data.file_name = f"{filename}.pdf"
        if report_data.report_file:
            report_data.report_file.delete()
        report_data.report_file.save(f"{filename}.pdf", pdf_file, save=False)
        report_data.file_name = report_data.report_file_name()
        report_data.calculation_status = 'done'
        report_data.save()
        pdf_buffer.close()
    except Exception as e:
        logger.error(f"Error in generating report: {e}")
        report_data.calculation_status = 'error'
        report_data.save()
        raise e

def export_sale_data(target_date: datetime, user_email: str):
    sale_good = SaleGoodDetail.objects.filter(
        Q(target_month__year=target_date.year) & Q(target_month__month=target_date.month)
         & Q(is_delete=False)
    )
    if not sale_good:
        logger.error(f"No sale data found for {target_date}")

    data_dict_of_lists = {}
    product_id_list = sale_good.values('product_detail__product_id').distinct()
    unique_product_id_list = list(set([product['product_detail__product_id'] for product in product_id_list]))
    # Filter ProductDetail objects based on product_id_list
    product_details = ProductDetail.objects.filter(product_id__in=unique_product_id_list).distinct()

    # Convert the queryset to a dictionary
    product_details_dict = {product.product_id: product for product in product_details}
    data_dict_of_lists['product_categorygroup_name '] = [product_details_dict.get(product_id).product_category_group.name if product_details_dict.get(product_id) else
                                          '-' for product_id in product_details_dict.keys()]
    data_dict_of_lists['product_id'] = [product_id for product_id in product_details_dict.keys()]
    data_dict_of_lists['product_name'] = [product_details_dict.get(product_id).product_name if product_details_dict.get(product_id) else
                                          '-' for product_id in product_details_dict.keys()]

    report_type_list = SaleGoodDetail.objects.filter(Q(target_month__month=target_date.month) & Q(target_month__year=target_date.year) & Q(is_delete=False)) \
        .values('report_type').order_by().distinct()
    total_amount_per_product = {product_id: 0 for product_id, _ in product_details_dict.items()}
    for report_type in report_type_list:
        report_type = report_type.get('report_type')
        data_dict_of_lists[report_type] = []
        for product in product_details_dict.keys():
            if report_type.lower() == 'daiki':
                sale_data = sale_good.filter(
                    Q(report_type=report_type) & Q(product_detail__product_id=product)
                    & Q(product_detail__is_delete=False)
                ).select_related('product_detail').aggregate(
                    total_sales=Sum(
                        ExpressionWrapper(
                            Cast('product_detail__price', models.IntegerField()) * Cast('quantity', models.IntegerField()),
                            output_field=models.IntegerField()
                        )
                    )
                )
            else:
                sale_data = sale_good.filter(
                    Q(report_type=report_type) & Q(product_detail__product_id=product)
                    & Q(product_detail__is_delete=False)
                ).aggregate(
                    total_sales=Sum(ExpressionWrapper(
                            Cast('original_sale', models.IntegerField()),
                            output_field=models.IntegerField()
                        ))
                )
            if sale_data.get('total_sales'):
                data_dict_of_lists[report_type].append(sale_data.get('total_sales'))
                total_amount_per_product[product] += sale_data.get('total_sales')
            else:
                data_dict_of_lists[report_type].append(0)

    data_dict_of_lists['合計金額'] = [total_amount_per_product.get(product_id) for product_id in product_details_dict.keys()]
    df_from_dict = pd.DataFrame(data_dict_of_lists)

    buffer = BytesIO()
    writer = pd.ExcelWriter(buffer, engine='xlsxwriter', engine_kwargs={'options': {'strings_to_numbers': True}})
    # df_from_dict.to_excel('sale_good_export.xlsx', index=False)
    df_from_dict.T.reset_index().T.to_excel(writer, header=False, index=False)
    # df_from_dict.to_excel(buffer, index=False, engine='openpyxl')
    # buffer.seek(0)
    writer.close()

    send_to = []
    if user_email:
        send_to = [user_email]

    admin_email = User.objects.filter(groups__name__in=['super_admin', 'admin']).values_list('email', flat=True)
    send_to.extend(admin_email)

    usecase.send_email_with_attachment(
        to=list(set(send_to)),
        subject=f"【分配計算システム | 売上データExcel】",
        message=f"売上データのExcelエクスポートが完了しました。\n添付のExcelファイルをご確認ください。",
        attachment=ContentFile(buffer.getvalue(), f"sale_data_{target_date.strftime('%B_%Y')}.xlsx"),
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

def register_product(recipient_data: pd.DataFrame, email: str, filename: str="product.xlsx"):
    current_date = datetime.now().strftime('%Y-%m-%d: %H:%M')
    log_message = f"◯登録日時\n {current_date}\n\n"
    log_message += f"◯ファイル名\n {filename}\n\n"
    log_message += f"◯不具合\n"
    is_error_import = False
    for data in recipient_data.to_dict(orient="records"):
        with transaction.atomic():
            try:
                if not data.get('product_id'):
                    logger.info("Product ID is required.")
                    log_message += f"登録されていない product_id が確認されました\n【{data.get('product_id')}】: {data.get('product_name')}"
                    continue
                else:
                    pass

                if data.get('artist_name'):
                    if not MstArtist.objects.filter(artist_name=data.get('artist_name')).exists():
                        logger.info(f'Artist {data.get("artist_name")} not found.')
                        log_message += f"登録されていない artist_name が確認されました\n【{data.get('product_id')}】: {data.get('product_name')}"
                        continue
                else:
                    logger.info("artist_name is required.")
                    log_message += f"登録されていない artist_name が確認されました\n【{data.get('product_id')}】: {data.get('product_name')}"
                    continue

                if data.get('distribution_pattern_group_id'):
                    if not MstDistributionPatternGroup.objects.filter(group_code=data.get('distribution_pattern_group_id')).exists():
                        logger.info(f'Pattern Group {data.get("distribution_pattern_group_id")} not found.')
                        log_message += f"登録されていない distribution_pattern_group_id が確認されました\n【{data.get('product_id')}】: {data.get('product_name')}"
                        continue
                else:
                    logger.info("distribution_pattern_group_id is required.")
                    log_message += f"登録されていない distribution_pattern_group_id が確認されました\n【{data.get('product_id')}】: {data.get('product_name')}"
                    continue

                if data.get('holding_company_name'):
                    if not MstCompany.objects.filter(company_name=data.get('holding_company_name')).exists():
                        logger.info(f'Holding Company {data.get("holding_company_name")} not found.')
                        log_message += f"登録されていない holding_company_name が確認されました\n【{data.get('product_id')}】: {data.get('product_name')}"
                        continue
                else:
                    logger.info("holding_company_name is required.")
                    log_message += f"登録されていない holding_company_name が確認されました\n【{data.get('product_id')}】: {data.get('product_name')}"
                    continue

                if data.get('segment_id'):
                    if not MstSegment.objects.filter(segment_name=data.get('segment_id')).exists():
                        logger.info(f'Segment {data.get("segment_id")} not found.')
                        log_message += f"登録されていない segment_id が確認されました\n【{data.get('product_id')}】: {data.get('product_name')}"
                        continue
                else:
                    logger.info("segment_id is required.")
                    log_message += f"登録されていない segment_id が確認されました\n【{data.get('product_id')}】: {data.get('product_name')}"
                    continue

                product_category = None
                product_category_note = data.get('product_category_note')
                if not utils.valid_enum(data.get('product_category'), ProductDetail.PRODUCT_CATEGORY_SELECTION):
                    logger.info(f'Product Category {data.get("product_category")} not found.')
                    log_message += f"登録されていない product_category が確認されました\n【{data.get('product_id')}】: {data.get('product_name')}"
                    # product_category_note = data.get('product_category')
                    product_category = None
                else:
                    product_category = data.get('product_category')

                if data.get('tax') and data.get('tax') not in [8, 10]:
                    logger.info(f'Tax value {data.get("tax")} not valid.')
                    log_message += f"登録されていない tax が確認されました\n【{data.get('product_id')}】: {data.get('product_name')}"
                    continue

                media_title = MediaTitle.objects.filter(code=data.get('media_title_code')).first()
                if data.get('media_title_code'):
                    if not media_title:
                        logger.info(f'Media title {data.get("media_title_code")} not found.')
                        log_message += f"登録されていない media_title_code が確認されました\n【{data.get('product_id')}】: {data.get('product_name')}"
                        # continue
                else:
                    logger.info("media_title_code is required.")
                    # log_message += f"登録されていない media_title_code が確認されました\n【{data.get('product_id')}】: {data.get('product_name')}"
                    # continue

                product_category_group = ProductCategoryGroup.objects.filter(name=data.get('product_category_group')).first()
                if data.get('product_category_group'):
                    if not product_category_group:
                        logger.info(f'Media title {data.get("product_category_group")} not found.')
                        log_message += f"登録されていない product_category_group が確認されました\n【{data.get('product_id')}】: {data.get('product_name')}"
                        continue
                else:
                    logger.info("product_category_group is required.")
                    continue

                data, created = ProductDetail.objects.update_or_create(
                    product_id=data.get('product_id'),
                    defaults={
                        'product_name': data.get('product_name'),
                        'artist': MstArtist.objects.filter(artist_name=data.get('artist_name')).first(),
                        'distribution_pattern_group': MstDistributionPatternGroup.objects.filter(group_code=data.get('distribution_pattern_group_id')).first(),
                        'holding_company': MstCompany.objects.filter(company_name=data.get('holding_company_name')).first(),
                        'segment': MstSegment.objects.filter(segment_name=data.get('segment_id')).first(),
                        'price': data.get('price'),
                        'daiki_price': data.get('daiki_price'),
                        'deduction_rate': data.get('deduction'),
                        'product_category': product_category,
                        'product_category_note': product_category_note,
                        'tax': data.get('tax'),
                        'deduction_rate': data.get('deduction_rate'),
                        'issue_date': data.get('issue_date') if data.get('issue_date') else datetime.now(),
                        'media_title': media_title,
                        'shown_report': data.get('shown_report') and 1 == data.get('shown_report'),
                        'product_category_group': product_category_group,
                    }
                )
            except Exception as e:
                logger.info(f"Error in registering product: {e}")
                log_message += f"エラーが発生しました\n【{data.get('product_id')}】: {data.get('product_name')}"
                is_error_import = True

        logger.info(f"registered product: {model_to_dict(instance=data, fields=[field.name for field in data._meta.fields])}")

    if not is_error_import:
        log_message += f"不具合なし\n"

    if email:
        usecase.send_email_to_user(
            to=[email],
            subject=f"【分配計算システム | 商品情報Excel登録結果】",
            message=log_message
        )

def export_product():
    products = ProductDetail.objects.all()
    data_dict_of_products = {
        "product_id": [],
        "media_title_code": [],
        "product_name": [],
        "segment_id": [],
        "distribution_pattern_group_id": [],
        "artist_name": [],
        "daiki_price": [],
        "deduction_rate": [],
        "price": [],
        "product_category": [],
        "product_category_note": [],
        "issue_date": [],
        "tax": [],
        "holding_company_name": [],
        "shown_report": [],
    }
    category_dict = {key: value for key, value in ProductDetail.PRODUCT_CATEGORY_SELECTION}
    for product in products:
        data_dict_of_products["product_id"].append(product.product_id)
        if product.media_title:
            data_dict_of_products["media_title_code"].append(product.media_title.code)
        else:
            data_dict_of_products["media_title_code"].append('')

        data_dict_of_products["product_name"].append(product.product_name)

        if product.segment:
            data_dict_of_products["segment_id"].append(product.segment.segment_name)
        else:
            data_dict_of_products["segment_id"].append('')

        if product.distribution_pattern_group:
            data_dict_of_products["distribution_pattern_group_id"].append(product.distribution_pattern_group.group_code)
        else:
            data_dict_of_products["distribution_pattern_group_id"].append('')

        if product.artist:
            data_dict_of_products["artist_name"].append(product.artist.artist_name)
        else:
            data_dict_of_products["artist_name"].append('')

        data_dict_of_products["daiki_price"].append(product.daiki_price)
        data_dict_of_products["price"].append(product.price)
        product_category = category_dict.get(product.product_category)
        # if not product_category:
        #     product_category = product.product_category_note
        data_dict_of_products["product_category"].append(product_category)
        data_dict_of_products["product_category_note"].append(product.product_category_note)
        data_dict_of_products["issue_date"].append(product.issue_date)
        data_dict_of_products["tax"].append(product.tax)
        if product.holding_company:
            data_dict_of_products["holding_company_name"].append(product.holding_company.company_name)
        else:
            data_dict_of_products["holding_company_name"].append('')

        if product.shown_report:
            data_dict_of_products["shown_report"].append("1")
        else:
            data_dict_of_products["shown_report"].append("")
        data_dict_of_products["deduction_rate"].append(product.deduction_rate)

    df_from_dict = pd.DataFrame(data_dict_of_products)

    buffer = BytesIO()
    # df_from_dict.to_excel('sale_good_export.xlsx', index=False)
    # df_from_dict.to_excel(buffer, index=False, engine='openpyxl')
    # buffer.seek(0)
    writer = pd.ExcelWriter(buffer, engine='xlsxwriter', engine_kwargs={'options': {'strings_to_numbers': True}})
    df_from_dict.T.reset_index().T.to_excel(writer, header=False, index=False)
    writer.close()

    return buffer

def export_product_and_send_email(email: str):
    buffer = export_product()

    if email:
        usecase.send_email_with_attachment(
            to=[email],
            subject=f"【分配計算システム | 商品情報Excel出力結果】",
            message=f"最新の商品情報を出力しました",
            attachment=ContentFile(buffer.getvalue(), f"商品情報_{datetime.now().strftime('%Y-%m-%d')}.xlsx"),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
