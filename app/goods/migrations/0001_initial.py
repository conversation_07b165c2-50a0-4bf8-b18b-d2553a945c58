# Generated by Django 4.2.13 on 2024-08-23 08:47

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('master_data', '0021_alter_mediatitle_advance_royalty_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductDetail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('product_id', models.CharField(blank=True, max_length=255, null=True, verbose_name='Product ID')),
                ('product_name', models.CharField(blank=True, max_length=255, null=True, verbose_name='Product Name')),
                ('price', models.CharField(blank=True, max_length=255, null=True, verbose_name='Price')),
                ('daiki_price', models.CharField(blank=True, max_length=255, null=True, verbose_name='Daiki Price')),
                ('product_category', models.CharField(blank=True, choices=[('CD(single)', 'CD(single)'), ('CD(album)', 'CD(album)'), ('DVD', 'DVD'), ('ブルーレイ', 'ブルーレイ'), ('書籍', '書籍')], max_length=255, null=True, verbose_name='Product Category')),
                ('tax', models.CharField(blank=True, choices=[('10', '10%'), ('8', '8%')], max_length=255, null=True, verbose_name='Tax')),
                ('dedecution_rate', models.CharField(blank=True, max_length=255, null=True, verbose_name='Deduction Rate')),
                ('issue_date', models.DateField(blank=True, null=True, verbose_name='Issue Date')),
                ('artist', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='product_detail', to='master_data.mstartist')),
                ('distribution_pattern_group', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='product_detail', to='master_data.mstdistributionpatterngroup')),
                ('holding_company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='product_detail', to='master_data.mstcompany')),
                ('media_title', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='product_detail', to='master_data.mediatitle')),
                ('segment', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='product_detail', to='master_data.mstsegment')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='product_detail', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Product Detail',
                'verbose_name_plural': 'Product Details',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='RoyaltyReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('file_status', models.CharField(blank=True, choices=[('in_progress', 'In Progress'), ('done', 'Done'), ('error', 'Error')], null=True, verbose_name='File Status')),
                ('file_name', models.CharField(blank=True, max_length=255, null=True, verbose_name='File Name')),
                ('report_file', models.FileField(blank=True, null=True, upload_to='royalty_report/', verbose_name='Report File')),
            ],
            options={
                'verbose_name': 'Royalty Report',
                'verbose_name_plural': 'Royalty Reports',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SaleGood',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('filename', models.CharField(blank=True, max_length=255, null=True, verbose_name='File Name')),
                ('status', models.CharField(blank=True, choices=[('in_progress', 'In Progress'), ('done', 'Done'), ('error', 'Error')], max_length=255, null=True, verbose_name='Status')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='sale_good', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Sale Good',
                'verbose_name_plural': 'Sale Goods',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SaleReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('target_month', models.DateField(blank=True, null=True, verbose_name='Target Month')),
                ('file_status', models.CharField(blank=True, choices=[('in_progress', 'In Progress'), ('done', 'Done'), ('error', 'Error')], null=True, verbose_name='File Status')),
                ('file_name', models.CharField(blank=True, max_length=255, null=True, verbose_name='File Name')),
                ('report_file', models.FileField(blank=True, null=True, upload_to='sale_report/', verbose_name='Report File')),
            ],
            options={
                'verbose_name': 'Sale Report',
                'verbose_name_plural': 'Sale Reports',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SaleGoodDetail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('sale', models.IntegerField(blank=True, default=0, null=True, verbose_name='Sale Price')),
                ('quantity', models.IntegerField(blank=True, default=0, null=True, verbose_name='Quantity')),
                ('price', models.IntegerField(blank=True, default=0, null=True, verbose_name='Price')),
                ('deduction', models.IntegerField(blank=True, default=0, null=True, verbose_name='Deduction')),
                ('target_month', models.DateField(blank=True, null=True, verbose_name='Target Month')),
                ('is_reward_generated', models.BooleanField(default=False, verbose_name='Is REward Generated')),
                ('report_type', models.CharField(blank=True, choices=[('daiki', 'daiki'), ('accounting', 'accounting'), ('live', 'live'), ('silkroad', 'silkroad'), ('amazon', 'amazon')], max_length=255, null=True, verbose_name='Report Type')),
                ('product_detail', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='sale_good_detail', to='goods.productdetail')),
                ('sale_good', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='sale_good_detail', to='goods.salegood')),
                ('sale_route', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='sale_good_detail', to='master_data.mstsaleroute')),
            ],
            options={
                'verbose_name': 'Sale Good Detail',
                'verbose_name_plural': 'Sale Good Details',
                'ordering': ['-created_at'],
            },
        ),
    ]
