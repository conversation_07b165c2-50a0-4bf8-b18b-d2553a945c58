# Generated by Django 4.2.13 on 2024-10-30 02:21

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('goods', '0018_alter_productdetail_shown_report'),
    ]

    operations = [
        migrations.CreateModel(
            name='GachaInformation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('sale_amount', models.IntegerField(blank=True, default=0, null=True, verbose_name='Sale Amount')),
                ('related_product', models.IntegerField(blank=True, default=0, null=True, verbose_name='Related Product')),
                ('badge_amount', models.IntegerField(blank=True, default=0, null=True, verbose_name='Badge Amount')),
                ('sticker_amount', models.IntegerField(blank=True, default=0, null=True, verbose_name='Sticker Amount')),
                ('instax_amount', models.IntegerField(blank=True, default=0, null=True, verbose_name='Instax Amount')),
                ('commitee_report', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='gacha_information', to='goods.committeereport')),
            ],
            options={
                'verbose_name': 'Gacha Information',
                'verbose_name_plural': 'Gacha Informations',
                'ordering': ['-created_at'],
            },
        ),
    ]
