# Generated by Django 4.2.13 on 2024-10-29 05:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('goods', '0016_committeereport_archive_description'),
    ]

    operations = [
        migrations.AddField(
            model_name='productdetail',
            name='product_category_note',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Product Category Note'),
        ),
        migrations.AddField(
            model_name='productdetail',
            name='shown_report',
            field=models.BooleanField(default=False, verbose_name='Shown Report'),
        ),
        migrations.AlterField(
            model_name='productdetail',
            name='product_category',
            field=models.CharField(blank=True, choices=[('CD(single)', 'CD(single)'), ('CD(album)', 'CD(album)'), ('DVD', 'DVD'), ('ブルーレイ', 'ブルーレイ'), ('Blu-ray', 'Blu-ray'), ('書籍', '書籍'), ('グッズ', 'グッズ')], max_length=255, null=True, verbose_name='Product Category'),
        ),
    ]
