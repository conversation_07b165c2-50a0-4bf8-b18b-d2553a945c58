# Generated by Django 4.2.13 on 2024-08-24 05:34

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('master_data', '0021_alter_mediatitle_advance_royalty_and_more'),
        ('goods', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='CommitteeReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('category', models.CharField(blank=True, choices=[('GOKU', '収支報告（通販・アーカイブ配信・楽天動画）'), ('ふしぎ遊戯', '収支報告（通販・アーカイブ配信）'), ('犬夜叉', '収支報告（DVD収支報告書）'), ('御茶ノ水ロック', '収支報告（販売+CD制作費+配信）'), ('御茶ノ水ロック', '収支報告（販売+CD制作費+配信）')], max_length=255, null=True, verbose_name='Category')),
                ('title', models.CharField(blank=True, max_length=255, null=True, verbose_name='Title')),
                ('period_start', models.DateField(blank=True, null=True, verbose_name='Period Start')),
                ('period_end', models.DateField(blank=True, null=True, verbose_name='Period End')),
                ('report_date', models.DateField(blank=True, null=True, verbose_name='Report Date')),
                ('pay_date', models.DateField(blank=True, null=True, verbose_name='Pay Date')),
                ('calculation_status', models.CharField(blank=True, choices=[('in_progress', 'In Progress'), ('done', 'Done'), ('error', 'Error')], null=True, verbose_name='File Status')),
                ('is_sent', models.BooleanField(default=False, verbose_name='Is Sent')),
                ('file_name', models.CharField(blank=True, max_length=255, null=True, verbose_name='File Name')),
                ('report_file', models.FileField(blank=True, null=True, upload_to='committee_report/', verbose_name='Report File')),
                ('payslip_number', models.CharField(blank=True, max_length=255, null=True, verbose_name='Payslip Number')),
                ('royalty_use_fee', models.FloatField(blank=True, default=0, null=True, verbose_name='Royalty Use Fee')),
                ('artist', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='committee_report', to='master_data.mstartist')),
            ],
            options={
                'verbose_name': 'Committee Report',
                'verbose_name_plural': 'Committee Reports',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ReportRakutenStreamService',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('category', models.CharField(blank=True, max_length=255, null=True, verbose_name='Category')),
                ('carryover', models.IntegerField(blank=True, default=0, null=True, verbose_name='Carryover')),
                ('committee_report', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='rakuten_stream_service', to='goods.committeereport')),
            ],
            options={
                'verbose_name': 'Report Rakuten Stream Service',
                'verbose_name_plural': 'Report Rakuten Stream Services',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ReportRakutenStreamServiceDetail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('month', models.DateField(blank=True, null=True, verbose_name='Month')),
                ('amount', models.IntegerField(blank=True, default=0, null=True, verbose_name='Amount')),
                ('sales', models.FloatField(blank=True, default=0, null=True, verbose_name='Sales')),
                ('rakuten_stream_service', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='rakuten_stream_service_detail', to='goods.reportrakutenstreamservice')),
            ],
            options={
                'verbose_name': 'Report Rakuten Stream Service',
                'verbose_name_plural': 'Report Rakuten Stream Services',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ReportArchiveStreaming',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('category', models.CharField(blank=True, choices=[('GOKU', '収支報告（通販・アーカイブ配信・楽天動画）'), ('ふしぎ遊戯', '収支報告（通販・アーカイブ配信）'), ('犬夜叉', '収支報告（DVD収支報告書）'), ('御茶ノ水ロック', '収支報告（販売+CD制作費+配信）'), ('御茶ノ水ロック', '収支報告（販売+CD制作費+配信）')], max_length=255, null=True, verbose_name='Category')),
                ('month', models.DateField(blank=True, null=True, verbose_name='Month')),
                ('amount', models.IntegerField(blank=True, default=0, null=True, verbose_name='Amount')),
                ('sales', models.FloatField(blank=True, default=0, null=True, verbose_name='Sales')),
                ('rate', models.FloatField(blank=True, default=0, null=True, verbose_name='Rate')),
                ('total_payment', models.FloatField(blank=True, default=0, null=True, verbose_name='Total Payment')),
                ('committee_report', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='committee_report_detail', to='goods.committeereport')),
            ],
            options={
                'verbose_name': 'Report Archive Streaming',
                'verbose_name_plural': 'Report Archive Streamings',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CommitteeReportCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('category', models.CharField(blank=True, choices=[('GOKU', '収支報告（通販・アーカイブ配信・楽天動画）'), ('ふしぎ遊戯', '収支報告（通販・アーカイブ配信）'), ('犬夜叉', '収支報告（DVD収支報告書）'), ('御茶ノ水ロック', '収支報告（販売+CD制作費+配信）'), ('御茶ノ水ロック', '収支報告（販売+CD制作費+配信）')], max_length=255, null=True, verbose_name='Category')),
                ('title', models.CharField(blank=True, max_length=255, null=True, verbose_name='Title')),
                ('artist', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='committee_report_category', to='master_data.mstartist')),
            ],
            options={
                'verbose_name': 'Committee Report Category',
                'verbose_name_plural': 'Committee Report Categories',
                'ordering': ['-created_at'],
                'unique_together': {('category', 'artist')},
            },
        ),
    ]
