# Generated by Django 4.2.13 on 2024-08-28 08:19

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('master_data', '0021_alter_mediatitle_advance_royalty_and_more'),
        ('goods', '0003_alter_productdetail_user'),
    ]

    operations = [
        migrations.CreateModel(
            name='SaleGoodReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('first_sale_amount', models.IntegerField(blank=True, default=0, null=True, verbose_name='First Sale Amount')),
                ('second_sale_amount', models.IntegerField(blank=True, default=0, null=True, verbose_name='Second Sale Amount')),
                ('third_sale_amount', models.IntegerField(blank=True, default=0, null=True, verbose_name='Third Sale Amount')),
                ('total_sales_amount', models.IntegerField(blank=True, default=0, null=True, verbose_name='Total Sales Amount')),
                ('first_sale_price', models.FloatField(blank=True, default=0, null=True, verbose_name='First Sale Price')),
                ('second_sale_price', models.FloatField(blank=True, default=0, null=True, verbose_name='Second Sale Price')),
                ('third_sale_price', models.FloatField(blank=True, default=0, null=True, verbose_name='Third Sale Price')),
                ('total_sales_price', models.FloatField(blank=True, default=0, null=True, verbose_name='Total Sales Price')),
                ('fee', models.FloatField(blank=True, default=0, null=True, verbose_name='Fee')),
                ('payment', models.FloatField(blank=True, default=0, null=True, verbose_name='Payment')),
                ('product_detail', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='sale_good_report', to='goods.productdetail')),
                ('sale_route', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='sale_good_report', to='master_data.mstsaleroute')),
            ],
            options={
                'verbose_name': 'Sale Good Report',
                'verbose_name_plural': 'Sale Good Reports',
                'ordering': ['-created_at'],
            },
        ),
    ]
