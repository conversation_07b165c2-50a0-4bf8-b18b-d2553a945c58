# Generated by Django 4.2.13 on 2024-08-31 04:28

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('goods', '0006_reportarchivestreaming_product_detail_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='reportrakutenstreamservice',
            name='first_sale_amount',
            field=models.IntegerField(blank=True, default=0, null=True, verbose_name='First Sale Amount'),
        ),
        migrations.AddField(
            model_name='reportrakutenstreamservice',
            name='first_sale_price',
            field=models.FloatField(blank=True, default=0, null=True, verbose_name='First Sale Price'),
        ),
        migrations.AddField(
            model_name='reportrakutenstreamservice',
            name='second_sale_amount',
            field=models.IntegerField(blank=True, default=0, null=True, verbose_name='Second Sale Amount'),
        ),
        migrations.AddField(
            model_name='reportrakutenstreamservice',
            name='second_sale_price',
            field=models.FloatField(blank=True, default=0, null=True, verbose_name='Second Sale Price'),
        ),
        migrations.AddField(
            model_name='reportrakutenstreamservice',
            name='third_sale_amount',
            field=models.IntegerField(blank=True, default=0, null=True, verbose_name='Third Sale Amount'),
        ),
        migrations.AddField(
            model_name='reportrakutenstreamservice',
            name='third_sale_price',
            field=models.FloatField(blank=True, default=0, null=True, verbose_name='Third Sale Price'),
        ),
        migrations.DeleteModel(
            name='ReportRakutenStreamServiceDetail',
        ),
    ]
