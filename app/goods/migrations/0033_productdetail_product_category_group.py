# Generated by Django 4.2.13 on 2025-02-18 12:04

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('master_data', '0023_artistcategorygroup_productcategorygroup_and_more'),
        ('goods', '0032_salereport_target_month_end'),
    ]

    operations = [
        migrations.AddField(
            model_name='productdetail',
            name='product_category_group',
            field=models.ForeignKey(blank=True, db_constraint=False, error_messages={'protected': 'すでに商品と紐づいているため削除できません'}, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='product_detail', to='master_data.productcategorygroup'),
        ),
    ]
