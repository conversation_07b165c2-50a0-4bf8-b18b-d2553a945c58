import os
from django.db import models
from django.db.models.signals import pre_save
from django.db.models import Q
from django.dispatch import receiver
from django.utils.translation import gettext as _

from app.common.utils import DeletedManager, TimeStampedModel, load_yaml
from app.master_data.models import MediaTitle, MstArtist, MstCompany, MstDistributionPattern, MstDistributionPatternGroup, MstSaleRoute, MstSegment, ProductCategoryGroup
from app.user.models import User

# Create your models here.
class SaleGood(TimeStampedModel):
    IMPORT_STATUS = (
        ('in_progress', 'In Progress'),
        ('done', 'Done'),
        ('error', 'Error'),
    )
    filename = models.CharField(_("File Name"), max_length=255, blank=True, null=True)
    user = models.ForeignKey(User, on_delete=models.DO_NOTHING, related_name='sale_good', blank=True, null=True)
    status = models.Char<PERSON>ield(_("Status"), choices=IMPORT_STATUS, max_length=255, blank=True, null=True)
    import_note = models.TextField(_("Import Note"), blank=True, null=True)

    objects = DeletedManager()

    class Meta:
        verbose_name = _('Sale Good')
        verbose_name_plural = _('Sale Goods')
        ordering = ['-created_at']

    def __str__(self):
        return self.filename

class ProductDetail(TimeStampedModel):
    TAX_SELECTION = (
        ('10', '10%'),
        ('8', '8%'),
    )
    PRODUCT_CATEGORY_SELECTION = (
        ('CD', 'CD'),
        ('CD(single)', 'CD(single)'),
        ('CD(album)', 'CD(album)'),
        ('DVD', 'DVD'),
        ('ブルーレイ', 'ブルーレイ'),
        ('Blu-ray', 'Blu-ray'),
        ('書籍', '書籍'),
        ('グッズ', 'グッズ'),
    )
    segment = models.ForeignKey(MstSegment, on_delete=models.DO_NOTHING, related_name='product_detail', blank=True, null=True)
    product_id = models.CharField(_("Product ID"), max_length=255, blank=True, null=True)
    product_name = models.CharField(_("Product Name"), max_length=255, blank=True, null=True)
    artist = models.ForeignKey(MstArtist, on_delete=models.DO_NOTHING, related_name='product_detail', blank=True, null=True)
    distribution_pattern_group = models.ForeignKey(MstDistributionPatternGroup, on_delete=models.DO_NOTHING, related_name='product_detail', blank=True, null=True)
    price = models.CharField(_("Price"), max_length=255, blank=True, null=True, default=0)
    daiki_price = models.CharField(_("Daiki Price"), max_length=255, blank=True, null=True, default=0)
    product_category = models.CharField(_("Product Category"), choices=PRODUCT_CATEGORY_SELECTION, max_length=255, blank=True, null=True)
    product_category_note = models.CharField(_("Product Category Note"), max_length=255, blank=True, null=True)
    tax = models.CharField(_("Tax"), choices=TAX_SELECTION, max_length=255, blank=True, null=True)
    holding_company = models.ForeignKey(MstCompany, on_delete=models.DO_NOTHING, related_name='product_detail', blank=True, null=True)
    deduction_rate = models.CharField(_("Deduction Rate"), max_length=255, blank=True, null=True)
    user = models.ForeignKey(User, on_delete=models.DO_NOTHING, related_name='product_detail', blank=True, null=True, db_constraint=False)
    issue_date = models.DateField(_("Issue Date"), blank=True, null=True)
    media_title = models.ForeignKey(MediaTitle, on_delete=models.DO_NOTHING, related_name='product_detail', blank=True, null=True)
    shown_report = models.BooleanField(_("Shown Report"), blank=True, null=True, default=False)
    product_category_group = models.ForeignKey(ProductCategoryGroup, on_delete=models.PROTECT,
                                               related_name='product_detail',
                                               blank=True, null=True,
                                               db_constraint=False,
                                               error_messages={
            'protected': _("すでに商品と紐づいているため削除できません")
        })

    objects = DeletedManager()

    class Meta:
        verbose_name = _('Product Detail')
        verbose_name_plural = _('Product Details')
        ordering = ['-created_at']
        constraints = [
            models.UniqueConstraint(
                fields=['product_id'],
                condition=Q(is_delete=False),
                name='unique_product_id_if_not_deleted'
            ),
        ]

class SaleGoodDetail(TimeStampedModel):
    # REPORT_TYPE = (
    #     ('daiki', 'daiki'),
    #     ('経理報告書', '経理報告書'),
    #     ('社販', '社販'),
    #     ('Silkroad Store', 'Silkroad Store'),
    #     ('Amazon', 'Amazon'),
    #     ('物販', '物販'),
    #     ('物販売上集計表GB', '物販売上集計表GB'),
    #     ('DL窓口', 'DL窓口'),
    # )
    REPORT_TYPE = load_yaml('config/choices.yaml')['report_types']
    sale_good = models.ForeignKey(SaleGood, on_delete=models.DO_NOTHING, related_name='sale_good_detail', blank=True, null=True)
    product_detail = models.ForeignKey('ProductDetail', on_delete=models.DO_NOTHING, related_name='sale_good_detail', blank=True, null=True)
    sale_route = models.ForeignKey(MstSaleRoute, on_delete=models.DO_NOTHING, related_name='sale_good_detail', blank=True, null=True)
    distribution_pattern = models.ForeignKey(MstDistributionPattern, on_delete=models.DO_NOTHING, related_name='sale_good_detail', blank=True, null=True, db_constraint=False)
    distribution_pattern_group = models.ForeignKey(MstDistributionPatternGroup, on_delete=models.DO_NOTHING, related_name='sale_good_detail', blank=True, null=True, db_constraint=False)
    original_sale = models.IntegerField(_("Original Sale Price"), default=0, blank=True, null=True)
    sale = models.IntegerField(_("Sale Price"), default=0, blank=True, null=True)
    quantity = models.IntegerField(_("Quantity"), default=0, blank=True, null=True)
    price = models.IntegerField(_("Price"), default=0, blank=True, null=True)
    deduction = models.IntegerField(_("Deduction"), default=0, blank=True, null=True)
    target_month = models.DateField(_("Target Month"), blank=True, null=True)
    is_reward_generated = models.BooleanField(_("Is REward Generated"), default=False)
    report_type = models.CharField(_("Report Type"), choices=REPORT_TYPE, max_length=255, blank=True, null=True)
    note = models.TextField(_("Note"), blank=True, null=True)

    objects = DeletedManager()

    class Meta:
        verbose_name = _('Sale Good Detail')
        verbose_name_plural = _('Sale Good Details')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['is_delete', 'target_month']),
            models.Index(fields=['is_delete', 'target_month', 'sale_route', 'product_detail']),
        ]

class SaleReport(TimeStampedModel):
    FILE_GENERATE_STATUS = (
        ('in_progress', 'In Progress'),
        ('done', 'Done'),
        ('error', 'Error'),
    )
    target_month = models.DateField(_("Target Month"), blank=True, null=True)
    target_month_end = models.DateField(_("Target Month End"), blank=True, null=True)
    file_status = models.CharField(_("File Status"), choices=FILE_GENERATE_STATUS, blank=True, null=True)
    file_name = models.CharField(_("File Name"), max_length=255, blank=True, null=True)
    report_file = models.FileField(_("Report File"), upload_to='sale_report/', blank=True, null=True)

    objects = DeletedManager()

    def report_file_name(self):
        return os.path.basename(self.report_file.name)

    class Meta:
        verbose_name = _('Sale Report')
        verbose_name_plural = _('Sale Reports')
        ordering = ['-created_at']

    def __str__(self):
        return self.report_file.name

class RoyaltyReport(TimeStampedModel):
    FILE_GENERATE_STATUS = (
        ('in_progress', 'In Progress'),
        ('done', 'Done'),
        ('error', 'Error'),
    )
    file_status = models.CharField(_("File Status"), choices=FILE_GENERATE_STATUS, blank=True, null=True)
    file_name = models.CharField(_("File Name"), max_length=255, blank=True, null=True)
    report_file = models.FileField(_("Report File"), upload_to='royalty_report/', blank=True, null=True)

    objects = DeletedManager()

    class Meta:
        verbose_name = _('Royalty Report')
        verbose_name_plural = _('Royalty Reports')
        ordering = ['-created_at']

    def __str__(self):
        return self.report_file.name

class CommitteeReportCategory(TimeStampedModel):
    REPORT_CATEGORY = (
        ('GOKU', '収支報告（通販・アーカイブ配信・楽天動画）'),
        ('ふしぎ遊戯', '収支報告（通販・アーカイブ配信）'),
        ('犬夜叉', '収支報告（DVD収支報告書）'),
        ('御茶ノ水ロック', '収支報告（販売+CD制作費+配信）'),
    )
    category = models.CharField(_("Category"), choices=REPORT_CATEGORY, max_length=255, blank=True, null=True)
    artist = models.ForeignKey(MstArtist, on_delete=models.DO_NOTHING, related_name='committee_report_category', blank=True, null=True)
    title = models.TextField(_("Title"), blank=True, null=True)

    objects = DeletedManager()

    class Meta:
        verbose_name = _('Committee Report Category')
        verbose_name_plural = _('Committee Report Categories')
        ordering = ['-created_at']
        unique_together = ('category', 'artist')

    def __str__(self):
        return self.artist.artist_name

class CommitteeReport(TimeStampedModel):
    CALCULATION_STATUS = (
        ('in_progress', 'In Progress'),
        ('done', 'Done'),
        ('error', 'Error'),
    )
    category = models.CharField(_("Category"), choices=CommitteeReportCategory.REPORT_CATEGORY, max_length=255, blank=True, null=True)
    artist = models.ForeignKey(MstArtist, on_delete=models.DO_NOTHING, related_name='committee_report', blank=True, null=True)
    title = models.CharField(_("Title"), max_length=255, blank=True, null=True)
    period_start = models.DateField(_("Period Start"), blank=True, null=True)
    period_end = models.DateField(_("Period End"), blank=True, null=True)
    report_date = models.DateField(_("Report Date"), blank=True, null=True)
    pay_date = models.DateField(_("Pay Date"), blank=True, null=True)
    calculation_status = models.CharField(_("File Status"), choices=CALCULATION_STATUS, blank=True, null=True)
    is_sent = models.BooleanField(_("Is Sent"), default=False)
    file_name = models.CharField(_("File Name"), max_length=255, blank=True, null=True)
    report_file = models.FileField(_("Report File"), upload_to='committee_report/', blank=True, null=True)
    payslip_number = models.CharField(_("Payslip Number"), max_length=255, blank=True, null=True)
    royalty_use_fee = models.FloatField(_("Royalty Use Fee"), default=0, blank=True, null=True)
    yutaka_price = models.CharField(_("Yutaka Price"), max_length=255, blank=True, null=True)
    archive_description = models.TextField(_("Archive Description"), blank=True, null=True)

    objects = DeletedManager()

    def report_file_name(self):
        return os.path.basename(self.report_file.name)

    class Meta:
        verbose_name = _('Committee Report')
        verbose_name_plural = _('Committee Reports')
        ordering = ['-created_at']

    def __str__(self):
        return self.report_file.name

class SaleGoodReport(TimeStampedModel):
    committee_report = models.ForeignKey(CommitteeReport, on_delete=models.DO_NOTHING, related_name='sale_good_report', blank=True, null=True)
    sale_route = models.ForeignKey(MstSaleRoute, on_delete=models.DO_NOTHING, related_name='sale_good_report', blank=True, null=True, db_constraint=False)
    product_detail = models.ForeignKey(ProductDetail, on_delete=models.DO_NOTHING, related_name='sale_good_report', blank=True, null=True, db_constraint=False)
    first_sale_amount = models.IntegerField(_("First Sale Amount"), default=0, blank=True, null=True)
    second_sale_amount = models.IntegerField(_("Second Sale Amount"), default=0, blank=True, null=True)
    third_sale_amount = models.IntegerField(_("Third Sale Amount"), default=0, blank=True, null=True)
    total_sales_amount = models.IntegerField(_("Total Sales Amount"), default=0, blank=True, null=True)
    first_sale_price = models.FloatField(_("First Sale Price"), default=0, blank=True, null=True)
    second_sale_price = models.FloatField(_("Second Sale Price"), default=0, blank=True, null=True)
    third_sale_price = models.FloatField(_("Third Sale Price"), default=0, blank=True, null=True)
    total_sales_price = models.FloatField(_("Total Sales Price"), default=0, blank=True, null=True)
    fee = models.FloatField(_("Fee"), default=0, blank=True, null=True)
    payment = models.FloatField(_("Payment"), default=0, blank=True, null=True)

    objects = DeletedManager()

    class Meta:
        verbose_name = _('Sale Good Report')
        verbose_name_plural = _('Sale Good Reports')
        ordering = ['-created_at']

class ReportArchiveStreaming(TimeStampedModel):
    ARCHIVE_CATEGORY = (
        ('GOKU', '収支報告（通販・アーカイブ配信・楽天動画）'),
        ('ふしぎ遊戯', '収支報告（通販・アーカイブ配信）'),
    )
    product_detail = models.ForeignKey(ProductDetail, on_delete=models.DO_NOTHING, related_name='report_archive_streaming', blank=True, null=True)
    committee_report = models.ForeignKey(CommitteeReport, on_delete=models.DO_NOTHING, related_name='report_archive_streaming', blank=True, null=True)
    category = models.CharField(_("Category"), choices=ARCHIVE_CATEGORY, max_length=255, blank=True, null=True)
    month = models.DateField(_("Month"), blank=True, null=True)
    amount = models.IntegerField(_("Amount"), default=0, blank=True, null=True)
    sales = models.FloatField(_("Sales"), default=0, blank=True, null=True)
    rate = models.FloatField(_("Rate"), default=0, blank=True, null=True)
    total_payment = models.FloatField(_("Total Payment"), default=0, blank=True, null=True)

    objects = DeletedManager()

    class Meta:
        verbose_name = _('Report Archive Streaming')
        verbose_name_plural = _('Report Archive Streamings')
        ordering = ['-created_at']

class ReportRakutenStreamService(TimeStampedModel):
    SERVICE_CATEGORY = (
        ('楽天ShowTime', '楽天ShowTime'),
        ('Rakuten TV', 'Rakuten TV'),
    )
    committee_report = models.ForeignKey(CommitteeReport, on_delete=models.DO_NOTHING, related_name='report_rakuten_stream_service', blank=True, null=True)
    category = models.CharField(_("Category"), choices=SERVICE_CATEGORY, max_length=255, blank=True, null=True)
    first_sale_amount = models.IntegerField(_("First Sale Amount"), default=0, blank=True, null=True)
    second_sale_amount = models.IntegerField(_("Second Sale Amount"), default=0, blank=True, null=True)
    third_sale_amount = models.IntegerField(_("Third Sale Amount"), default=0, blank=True, null=True)
    first_sale_price = models.FloatField(_("First Sale Price"), default=0, blank=True, null=True)
    second_sale_price = models.FloatField(_("Second Sale Price"), default=0, blank=True, null=True)
    third_sale_price = models.FloatField(_("Third Sale Price"), default=0, blank=True, null=True)
    carryover = models.IntegerField(_("Carryover"), default=0, blank=True, null=True)

    objects = DeletedManager()

    class Meta:
        verbose_name = _('Report Rakuten Stream Service')
        verbose_name_plural = _('Report Rakuten Stream Services')
        ordering = ['-created_at']

class GachaInformation(TimeStampedModel):
    commitee_report = models.ForeignKey(CommitteeReport, on_delete=models.DO_NOTHING, related_name='gacha_information', blank=True, null=True)
    sale_amount = models.IntegerField(_("Sale Amount"), default=0, blank=True, null=True)
    related_product = models.IntegerField(_("Related Product"), default=0, blank=True, null=True)
    badge_amount = models.IntegerField(_("Badge Amount"), default=0, blank=True, null=True)
    sticker_amount = models.IntegerField(_("Sticker Amount"), default=0, blank=True, null=True)
    instax_amount = models.IntegerField(_("Instax Amount"), default=0, blank=True, null=True)

    objects = DeletedManager()

    class Meta:
        verbose_name = _('Gacha Information')
        verbose_name_plural = _('Gacha Informations')
        ordering = ['-created_at']
