import graphene
from graphene_django import DjangoO<PERSON>Type, DjangoConnection<PERSON>ield
from graphene_django.filter import DjangoFilterConnectionField

from django.db import models
from django.db.models import Q, Sum, F
from app.goods.models import CommitteeReport, CommitteeReportCategory, GachaInformation, ProductDetail, ReportArchiveStreaming, ReportRakutenStreamService, RoyaltyReport, SaleGood, SaleGoodDetail, SaleGoodReport, SaleReport
from app.master_data.models import MstSaleRoute


class ProductDetailNode(DjangoObjectType):
    pk = graphene.Int(source='pk')
    product_category = graphene.String()

    class Meta:
        model = ProductDetail
        fields = '__all__'
        filter_fields = {
            'product_id': ['exact'],
            'product_name': ['exact', 'icontains'],
        }
        interfaces = (graphene.relay.Node, )

    def resolve_product_category(self, info):
        category = self.product_category
        category_dict = {key: value for key, value in ProductDetail.PRODUCT_CATEGORY_SELECTION}
        category_name = category_dict.get(category)
        return category_name

class SaleGoodNode(DjangoObjectType):
    pk = graphene.Int(source='pk')

    class Meta:
        model = SaleGood
        fields = '__all__'
        filter_fields = {
            'filename': ['exact', 'icontains'],
        }
        interfaces = (graphene.relay.Node, )

class GachaInformationNode(DjangoObjectType):
    pk = graphene.Int(source='pk')

    class Meta:
        model = GachaInformation
        fields = '__all__'
        filter_fields = {
            'commitee_report__id': ['exact'],
        }
        interfaces = (graphene.relay.Node, )

class SaleSummaryType(graphene.ObjectType):
    report_type = graphene.String()
    target_month = graphene.Date()
    sale_route_name = graphene.String()
    total_sales = graphene.Float()
    created_at = graphene.Date()

class MonthlySaleNode(DjangoObjectType):
    pk = graphene.Int(source='pk')
    total_sales = graphene.Float()
    created_at = graphene.Date()
    route_name = graphene.String()

    class Meta:
        model = SaleGoodDetail
        fields = '__all__'
        interfaces = (graphene.relay.Node, )

    @classmethod
    def get_queryset(cls, queryset, info):
        return queryset.values(
            'report_type',
            'target_month',
            'sale_route__route_name'
        ).annotate(
            total_sales=Sum('sale')
        ).order_by(
            'report_type',
            'target_month'
        )

    def resolve_total_sales(self, info):
        # Assuming the aggregation is already done in the query
        return self.total_sales if hasattr(self, 'total_sales') else 0

    def resolve_route_name(self, info):
        return self.sale_route.route_name if self.sale_route else ''

class MonthlySaleV2Node(DjangoObjectType):
    pk = graphene.Int(source='pk')
    total_sales = graphene.Float()
    created_at = graphene.Date()

    class Meta:
        model = MstSaleRoute
        fields = ('route_name',)
        filter_fields = {
            'route_name': ['exact'],
        }
        interfaces = (graphene.relay.Node, )

    def resolve_total_sales(self, info):
        # Assuming the aggregation is already done in the query
        return self.total_sales if hasattr(self, 'total_sales') else 0

    def resolve_created_at(self, info):
        return self.created_at

class SaleGoodDetailNode(DjangoObjectType):
    pk = graphene.Int(source='pk')
    report_type = graphene.String()

    class Meta:
        model = SaleGoodDetail
        fields = '__all__'
        filter_fields = ["sale_route"]
        # filter_fields = {
        #     'sale_route__route_name': ['exact', 'icontains'],
        # }
        interfaces = (graphene.relay.Node, )

    def resolve_report_type(self, info):
        report_type = self.report_type
        report_type_dict = {key: value for key, value in SaleGoodDetail.REPORT_TYPE}
        report_type_name = report_type_dict.get(report_type)
        return report_type_name

class SaleReportNode(DjangoObjectType):
    pk = graphene.Int(source='pk')
    # target_month = graphene.String()

    class Meta:
        model = SaleReport
        fields = '__all__'
        filter_fields = {
            'file_status': ['exact'],
        }
        interfaces = (graphene.relay.Node, )

    # def resolve_target_month(self, info):
    #     start_period = self.target_month
    #     end_period = self.target_month_end.strftime("%m") if self.target_month_end else None
    #     if self.target_month_end:
    #         start_period = start_period.strftime("%Y-%m")
    #         return f"{start_period} ~ {end_period}"
    #     return start_period

class RoyaltyReportNode(DjangoObjectType):
    pk = graphene.Int(source='pk')

    class Meta:
        model = RoyaltyReport
        fields = '__all__'
        filter_fields = {
            'file_status': ['exact'],
        }
        interfaces = (graphene.relay.Node, )

class CommitteeReportCatgeoryNode(DjangoObjectType):
    pk = graphene.Int(source='pk')
    category = graphene.String()
    category_id = graphene.String()

    class Meta:
        model = CommitteeReportCategory
        fields = '__all__'
        filter_fields = {
            'category': ['exact'],
            'artist__artist_name': ['exact', 'icontains'],
        }
        interfaces = (graphene.relay.Node, )

    def resolve_category(self, info):
        category = self.category
        category_dict = {key: value for key, value in CommitteeReportCategory.REPORT_CATEGORY}
        category_name = category_dict.get(category)
        return category_name

    def resolve_category_id(self, info):
        category = self.category
        category_dict = {key: key for key, _ in CommitteeReportCategory.REPORT_CATEGORY}
        category_name = category_dict.get(category)
        return category_name

class CommitteeReportNode(DjangoObjectType):
    pk = graphene.Int(source='pk')
    category = graphene.String()
    category_id = graphene.String()

    class Meta:
        model = CommitteeReport
        fields = '__all__'
        filter_fields = {
            'calculation_status': ['exact'],
        }
        interfaces = (graphene.relay.Node, )

    def resolve_category(self, info):
        category = self.category
        category_dict = {key: value for key, value in CommitteeReportCategory.REPORT_CATEGORY}
        category_name = category_dict.get(category)
        return category_name

    def resolve_category_id(self, info):
        category = self.category
        category_dict = {key: key for key, _ in CommitteeReportCategory.REPORT_CATEGORY}
        category_name = category_dict.get(category)
        return category_name

class CommitteeSaleReportNode(DjangoObjectType):
    pk = graphene.Int(source='pk')

    class Meta:
        model = SaleGoodReport
        fields = "__all__"
        filter_fields = {
            'product_detail__product_id': ['exact'],
        }
        interfaces = (graphene.relay.Node, )

class ReportArchiveStreamingNode(DjangoObjectType):
    pk = graphene.Int(source='pk')
    category = graphene.String()
    category_id = graphene.String()

    class Meta:
        model = ReportArchiveStreaming
        fields = '__all__'
        filter_fields = {
            'product_detail__product_id': ['exact'],
        }
        interfaces = (graphene.relay.Node, )

    def resolve_category(self, info):
        category = self.category
        category_dict = {key: value for key, value in ReportArchiveStreaming.ARCHIVE_CATEGORY}
        category_name = category_dict.get(category)
        return category_name

    def resolve_category_id(self, info):
        category = self.category
        category_dict = {key: key for key, _ in ReportArchiveStreaming.ARCHIVE_CATEGORY}
        category_name = category_dict.get(category)
        return category_name

class ReportRakutenStreamServiceNode(DjangoObjectType):
    pk = graphene.Int(source='pk')
    category = graphene.String()
    category_id = graphene.String()

    class Meta:
        model = ReportRakutenStreamService
        fields = '__all__'
        filter_fields = {
            'category': ['exact'],
        }
        interfaces = (graphene.relay.Node, )

    def resolve_category(self, info):
        category = self.category
        category_dict = {key: value for key, value in ReportRakutenStreamService.SERVICE_CATEGORY}
        category_name = category_dict.get(category)
        return category_name

    def resolve_category_id(self, info):
        category = self.category
        category_dict = {key: key for key, _ in ReportRakutenStreamService.SERVICE_CATEGORY}
        category_name = category_dict.get(category)
        return category_name

class CommitteeReportDetailNode(DjangoObjectType):
    pk = graphene.Int(source='pk')
    category = graphene.String()
    category_id = graphene.String()
    sale_report = DjangoConnectionField(CommitteeSaleReportNode)
    archive_streaming = DjangoConnectionField(ReportArchiveStreamingNode)
    rakuten_stream_service = DjangoConnectionField(ReportRakutenStreamServiceNode)
    gacha_information_data = DjangoConnectionField(GachaInformationNode)

    class Meta:
        model = CommitteeReport
        fields = '__all__'
        filter_fields = {
            'calculation_status': ['exact'],
        }
        interfaces = (graphene.relay.Node, )

    def resolve_category(self, info):
        category = self.category
        category_dict = {key: value for key, value in CommitteeReportCategory.REPORT_CATEGORY}
        category_name = category_dict.get(category)
        return category_name

    def resolve_category_id(self, info):
        category = self.category
        category_dict = {key: key for key, _ in CommitteeReportCategory.REPORT_CATEGORY}
        category_name = category_dict.get(category)
        return category_name

    def resolve_sale_report(self, info):
        return self.sale_good_report.all()

    def resolve_archive_streaming(self, info):
        return self.report_archive_streaming.all()

    def resolve_rakuten_stream_service(self, info):
        return self.report_rakuten_stream_service.all()

    def resolve_gacha_information_data(self, info):
        return self.gacha_information.all()
