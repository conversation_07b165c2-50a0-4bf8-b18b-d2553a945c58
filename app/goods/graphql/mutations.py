import base64
from datetime import datetime
from io import BytesIO
import logging
import re
import graphene
from graphene_django.forms.mutation import DjangoModelFormMutation
from graphene import relay
from graphene_file_upload.scalars import Upload
from django.db import transaction
from django.db import models  # Add this import statement
from django.db.models import Sum, F, Q
from graphql import GraphQLError
import numpy as np
import pandas as pd  # Add this import statement
from django_q.tasks import async_task
from graphql_jwt.decorators import login_required

from app.common import utils
from app.goods import usecase
from app.common import usecase as common_usecase
from app.goods.forms import CommitteeReportForm, GachaInformationForm, ProductDetailForm, ReportArchiveStreamingForm, ReportRakutenStreamServiceForm
from app.goods.graphql.types import CommitteeReportNode, GachaInformationNode, ProductDetailNode, ReportArchiveStreamingNode, ReportRakutenStreamServiceNode
from app.goods.models import CommitteeReport, GachaInformation, ProductDetail, ReportArchiveStreaming, ReportRakutenStreamService, RoyaltyReport, SaleGood, SaleGoodDetail, SaleReport
from app.master_data.models import MstDistributionPatternGroupDetail, MstSaleRoute
from app.project.models import PaySlipReward, RecipientRewardDistribution, RewardDistributorPortion
from app.rewards.models import RewardDistributionMapping

logger = logging.getLogger(__name__)

class CreateProduct(DjangoModelFormMutation):
    product = graphene.Field(ProductDetailNode)

    class Meta:
        form_class = ProductDetailForm
        return_field_name = 'product'

    @classmethod
    def get_form_kwargs(cls, root, info, **input):
        kwargs = super().get_form_kwargs(root, info, **input)
        data = kwargs.get('data')
        data['logged_user'] = info.context.user
        kwargs['data'] = data
        return kwargs

    @classmethod
    @login_required
    def perform_mutate(cls, form, info):
        return super().perform_mutate(form, info)

class DeleteProductDetailData(relay.ClientIDMutation):  # Update the class definition
    Output = utils.Responser

    class Input:
        id = graphene.ID(required=True)

    @login_required
    def mutate_and_get_payload(root, info, id):
        product = ProductDetail.objects.get(pk=id)
        if not product:
            raise GraphQLError("Data not found")

        if product.is_delete:
            raise GraphQLError("Data already deleted")

        product.is_delete = True
        product.save()

        return utils.Responser(result="Success")

class UploadSalesGoodMutation(graphene.Mutation):
    class Arguments:
        file_in = Upload(required=True)

    Output = utils.Responser

    @login_required
    def mutate(self, info, file_in):
        file_name = file_in.name

        #check filename format
        pattern = r'^\d{4}-\d{2}-sale-data.*'
        if not re.match(pattern, file_name):
            raise GraphQLError(f'有効なファイル名を持つデータを選択してください。(例: yyyy-mmm-sale-data.xlsx)')

        # Extract year and month from the file name
        year, month = file_name.split('-')[:2]

        # Check if the year and month are valid
        try:
            datetime.strptime(f"{year}-{month}", "%Y-%m")
        except ValueError:
            raise GraphQLError("Invalid yyyy or mm in file name")

        sale_good, create = SaleGood.objects.update_or_create(
            filename=file_in.name,
            defaults={
                'status': 'in_progress',
                'user': info.context.user,
            }
        )

        with transaction.atomic():
            print(file_in.name)
            excel = pd.ExcelFile(file_in)
            sheet_name = excel.sheet_names[0]  # Get the first sheet name
            df = pd.read_excel(excel, sheet_name=sheet_name).dropna(how='all').replace(np.nan, None, regex=True)
            target_date = datetime.now().date().strftime("%Y-%m-%d")
            cell_value = df.iloc[0, 0]
            if cell_value:
                target_date = cell_value

            # datetime.now().
            target_date = datetime.strptime(target_date, "%Y-%m-%d").date()
            sale_good_data = excel.parse(0, skiprows=3).dropna(how='all').replace(np.nan, None, regex=True)
            log_message = f"◯読込終了日時\n {target_date}\n\n"
            log_message += f"◯ファイル名\n {file_in.name}\n\n"
            log_message += f"◯不具合\n"
            is_error_import = False

            SaleGoodDetail.objects.filter(Q(target_month__month=target_date.month) & Q(target_month__year=target_date.year)) \
                .update(is_delete=True)

            try:
                for idx, data in enumerate(sale_good_data.to_dict(orient="records")):
                    logger.debug(f"sale_good_data: {data}")

                    if not data.get('product_id'):
                        is_error_import = True
                        log_message += f"登録されていない商品が確認されました\n"
                        log_message += f"{idx}【{data.get('product_id')}】: {data.get('product_name')} \n\n"
                        logger.debug(f"Product id to is required")
                        continue
                    product = ProductDetail.objects.filter(product_id=data.get('product_id')).first()
                    if not product:
                        is_error_import = True
                        log_message += f"登録されていない商品が確認されました\n"
                        log_message += f"{idx}【{data.get('product_id')}】: {data.get('product_name')} \n\n"
                        logger.debug(f"Product id to {data.get('product_id')} not found")
                        continue

                    if not data.get('dist_pattern_code'):
                        is_error_import = True
                        log_message += f"登録されていない分配コードが確認されました\n"
                        log_message += f"{idx}【{data.get('dist_pattern_code')}】: {data.get('product_name')} \n\n"
                        logger.debug(f"Sales code id to is required")
                        continue
                    sale_route = MstSaleRoute.objects.filter(route_name=data.get('dist_pattern_code')).first()
                    if not sale_route:
                        is_error_import = True
                        log_message += f"登録されていない分配コードが確認されました\n"
                        log_message += f"{idx}【{data.get('dist_pattern_code')}】: {data.get('product_name')} \n\n"
                        log_message += f"Sales code {data.get('dist_pattern_code')} not found\n"
                        logger.debug(f"Sales code id to {data.get('code')} not found")
                        continue

                    pattern_group = MstDistributionPatternGroupDetail.objects.filter(pattern_group=product.distribution_pattern_group, sale_route=sale_route).first()
                    if not pattern_group or not pattern_group.pattern:
                        logger.debug(f"Pattern data with route id {data.get('code')} and group code {product.distribution_pattern_group.group_code} not found")
                        raise GraphQLError(f"登録されていない分配パターンコードが存在します。分配パターンコード: {product.distribution_pattern_group.group_code}")

                    if not utils.valid_enum(data.get('report_type'), SaleGoodDetail.REPORT_TYPE):
                    # if data.get('report_type') not in ['daiki','accounting','live','silkroad','amazon',]:
                        is_error_import = True
                        log_message += f"登録されていない報告書種類が確認されました\n"
                        log_message += f"{idx}【{data.get('report_type')}】: {data.get('product_name')} \n\n"
                        logger.debug(f"Invalid report type {data.get('report_type')}")
                        continue

                    product_price = utils.to_float(product.price) if product.price else 0
                    sale_data_quantity = utils.to_float(data.get('quantity')) if data.get('quantity') else 0
                    try:
                        final_sale_price = sale_data_quantity * product_price / 1.1
                    except (ValueError, TypeError):
                        logger.debug(f"Invalid sale price {data.get('sale')}")
                        final_sale_price = 0

                    if data.get('report_type') and str(data.get('report_type')).lower() == 'daiki'.lower():
                        try:
                            product_deduction_rate = utils.to_float(product.deduction_rate) if product.deduction_rate else 0
                            product_deduction_rate = product_deduction_rate / 100
                            final_sale_price = sale_data_quantity * product_deduction_rate * product_price / 1.1
                        except (ValueError, TypeError):
                            logger.debug(f"Invalid daiki price {product.daiki_price}")
                            final_sale_price = 0

                    pattern_data = None
                    if pattern_group:
                        pattern_data = pattern_group.pattern

                    sale_good_detail = SaleGoodDetail.objects.filter(Q(target_month__month=target_date.month) & Q(target_month__year=target_date.year))
                    is_reward_generated = sale_good_detail.filter(
                        report_type=data.get('report_type'),
                        sale_good=sale_good,
                        product_detail=product,
                        sale_route=sale_route, is_reward_generated=True).exists()
                    if is_reward_generated:
                        logger.debug(f"Reward already generated for {idx}【{data.get('product_id')}】: {data.get('product_name')} data")
                        continue

                    sale_good_detail, created = sale_good_detail.update_or_create(
                        report_type=data.get('report_type'),
                        sale_good=sale_good,
                        product_detail=product,
                        sale_route=sale_route,
                        defaults={
                            'target_month': target_date,
                            'quantity': data.get('quantity'),
                            'original_sale': data.get('sale'),
                            'sale': utils.rounddown(final_sale_price),
                            'note': data.get('note'),
                            'is_delete': False,
                            'distribution_pattern': pattern_data,
                            'distribution_pattern_group': product.distribution_pattern_group,
                        }
                    )
                    logger.debug(f"registered sale: {utils.conv_model_to_dict(sale_good_detail)}")

                    sale_good.import_note = log_message
                    sale_good.save()
            except Exception as e:
                is_error_import = True
                logger.error(f"Error: {e}")
                sale_good.status = 'error'
                sale_good.import_note = log_message
                sale_good.save()
                if isinstance(e, GraphQLError):
                    raise e
                raise GraphQLError("Invalid data")

            if not is_error_import:
                log_message += f"不具合なし\n"

            sale_good.status = 'done'
            sale_good.save()
            common_usecase.send_email_to_admin("【分配計算システム | 売上データアップロード結果】", log_message)
        return utils.Responser(result="Success")

class ExportSaleGoodMutation(relay.ClientIDMutation):
    file = graphene.String()

    class Input:
        target_date = graphene.Date(required=True)

    @login_required
    def mutate_and_get_payload(root, info, target_date):
        sale_good = SaleGoodDetail.objects.filter(Q(target_month__month=target_date.month) & Q(target_month__year=target_date.year))
        if not sale_good:
            raise GraphQLError("Data not found")

        data_dict_of_lists = {}
        product_id_list = sale_good.values('product_detail__product_id').distinct()
        data_dict_of_lists['product_id'] = [product['product_detail__product_id'] for product in product_id_list]
        product_name_list = sale_good.values('product_detail__product_name').distinct()
        data_dict_of_lists['product_name'] = [product['product_detail__product_name'] for product in product_name_list]

        sale_list = sale_good.values('sale_route__route_name').distinct()
        for sale in sale_list:
            sale_route = sale['sale_route__route_name']
            data_dict_of_lists[sale_route] = []
            for product in product_id_list:
                sale_data = sale_good.filter(sale_route__route_name=sale_route, product_detail__product_id=product['product_detail__product_id']).annotate(
                    total_sales=Sum(F('sale'), output_field=models.DecimalField())
                ).first()
                if sale_data:
                    data_dict_of_lists[sale_route].append(sale_data.total_sales)
                else:
                    data_dict_of_lists[sale_route].append(0)

        df_from_dict = pd.DataFrame(data_dict_of_lists)

        buffer = BytesIO()
        # df_from_dict.to_excel('sale_good_export.xlsx', index=False)
        df_from_dict.to_excel(buffer, index=False, engine='openpyxl')
        buffer.seek(0)
        excel_base64 = base64.b64encode(buffer.read()).decode()

        return ExportSaleGoodMutation(file=excel_base64)

class GenerateSalesGoodRewardMutation(graphene.Mutation):
    class Arguments:
        target_date = graphene.Date(required=True)

    Output = utils.Responser

    @login_required
    def mutate(self, info, target_date):
        print(f"target_date: {target_date}")
        sale_good_list = SaleGoodDetail.objects.filter(Q(target_month__month=target_date.month) & Q(target_month__year=target_date.year) & Q(is_reward_generated=False))
        if not sale_good_list.exists():
            raise GraphQLError("当該月の報酬は既に算出されています")

        with transaction.atomic():
            for sale_good in sale_good_list:
                if not sale_good:
                    print(f"no salegood found")
                    logger.info(f"no salegood found")
                    continue

                if not sale_good.product_detail.product_category_group:
                    raise GraphQLError(f" 商品カテゴリグループと紐付いていない商品があります。　報酬が正しく生成できません。商品コード: {sale_good.product_detail.product_id}")

                if not sale_good.product_detail.artist.artist_category_group:
                    raise GraphQLError(f"アーティストカテゴリと紐づいていないアーティスト情報があります。報酬が正しく生成できません。アーティスト:  {sale_good.product_detail.artist.artist_name}")

                pattern_data = sale_good.distribution_pattern
                if not pattern_data:
                    pattern_group = MstDistributionPatternGroupDetail.objects.filter(pattern_group=sale_good.product_detail.distribution_pattern_group, sale_route=sale_good.sale_route).first()
                    if not pattern_group or not hasattr(pattern_group, 'pattern'):
                        print(f"pattern_group in sale data {sale_good.pk} not found")
                        logger.info(f"pattern_group in sale data {sale_good.pk} not found")
                        raise GraphQLError(f"登録されていない分配パターンコードが存在します。分配パターンコード: {sale_good.product_detail.distribution_pattern_group.group_code}")
                        # continue
                    pattern_data = pattern_group.pattern

                if not sale_good.product_detail:
                    continue
                orig_source_of_fund = sale_good.product_detail.price if sale_good.product_detail.price else 0
                orig_source_of_fund = utils.to_float(orig_source_of_fund)

                if orig_source_of_fund == 0:
                    logger.info(f"source_of_fund is 0 for {sale_good.pk}")
                    continue

                sale_data_quantity = utils.to_float(sale_good.quantity) if sale_good.quantity else 0
                source_of_fund = orig_source_of_fund * sale_data_quantity

                distributor_percentage = pattern_data.distributor_percentage if pattern_data.distributor_percentage else 0
                distributor_portion = utils.to_float(source_of_fund) * distributor_percentage / 100
                accounting_reward_portion = utils.to_float(source_of_fund) - distributor_portion
                accounting_reward, created = RewardDistributorPortion.objects.update_or_create(sale_data=sale_good,
                                                                  distributor_position=1,
                                                                    defaults={
                                                                        'company': pattern_data.accounting_company,
                                                                        'income': utils.rounddown(source_of_fund),
                                                                        'expense': utils.rounddown(distributor_portion),
                                                                        'reward': utils.rounddown(accounting_reward_portion),
                                                                    })
                accounting_mapping = RewardDistributionMapping.objects.filter(
                    target_month=sale_good.target_month,
                    product_category_group=sale_good.product_detail.product_category_group,
                    artist_group=sale_good.product_detail.artist.artist_category_group,
                    source_payer_id=pattern_data.accounting_company.id,
                    source_payee_id=pattern_data.accounting_company.id,
                    mapping_reward_layer=1
                ).select_for_update()

                if accounting_mapping.exists():
                    accounting_mapping = accounting_mapping.first()
                    accounting_mapping.source_sale_data = sale_good
                    accounting_mapping.payee_type = 1
                    accounting_mapping.payer_name = pattern_data.accounting_company.company_name
                    accounting_mapping.payee_name = pattern_data.accounting_company.company_name
                    accounting_mapping.distributed_price += utils.rounddown(source_of_fund)
                    accounting_mapping.net_profit += utils.rounddown(accounting_reward_portion)
                    accounting_mapping.save()
                else:
                    accounting_mapping = RewardDistributionMapping.objects.create(
                        target_month=sale_good.target_month,
                        product_category_group=sale_good.product_detail.product_category_group,
                        artist_group=sale_good.product_detail.artist.artist_category_group,
                        source_sale_data=sale_good,
                        source_dist_pattern=pattern_data,
                        source_payer_id=pattern_data.accounting_company.id,
                        source_payee_id=pattern_data.accounting_company.id,
                        mapping_reward_layer=1,
                        payee_type=1,
                        payer_name=pattern_data.accounting_company.company_name,
                        payee_name=pattern_data.accounting_company.company_name,
                        distributed_price=utils.rounddown(source_of_fund),
                        net_profit=utils.rounddown(accounting_reward_portion),
                    )

                print(f"accounting_reward: {utils.conv_model_to_dict(accounting_reward)}")
                logger.info(f"accounting_reward: {utils.conv_model_to_dict(accounting_reward)}")

                third_recipient_percentage = pattern_data.third_recipient_percentage if pattern_data.third_recipient_percentage else 0
                third_recipient_portion = utils.to_float(source_of_fund) * third_recipient_percentage / 100
                distributor_reward_portion = distributor_portion - third_recipient_portion
                dist_reward, created = RewardDistributorPortion.objects.update_or_create(sale_data=sale_good,
                                                                  distributor_position=2,
                                                                    defaults={
                                                                        'company': pattern_data.distributor_company,
                                                                        'income': utils.rounddown(distributor_portion),
                                                                        'expense': utils.rounddown(third_recipient_portion),
                                                                        'reward': utils.rounddown(distributor_reward_portion),
                                                                        })
                accounting_to_distributor_mapping = RewardDistributionMapping.objects.filter(
                    target_month=sale_good.target_month,
                    product_category_group=sale_good.product_detail.product_category_group,
                    artist_group=sale_good.product_detail.artist.artist_category_group,
                    source_payer_id=pattern_data.accounting_company.id,
                    source_payee_id=pattern_data.distributor_company.id,
                    mapping_reward_layer=2,
                    accounting_company_id=pattern_data.accounting_company.id,
                    accounting_company_name=pattern_data.accounting_company.company_name
                ).select_for_update()

                if accounting_to_distributor_mapping.exists():
                    accounting_to_distributor_mapping = accounting_to_distributor_mapping.first()
                    accounting_to_distributor_mapping.parent_id = accounting_mapping.id
                    accounting_to_distributor_mapping.payee_type = 1
                    accounting_to_distributor_mapping.source_sale_data = sale_good
                    accounting_to_distributor_mapping.payer_name = pattern_data.accounting_company.company_name
                    accounting_to_distributor_mapping.payee_name = pattern_data.distributor_company.company_name
                    accounting_to_distributor_mapping.accounting_company_id = pattern_data.accounting_company.id
                    accounting_to_distributor_mapping.accounting_company_name = pattern_data.accounting_company.company_name
                    accounting_to_distributor_mapping.distributed_price += utils.rounddown(distributor_portion)
                    accounting_to_distributor_mapping.net_profit += utils.rounddown(distributor_reward_portion)
                    accounting_to_distributor_mapping.save()
                else:
                    RewardDistributionMapping.objects.create(
                        parent_id=accounting_mapping.id,
                        target_month=sale_good.target_month,
                        product_category_group=sale_good.product_detail.product_category_group,
                        artist_group=sale_good.product_detail.artist.artist_category_group,
                        source_sale_data = sale_good,
                        source_dist_pattern = pattern_data,
                        source_payer_id=pattern_data.accounting_company.id,
                        source_payee_id=pattern_data.distributor_company.id,
                        mapping_reward_layer=2,
                        payee_type=1,
                        payer_name=pattern_data.accounting_company.company_name,
                        payee_name=pattern_data.distributor_company.company_name,
                        distributed_price=utils.rounddown(distributor_portion),
                        net_profit=utils.rounddown(distributor_reward_portion),
                        accounting_company_id=pattern_data.accounting_company.id,
                        accounting_company_name=pattern_data.accounting_company.company_name
                    )

                print(f"dist_reward: {utils.conv_model_to_dict(dist_reward)}")
                logger.info(f"dist_reward: {utils.conv_model_to_dict(dist_reward)}")

                recipent_portion = 0
                reward_distribution_mapping_list = []
                pattern_list = pattern_data.pattern_detail

                print(f"pattern_list: {pattern_list.count()}")
                logger.info(f"pattern_list: {pattern_list.count()}")

                third_payer_name = pattern_data.third_recipient_company.company_name
                for recipient_pattern_data in pattern_list.all():
                    if not recipient_pattern_data.recipient:
                        continue

                    deduction_rate = sale_good.product_detail.deduction_rate if sale_good.product_detail.deduction_rate else 0
                    recipent_percentage = recipient_pattern_data.percentage
                    if third_payer_name == '株eu SP' and deduction_rate != 0:
                        deduction_rate = utils.to_float(deduction_rate) / 100
                        recipient_amount = (orig_source_of_fund) * utils.to_float(utils.roundup(sale_data_quantity * (1-deduction_rate))) * (recipent_percentage/100)
                        recipient_amount = utils.rounddown(recipient_amount)
                    else:
                        recipient_amount = source_of_fund * (recipent_percentage/100)
                        recipient_amount = utils.rounddown(recipient_amount)
                    recipent_portion += recipient_amount

                    last_date = utils.get_last_date(sale_good.target_month, '%Y-%m-%d') if sale_good.target_month else utils.get_last_date(target_date)
                    PaySlipReward.objects.update_or_create(
                        reward_source_type=2,
                        reward_source_id=sale_good.pk,
                        sale_good=sale_good,
                        recipient=recipient_pattern_data.recipient,
                        defaults={
                            'amount': recipient_amount,
                            'billing_title': sale_good.product_detail.product_name,
                            'pay_date': utils.convert_to_date(last_date),
                        }
                    )

                    recipient_reward, created = RecipientRewardDistribution.objects.update_or_create(
                        sale_data=sale_good,
                        recipient=recipient_pattern_data.recipient,
                        defaults={
                            'reward': recipient_amount,
                        }
                    )

                    recipient_mapping = RewardDistributionMapping.objects.filter(
                        target_month=sale_good.target_month,
                        product_category_group=sale_good.product_detail.product_category_group,
                        artist_group=sale_good.product_detail.artist.artist_category_group,
                        source_payer_id=pattern_data.third_recipient_company.id,
                        source_payee_id=recipient_pattern_data.recipient.id,
                        mapping_reward_layer=4,
                        accounting_company_id=pattern_data.accounting_company.id,
                        accounting_company_name=pattern_data.accounting_company.company_name,
                        distributor_company_id=pattern_data.distributor_company.id,
                        distributor_company_name=pattern_data.distributor_company.company_name,
                        third_level_company_id=pattern_data.third_recipient_company.id,
                        third_level_company_name=pattern_data.third_recipient_company.company_name,
                    ).select_for_update()

                    if recipient_mapping.exists():
                        recipient_mapping = recipient_mapping.first()
                        recipient_mapping.parent_id = accounting_mapping.id
                        recipient_mapping.payee_type = 2
                        recipient_mapping.source_sale_data = sale_good
                        recipient_mapping.source_dist_pattern_detail = recipient_pattern_data
                        recipient_mapping.payer_name = pattern_data.third_recipient_company.company_name
                        recipient_mapping.payee_name = recipient_pattern_data.recipient.recipient_name
                        recipient_mapping.accounting_company_id = pattern_data.accounting_company.id
                        recipient_mapping.accounting_company_name = pattern_data.accounting_company.company_name
                        recipient_mapping.distributor_company_id = pattern_data.distributor_company.id
                        recipient_mapping.distributor_company_name = pattern_data.distributor_company.company_name
                        recipient_mapping.third_level_company_id = pattern_data.third_recipient_company.id
                        recipient_mapping.third_level_company_name = pattern_data.third_recipient_company.company_name
                        recipient_mapping.distributed_price += utils.rounddown(recipient_amount)
                        recipient_mapping.net_profit += utils.rounddown(recipient_amount)
                        recipient_mapping.save()
                    else:
                        RewardDistributionMapping.objects.create(
                            parent_id=accounting_mapping.id,
                            target_month=sale_good.target_month,
                            product_category_group=sale_good.product_detail.product_category_group,
                            artist_group=sale_good.product_detail.artist.artist_category_group,
                            source_sale_data = sale_good,
                            source_dist_pattern = pattern_data,
                            source_dist_pattern_detail = recipient_pattern_data,
                            source_payer_id=pattern_data.third_recipient_company.id,
                            source_payee_id=recipient_pattern_data.recipient.id,
                            mapping_reward_layer=4,
                            payee_type=2,
                            payer_name=pattern_data.third_recipient_company.company_name,
                            payee_name=recipient_pattern_data.recipient.recipient_name,
                            distributed_price=utils.rounddown(recipient_amount),
                            net_profit=utils.rounddown(recipient_amount),
                            accounting_company_id=pattern_data.accounting_company.id,
                            accounting_company_name=pattern_data.accounting_company.company_name,
                            distributor_company_id=pattern_data.distributor_company.id,
                            distributor_company_name=pattern_data.distributor_company.company_name,
                            third_level_company_id=pattern_data.third_recipient_company.id,
                            third_level_company_name=pattern_data.third_recipient_company.company_name
                        )

                    print(f"recipient_reward: {utils.conv_model_to_dict(recipient_reward)}")
                    logger.info(f"recipient_reward: {utils.conv_model_to_dict(recipient_reward)}")

                third_recipient_reward_portion = third_recipient_portion - recipent_portion
                third_dist_reward, created = RewardDistributorPortion.objects.update_or_create(sale_data=sale_good,
                                                                  distributor_position=3,
                                                                    defaults={
                                                                        'company': pattern_data.third_recipient_company,
                                                                        'income': utils.rounddown(third_recipient_portion),
                                                                        'expense': utils.rounddown(recipent_portion),
                                                                        'reward': utils.rounddown(third_recipient_reward_portion),
                                                                        })
                distributor_to_third_company = RewardDistributionMapping.objects.filter(
                    target_month=sale_good.target_month,
                    product_category_group=sale_good.product_detail.product_category_group,
                    artist_group=sale_good.product_detail.artist.artist_category_group,
                    source_payer_id=pattern_data.distributor_company.id,
                    source_payee_id=pattern_data.third_recipient_company.id,
                    mapping_reward_layer=3,
                    accounting_company_id=pattern_data.accounting_company.id,
                    accounting_company_name=pattern_data.accounting_company.company_name,
                    distributor_company_id=pattern_data.distributor_company.id,
                    distributor_company_name=pattern_data.distributor_company.company_name
                ).select_for_update()

                third_recipient_net_profit = third_recipient_portion - recipent_portion
                if distributor_to_third_company.exists():
                    distributor_to_third_company = distributor_to_third_company.first()
                    distributor_to_third_company.parent_id = accounting_mapping.id
                    distributor_to_third_company.payee_type = 1
                    distributor_to_third_company.source_sale_data = sale_good
                    distributor_to_third_company.payer_name = pattern_data.distributor_company.company_name
                    distributor_to_third_company.payee_name = pattern_data.third_recipient_company.company_name
                    distributor_to_third_company.accounting_company_id = pattern_data.accounting_company.id
                    distributor_to_third_company.accounting_company_name = pattern_data.accounting_company.company_name
                    distributor_to_third_company.distributor_company_id = pattern_data.distributor_company.id
                    distributor_to_third_company.distributor_company_name = pattern_data.distributor_company.company_name
                    distributor_to_third_company.distributed_price += utils.rounddown(third_recipient_portion)
                    distributor_to_third_company.net_profit += utils.rounddown(third_recipient_net_profit)
                    distributor_to_third_company.save()
                else:
                    distributor_to_third_company = RewardDistributionMapping.objects.create(
                        parent_id=accounting_mapping.id,
                        target_month=sale_good.target_month,
                        product_category_group=sale_good.product_detail.product_category_group,
                        artist_group=sale_good.product_detail.artist.artist_category_group,
                        source_sale_data = sale_good,
                        source_dist_pattern = pattern_data,
                        source_payer_id=pattern_data.distributor_company.id,
                        source_payee_id=pattern_data.third_recipient_company.id,
                        mapping_reward_layer=3,
                        payee_type=1,
                        payer_name=pattern_data.distributor_company.company_name,
                        payee_name=pattern_data.third_recipient_company.company_name,
                        distributed_price=utils.rounddown(third_recipient_portion),
                        net_profit=utils.rounddown(third_recipient_net_profit),
                        accounting_company_id=pattern_data.accounting_company.id,
                        accounting_company_name=pattern_data.accounting_company.company_name,
                        distributor_company_id=pattern_data.distributor_company.id,
                        distributor_company_name=pattern_data.distributor_company.company_name
                    )

                # print(f"third_dist_reward: {utils.conv_model_to_dict(third_dist_reward)}")
                # logger.info(f"third_dist_reward: {utils.conv_model_to_dict(third_dist_reward)}")

                # print(f"third_dist_reward_mapping: {utils.conv_model_to_dict(distributor_to_third_company)}")
                # logger.info(f"third_dist_reward_mapping: {utils.conv_model_to_dict(distributor_to_third_company)}")

                sale_good.is_reward_generated = True
                sale_good.save()

        return utils.Responser(result="Success")

class SaleGoodReportGeneration(graphene.Mutation):
    class Arguments:
        target_date = graphene.Date(required=True)

    Output = utils.Responser

    @login_required
    def mutate(self, info, target_date: datetime):
        formatted_target_date = target_date.strftime("%Y-%m")
        filename = f"sale_report_{formatted_target_date}_{utils.generate_epoch_time()}.xlsx"
        SaleReport.objects.create(
            target_month=target_date,
            file_status='in_progress',
            file_name=filename
        )
        async_task(usecase.generate_sale_report, target_date, None, filename)

        return utils.Responser(result="Success")

class SaleGoodPeriodicReportGeneration(graphene.Mutation):
    class Arguments:
        start_date = graphene.Date(required=True)
        end_date = graphene.Date(required=True)

    Output = utils.Responser

    @login_required
    def mutate(self, info, start_date: datetime, end_date: datetime):
        if end_date < start_date:
            raise GraphQLError("End date cannot be earlier than start date")

        # check if start date month and end date month is less than 12 month
        # Calculate months between dates
        months_diff = (end_date.year - start_date.year) * 12 + (end_date.month - start_date.month)
        if months_diff > 12 or months_diff < 0:
            raise GraphQLError("12ヶ月以内で対象期間を設定してください")

        formatted_start_date = start_date.strftime("%Y-%m")
        formatted_end_date = end_date.strftime("%Y-%m")
        filename = f"sale_report_{formatted_start_date}_{formatted_end_date}_{utils.generate_epoch_time()}.xlsx"
        SaleReport.objects.create(
            target_month=start_date,
            target_month_end=end_date,
            file_status='in_progress',
            file_name=filename
        )
        async_task(usecase.generate_sale_report, start_date, end_date, filename)

        return utils.Responser(result="Success")

class RoyaltyRewardGeneration(graphene.Mutation):
    class Arguments:
        start_date = graphene.Date(required=True)
        end_date = graphene.Date(required=True)
        pay_amount = graphene.Float(required=True)
        pay_date = graphene.Date(required=True)

    Output = utils.Responser

    @login_required
    def mutate(self, info, start_date, end_date, pay_amount, pay_date):
        start_year = utils.convert_to_str(start_date, "%Y")
        start_month = utils.convert_to_str(start_date, "%m")
        end_month = utils.convert_to_str(end_date, "%m")
        filename = f"刀剣DL社プロデュース印税_{start_year}年{start_month}月〜{end_month}月"

        if start_date > end_date:
            raise GraphQLError("Invalid date range")

        if int(end_month) - int(start_month) > 2:
            raise GraphQLError("Date range is too long")

        report = RoyaltyReport.objects.filter(
            file_name__icontains=f"{filename}"
        )
        report_count = report.count()
        if report_count > 0:
            filename = f"{filename}({report_count})"

        filename = f"{filename}.pdf"

        RoyaltyReport.objects.create(
            file_status='in_progress',
            file_name=filename
        )
        args = {
			"start_date": start_date,
			"end_date": end_date,
			"pay_amount": pay_amount,
			"pay_date": pay_date,
		}
        # usecase.generate_royalty_report(filename, **args)
        async_task(usecase.generate_royalty_report, filename, **args)

        return utils.Responser(result="Success")

class CreateCommitteeReport(DjangoModelFormMutation):
    committee_report = graphene.Field(CommitteeReportNode)

    class Meta:
        form_class = CommitteeReportForm
        return_field_name = 'committee_report'

    @classmethod
    def get_form_kwargs(cls, root, info, **input):
        kwargs = super().get_form_kwargs(root, info, **input)
        data = kwargs.get('data')
        data['logged_user'] = info.context.user
        kwargs['data'] = data
        return kwargs

    @classmethod
    @login_required
    def perform_mutate(cls, form, info):
        return super().perform_mutate(form, info)

class DeleteCommitteeReportData(relay.ClientIDMutation):  # Update the class definition
    Output = utils.Responser

    class Input:
        id = graphene.ID(required=True)

    @login_required
    def mutate_and_get_payload(root, info, id):
        report = CommitteeReport.objects.filter(pk=id).first()
        if not report:
            raise GraphQLError("Data not found")

        if not report.calculation_status == 'done':
            raise GraphQLError("Calculation not done yet")

        if report.is_delete:
            raise GraphQLError("Data already deleted")

        report.is_delete = True
        report.save()

        report.sale_good_report.update(is_delete=True)

        return utils.Responser(result="Success")

class CreateArchiveStreaming(DjangoModelFormMutation):
    archive_streaming = graphene.Field(ReportArchiveStreamingNode)

    class Meta:
        form_class = ReportArchiveStreamingForm
        return_field_name = 'archive_streaming'

    @classmethod
    def get_form_kwargs(cls, root, info, **input):
        kwargs = super().get_form_kwargs(root, info, **input)
        data = kwargs.get('data')
        data['logged_user'] = info.context.user
        kwargs['data'] = data
        return kwargs

    @classmethod
    @login_required
    def perform_mutate(cls, form, info):
        return super().perform_mutate(form, info)

class DeleteArchiveStreaming(relay.ClientIDMutation):  # Update the class definition
    Output = utils.Responser

    class Input:
        id = graphene.ID(required=True)

    @login_required
    def mutate_and_get_payload(root, info, id):
        report = ReportArchiveStreaming.objects.filter(pk=id).first()
        if not report:
            raise GraphQLError("Data not found")

        if report.is_delete:
            raise GraphQLError("Data already deleted")

        report.is_delete = True
        report.save()

        return utils.Responser(result="Success")

class CreateRakutenStreamService(DjangoModelFormMutation):
    report_rakuten_stream_service = graphene.Field(ReportRakutenStreamServiceNode)

    class Meta:
        form_class = ReportRakutenStreamServiceForm
        return_field_name = 'report_rakuten_stream_service'

    @classmethod
    def get_form_kwargs(cls, root, info, **input):
        kwargs = super().get_form_kwargs(root, info, **input)
        data = kwargs.get('data')
        data['logged_user'] = info.context.user
        kwargs['data'] = data
        return kwargs

    @classmethod
    @login_required
    def perform_mutate(cls, form, info):
        return super().perform_mutate(form, info)

class DeleteRakutenStreamService(relay.ClientIDMutation):
    Output = utils.Responser

    class Input:
        id = graphene.ID(required=True)

    @login_required
    def mutate_and_get_payload(root, info, id):
        report = ReportRakutenStreamService.objects.filter(pk=id).first()
        if not report:
            raise GraphQLError("Data not found")

        if report.is_delete:
            raise GraphQLError("Data already deleted")

        report.is_delete = True
        report.save()

        return utils.Responser(result="Success")

class RegenerateCommitteeReportMutation(graphene.Mutation):
    class Arguments:
        id = graphene.ID(required=True)

    Output = utils.Responser

    @login_required
    def mutate(self, info, id):
        report = CommitteeReport.objects.filter(pk=id).first()
        if not report:
            raise GraphQLError("Data not found")

        if not report.calculation_status == 'done':
            raise GraphQLError("Calculation not done yet")

        report.calculation_status = 'in_progress'
        report.file_name = None
        report.report_file.delete()
        report.save()

        async_task(usecase.generate_committee_report, report.pk)

        return utils.Responser(result="Success")

class UploadProductMutation(graphene.Mutation):
    class Arguments:
        file_in = Upload(required=True)

    Output = utils.Responser

    @login_required
    def mutate(self, info, file_in):
        user = info.context.user
        if not user.is_admin():
            raise GraphQLError("Permission denied")

        excel = pd.ExcelFile(file_in)
        excel = excel.parse(0).dropna(how='all').replace(np.nan, None, regex=True)

        expected_column = ['product_id', 'media_title_code', 'product_name', 'segment_id', 'distribution_pattern_group_id', 'artist_name', 'daiki_price', 'price', 'product_category', 'product_category_note', 'issue_date', 'tax', 'holding_company_name', 'shown_report']
        for col in expected_column:
            if col not in excel.columns:
                logger.error(f"Column {col} not found in the excel file")
                raise GraphQLError("Excelフォーマットが正しくありません")

        try:
            excel[['issue_date']] = excel[['issue_date']].astype(object).where(excel[['issue_date']].notnull(), None)
        except Exception as e:
            logger.error(f"Error: {e}")
            raise GraphQLError("Excelフォーマットが正しくありません")

        async_task(usecase.register_product, excel, user.email, file_in.name)
        return utils.Responser(result="商品情報を更新後、管理者宛に更新結果をメールで通知します")

class ExportProductMutation(graphene.Mutation):
    Output = utils.Responser

    @login_required
    def mutate(self, info, **kwargs):
        user = info.context.user
        if not user.is_admin():
            raise GraphQLError("Permission denied")

        logger.debug(f"current logged user: {user.email}")

        async_task(usecase.export_product_and_send_email, user.email)
        return utils.Responser(result="商品情報Excel生成後、管理者へファイルを添付したメールを送付します")

class CreateGachaInformation(DjangoModelFormMutation):
    gacha_information = graphene.Field(GachaInformationNode)

    class Meta:
        form_class = GachaInformationForm
        return_field_name = 'gacha_information'

    @classmethod
    def get_form_kwargs(cls, root, info, **input):
        kwargs = super().get_form_kwargs(root, info, **input)
        data = kwargs.get('data')
        data['logged_user'] = info.context.user
        kwargs['data'] = data
        return kwargs

    @classmethod
    @login_required
    def perform_mutate(cls, form, info):
        return super().perform_mutate(form, info)

class DeleteGachaInformation(relay.ClientIDMutation):
    Output = utils.Responser

    class Input:
        id = graphene.ID(required=True)

    @login_required
    def mutate_and_get_payload(root, info, id):
        gacha = GachaInformation.objects.filter(pk=id).first()
        if not gacha:
            raise GraphQLError("Data not found")

        if gacha.is_delete:
            raise GraphQLError("Data already deleted")

        gacha.is_delete = True
        gacha.save()

        return utils.Responser(result="Success")
