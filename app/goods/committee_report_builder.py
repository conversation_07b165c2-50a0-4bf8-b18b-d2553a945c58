from app.common import utils
from app.goods.models import ReportArchiveStreaming, ReportRakutenStreamService, SaleGoodReport

class CommitteeReportBuilder:
    def __init__(self, **kwargs) -> None:
        self.kwargs = kwargs

    def build_table_silkroad_store(self, sale_good_reports: SaleGoodReport, month_list):
        height_y1 = self.kwargs.get('height_y1', 570)
        silk_store_good_sale = sale_good_reports.filter(sale_route__route_name__iexact='Silkroad Store')
        silk_road_product_table_data = self.kwargs.get('silk_road_product_table_data', [
            ["商品コード", "商品名", "出荷数（戻り数控除済み）", "", "", "", "","単価", "売上金額", "", "", "", "", ""],
            ["", "", "{1st month}月", "{2nd month}月", "{3rd month}月", "販売数合計", "","（税込）", "{1st month}月", "{2nd month}月", "{3rd month}月", "売上金額合計", "販売事業部手数料（30%）", "制作委員会様入金額"],
        ])

        for i, month in enumerate(month_list):
            month = month.month
            silk_road_product_table_data[1][i+2] = f"{month}月"
            silk_road_product_table_data[1][i+8] = f"{month}月"

        total_amount_data = ["", "小計", 0, 0, 0, 0, "", "", 0, 0, 0, 0, 0, 0]
        silk_road_table_data_height = height_y1 - 95
        total_include_tax = self.kwargs.get('total_include_tax', 0)
        total_exclude_tax = 0
        fee_include_tax = self.kwargs.get('fee_include_tax', 0)
        for sale_good in silk_store_good_sale:
            if not sale_good.product_detail:
                continue

            product_data = []
            product_data.append(sale_good.product_detail.product_id)
            product_data.append(utils.trim_text(sale_good.product_detail.product_name, 25))
            # product_data.append(sale_good.product_detail.product_name)
            product_data.append(utils.format_amount(sale_good.first_sale_amount))
            product_data.append(utils.format_amount(sale_good.second_sale_amount))
            product_data.append(utils.format_amount(sale_good.third_sale_amount))
            product_data.append(utils.format_amount(sale_good.total_sales_amount))
            product_data.append("")
            product_data.append(utils.format_currency_value(int(sale_good.product_detail.price)))
            product_data.append(utils.format_currency_value(int(sale_good.first_sale_price)))
            product_data.append(utils.format_currency_value(int(sale_good.second_sale_price)))
            product_data.append(utils.format_currency_value(int(sale_good.third_sale_price)))
            product_data.append(utils.format_currency_value(int(sale_good.total_sales_price)))
            product_data.append(utils.format_currency_value(int(sale_good.fee)))
            product_data.append(utils.format_currency_value(int(sale_good.payment)))

            silk_road_product_table_data.append(product_data)

            total_amount_data[2] += sale_good.first_sale_amount
            total_amount_data[3] += sale_good.second_sale_amount
            total_amount_data[4] += sale_good.third_sale_amount
            total_amount_data[5] += sale_good.total_sales_amount

            total_amount_data[8] += sale_good.first_sale_price
            total_amount_data[9] += sale_good.second_sale_price
            total_amount_data[10] += sale_good.third_sale_price
            total_amount_data[11] += sale_good.total_sales_price
            total_amount_data[12] += sale_good.fee
            total_amount_data[13] += sale_good.payment

            silk_road_table_data_height -= 11

        total_exclude_tax = total_amount_data[11]
        fee_exclude_tax = total_amount_data[12]
        total_include_tax = total_exclude_tax / 1.1
        fee_include_tax = fee_exclude_tax / 1.1
        first_total_amount_data = total_amount_data[0:8]
        second_total_amount_data = [utils.format_currency_value(int(data or 0)) for data in total_amount_data[8:14]]
        total_amount_data = first_total_amount_data + second_total_amount_data
        total_amount_data = [str(data) for data in total_amount_data]

        silk_road_product_table_data.append(total_amount_data)
        silk_road_product_table_data.append(["", "", "", "", "", "", "", "", "", "", "", "", "", ""])
        silk_road_product_table_data.append(["", "", "", "", "", "", "", "", "", "", "消費税", "税抜き金額", "税抜き金額", ""])
        silk_road_product_table_data.append(["", "", "", "", "", "", "", "", "", "", "10%", f"{utils.format_currency_value(total_include_tax)}", f"{utils.format_currency_value(fee_include_tax)}", ""])

        self.kwargs = {
            "height_y1": height_y1,
            "silroad_product_table_data": silk_road_product_table_data,
            "silk_road_table_data_height": silk_road_table_data_height,
            "total_include_tax": total_include_tax,
            "fee_include_tax": fee_include_tax,
        }

    def build_table_silkroad_store_dvd(self, sale_good_reports: SaleGoodReport, month_list):
        height_y1 = self.kwargs.get('height_y1', 570)
        silk_store_dvd_sale = sale_good_reports.filter(
            sale_route__route_name__iexact='Silkroad Store', product_detail__product_category__icontains='DVD'
        )
        silk_road_product_table_data = self.kwargs.get('silk_road_product_table_data', [
            ["商品コード", "商品名", "出荷数（戻り数控除済み）", "", "", "", "","単価", "売上金額", "", "", "", "", ""],
            ["", "", "{1st month}月", "{2nd month}月", "{3rd month}月", "販売数合計", "","（税込）", "{1st month}月", "{2nd month}月", "{3rd month}月", "売上金額合計", "販売事業部手数料（30%）", "制作委員会様入金額"],
        ])

        for i, month in enumerate(month_list):
            month = month.month
            silk_road_product_table_data[1][i+2] = f"{month}月"
            silk_road_product_table_data[1][i+8] = f"{month}月"

        silk_road_product_footer_data = ["", "小計", 0, 0, 0, 0, "", "", 0, 0, 0, 0, 0, 0]
        silk_road_table_data_height = height_y1 - 85
        total_include_tax = self.kwargs.get('total_include_tax', 0)
        total_exclude_tax = 0
        fee_include_tax = self.kwargs.get('fee_include_tax', 0)
        for sale_good in silk_store_dvd_sale:
            if not sale_good.product_detail:
                continue

            product_data = []
            product_data.append(sale_good.product_detail.product_id)
            product_data.append(utils.trim_text(sale_good.product_detail.product_name, 25))
            # product_data.append(sale_good.product_detail.product_name)
            product_data.append(utils.format_amount(sale_good.first_sale_amount))
            product_data.append(utils.format_amount(sale_good.second_sale_amount))
            product_data.append(utils.format_amount(sale_good.third_sale_amount))
            product_data.append(utils.format_amount(sale_good.total_sales_amount))
            product_data.append("")
            product_data.append(utils.format_currency_value(int(sale_good.product_detail.price)))
            product_data.append(utils.format_currency_value(int(sale_good.first_sale_price)))
            product_data.append(utils.format_currency_value(int(sale_good.second_sale_price)))
            product_data.append(utils.format_currency_value(int(sale_good.third_sale_price)))
            product_data.append(utils.format_currency_value(int(sale_good.total_sales_price)))
            product_data.append(utils.format_currency_value(int(sale_good.fee)))
            product_data.append(utils.format_currency_value(int(sale_good.payment)))

            silk_road_product_table_data.append(product_data)

            silk_road_product_footer_data[2] += sale_good.first_sale_amount
            silk_road_product_footer_data[3] += sale_good.second_sale_amount
            silk_road_product_footer_data[4] += sale_good.third_sale_amount
            silk_road_product_footer_data[5] += sale_good.total_sales_amount

            silk_road_product_footer_data[8] += sale_good.first_sale_price
            silk_road_product_footer_data[9] += sale_good.second_sale_price
            silk_road_product_footer_data[10] += sale_good.third_sale_price
            silk_road_product_footer_data[11] += sale_good.total_sales_price
            silk_road_product_footer_data[12] += sale_good.fee
            silk_road_product_footer_data[13] += sale_good.payment

            silk_road_table_data_height -= 11

        total_exclude_tax = silk_road_product_footer_data[11]
        fee_exclude_tax = silk_road_product_footer_data[12]
        total_include_tax = total_exclude_tax / 1.1
        fee_include_tax = fee_exclude_tax / 1.1
        first_total_amount_data = silk_road_product_footer_data[0:8]
        second_total_amount_data = [utils.format_currency_value(int(data or 0)) for data in silk_road_product_footer_data[8:14]]
        silk_road_product_footer_data = first_total_amount_data + second_total_amount_data
        silk_road_product_footer_data = [str(data) for data in silk_road_product_footer_data]

        silk_road_product_table_data.append(silk_road_product_footer_data)
        silk_road_product_table_data.append(["", "", "", "", "", "", "", "", "", "", "", "", "", ""])
        silk_road_product_table_data.append(["", "", "", "", "", "", "", "", "", "", "消費税", "税抜き金額", "税抜き金額", ""])
        silk_road_product_table_data.append(["", "", "", "", "", "", "", "", "", "", "10%", f"{utils.format_currency_value(total_include_tax)}", f"{utils.format_currency_value(fee_include_tax)}", ""])

        self.kwargs = {
            "height_y1": height_y1,
            "silroad_product_table_data": silk_road_product_table_data,
            "silk_road_table_data_height": silk_road_table_data_height,
            "total_include_tax": total_include_tax,
            "fee_include_tax": fee_include_tax,
        }

    def build_table_amazon_dvd(self, sale_good_reports: SaleGoodReport, month_list):
        height_y1 = self.kwargs.get('height_y1', 570)
        amazon_dvd_sale = sale_good_reports.filter(
            sale_route__route_name__iexact='Amazon', product_detail__product_category__icontains='DVD'
        )
        amazon_product_table_data = self.kwargs.get('amazon_product_table_data', [
            ["商品コード", "商品名", "出荷数（戻り数控除済み）", "", "", "", "","単価", "売上金額", "", "", "", "", ""],
            ["", "", "{1st month}月", "{2nd month}月", "{3rd month}月", "販売数合計", "","（税込）", "{1st month}月", "{2nd month}月", "{3rd month}月", "売上金額合計", "販売事業部手数料（30%）", "制作委員会様入金額"],
        ])

        for i, month in enumerate(month_list):
            month = month.month
            amazon_product_table_data[1][i+2] = f"{month}月"
            amazon_product_table_data[1][i+8] = f"{month}月"

        amazon_product_footer_data = ["", "小計", 0, 0, 0, 0, "", "", 0, 0, 0, 0, 0, 0]
        amazon_table_data_height = self.kwargs.get('amazon_table_data_height', height_y1 - 85)
        total_include_tax = self.kwargs.get('total_include_tax', 0)
        total_exclude_tax = 0
        fee_include_tax = self.kwargs.get('fee_include_tax', 0)
        for sale_good in amazon_dvd_sale:
            if not sale_good.product_detail:
                continue

            product_data = []
            product_data.append(sale_good.product_detail.product_id)
            product_data.append(utils.trim_text(sale_good.product_detail.product_name, 25))
            # product_data.append(sale_good.product_detail.product_name)
            product_data.append(utils.format_amount(sale_good.first_sale_amount))
            product_data.append(utils.format_amount(sale_good.second_sale_amount))
            product_data.append(utils.format_amount(sale_good.third_sale_amount))
            product_data.append(utils.format_amount(sale_good.total_sales_amount))
            product_data.append("")
            product_data.append(utils.format_currency_value(int(sale_good.product_detail.price)))
            product_data.append(utils.format_currency_value(int(sale_good.first_sale_price)))
            product_data.append(utils.format_currency_value(int(sale_good.second_sale_price)))
            product_data.append(utils.format_currency_value(int(sale_good.third_sale_price)))
            product_data.append(utils.format_currency_value(int(sale_good.total_sales_price)))
            product_data.append(utils.format_currency_value(int(sale_good.fee)))
            product_data.append(utils.format_currency_value(int(sale_good.payment)))

            amazon_product_table_data.append(product_data)

            amazon_product_footer_data[2] += sale_good.first_sale_amount
            amazon_product_footer_data[3] += sale_good.second_sale_amount
            amazon_product_footer_data[4] += sale_good.third_sale_amount
            amazon_product_footer_data[5] += sale_good.total_sales_amount

            amazon_product_footer_data[8] += sale_good.first_sale_price
            amazon_product_footer_data[9] += sale_good.second_sale_price
            amazon_product_footer_data[10] += sale_good.third_sale_price
            amazon_product_footer_data[11] += sale_good.total_sales_price
            amazon_product_footer_data[12] += sale_good.fee
            amazon_product_footer_data[13] += sale_good.payment

            amazon_table_data_height -= 11

        total_exclude_tax = amazon_product_footer_data[11]
        fee_exclude_tax = amazon_product_footer_data[12]
        total_include_tax = total_exclude_tax / 1.1
        fee_include_tax = fee_exclude_tax / 1.1
        first_total_amount_data = amazon_product_footer_data[0:8]
        second_total_amount_data = [utils.format_currency_value(int(data or 0)) for data in amazon_product_footer_data[8:14]]
        amazon_product_footer_data = first_total_amount_data + second_total_amount_data
        amazon_product_footer_data = [str(data) for data in amazon_product_footer_data]

        amazon_product_table_data.append(amazon_product_footer_data)
        amazon_product_table_data.append(["", "", "", "", "", "", "", "", "", "", "", "", "", ""])
        amazon_product_table_data.append(["", "", "", "", "", "", "", "", "", "", "消費税", "税抜き金額", "税抜き金額", ""])
        amazon_product_table_data.append(["", "", "", "", "", "", "", "", "", "", "10%", f"{utils.format_currency_value(total_include_tax)}", f"{utils.format_currency_value(fee_include_tax)}", ""])

        self.kwargs = {
            "height_y1": height_y1,
            "amazon_product_table_data": amazon_product_table_data,
            "amazon_table_data_height": amazon_table_data_height,
            "total_include_tax": total_include_tax,
            "fee_include_tax": fee_include_tax,
        }

    def build_table_archive_goku(self, archive_streaming_goku: ReportArchiveStreaming):
        height = self.kwargs.get('height', 770)
        goku_total_sales = self.kwargs.get('goku_total_sales', 0)
        archive_streaming_total_sales = self.kwargs.get('archive_streaming_total_sales', 0)
        goku_archive_table_data = [
            ["対象月", "品名", "件数", "売上金額", "料率（％）", "支払額",],
        ]
        self.kwargs.get('goku_archive_table_data', goku_archive_table_data)

        goku_total_payment = 0
        archive_table_data_height = height - 140
        for archive in archive_streaming_goku:
            goku_data = []
            goku_data.append(archive.month.strftime("%B"))
            goku_data.append(utils.trim_text(archive.product_detail.product_name, 10))
            goku_data.append(utils.format_amount(archive.amount))
            goku_data.append(utils.format_currency_value(archive.sales))
            goku_data.append(utils.format_percentage(archive.rate))
            goku_data.append(utils.format_currency_value(archive.total_payment))

            goku_total_payment += archive.total_payment
            goku_total_sales += archive.sales
            archive_streaming_total_sales += archive.sales

            goku_archive_table_data.append(goku_data)
            archive_table_data_height -= 10

        goku_archive_table_data.append(["", "", "", "", "", ""])
        goku_archive_table_data.append(["", "", "", "", "税抜き金額", f"{utils.format_currency_value(goku_total_payment)}"])

        self.kwargs = {
            "height": height,
            "goku_archive_table_data": goku_archive_table_data,
            "archive_table_data_height": archive_table_data_height,
            "goku_total_payment": goku_total_payment,
            "goku_total_sales": goku_total_sales,
            "archive_streaming_total_sales": archive_streaming_total_sales,
        }

    def build_table_rakuten_showtime(self, rakuten_showtime: ReportRakutenStreamService, month_list):
        height = self.kwargs.get('height', 770)
        rakuten_showtime_table_data = [
            ["配信月", "販売数", "",],
            # ["July", "0", f"{utils.format_currency_value(0)}"],
        ]
        rakuten_showtime_table_data = self.kwargs.get('rakuten_showtime_table_data', rakuten_showtime_table_data)

        rakuten_table_data_height = height - 150
        rakuten_showtime_sale_amount = [0,0,0]
        rakuten_showtime_sale_price = [0,0,0]
        rakuten_showtime_carryover_total = 0
        rakuten_showtime_total = 0
        for rakuten in rakuten_showtime:
            rakuten_showtime_sale_amount[0] += rakuten.first_sale_amount
            rakuten_showtime_sale_amount[1] += rakuten.second_sale_amount
            rakuten_showtime_sale_amount[2] += rakuten.third_sale_amount

            rakuten_showtime_sale_price[0] += rakuten.first_sale_price
            rakuten_showtime_sale_price[1] += rakuten.second_sale_price
            rakuten_showtime_sale_price[2] += rakuten.third_sale_price
            total_sale_price = rakuten.first_sale_price + rakuten.second_sale_price + rakuten.third_sale_price

            rakuten_showtime_carryover_total += rakuten.carryover
            rakuten_showtime_total += (rakuten.carryover + total_sale_price)

        for i, date_data in enumerate(month_list):
            rakuten_showtime_table_data.append([f'{date_data.strftime("%m")}月', f"{rakuten_showtime_sale_amount[i]}", f"{utils.format_currency_value(rakuten_showtime_sale_price[i])}"])
            rakuten_table_data_height -= 10

        rakuten_showtime_table_data.append(["前期繰越分（税抜き）", "", f"{utils.format_currency_value(rakuten_showtime_carryover_total)}"])
        rakuten_showtime_table_data.append(["合計", "", f"{utils.format_currency_value(rakuten_showtime_total)}"])

        self.kwargs = {
            "height": height,
            "rakuten_showtime_table_data": rakuten_showtime_table_data,
            "rakuten_table_data_height": rakuten_table_data_height,
            "rakuten_showtime_sale_amount": rakuten_showtime_sale_amount,
            "rakuten_showtime_sale_price": rakuten_showtime_sale_price,
            "rakuten_showtime_carryover_total": rakuten_showtime_carryover_total,
            "rakuten_showtime_total": rakuten_showtime_total,
        }

    def build_table_rakuten_tv(self, rakuten_tv: ReportRakutenStreamService, month_list):
        height = self.kwargs.get('height', 770)
        rakuten_tv_table_data = [
            ["配信月", "販売数", "",],
        ]
        rakuten_tv_table_data = self.kwargs.get('rakuten_tv_table_data', rakuten_tv_table_data)

        rakuten_table_data_height = self.kwargs.get('rakuten_table_data_height', height - 310)
        rakuten_table_data_height = rakuten_table_data_height - 195
        rakuten_tv_sale_amount = [0,0,0]
        rakuten_tv_sale_price = [0,0,0]
        rakuten_tv_carryover_total = 0
        rakuten_tv_total = 0
        for rakuten in rakuten_tv:
            rakuten_tv_sale_amount[0] += rakuten.first_sale_amount
            rakuten_tv_sale_amount[1] += rakuten.second_sale_amount
            rakuten_tv_sale_amount[2] += rakuten.third_sale_amount

            rakuten_tv_sale_price[0] += rakuten.first_sale_price
            rakuten_tv_sale_price[1] += rakuten.second_sale_price
            rakuten_tv_sale_price[2] += rakuten.third_sale_price
            total_sale_price = rakuten.first_sale_price + rakuten.second_sale_price + rakuten.third_sale_price

            rakuten_tv_carryover_total += rakuten.carryover
            rakuten_tv_total += (rakuten.carryover + total_sale_price)

        for i, date_data in enumerate(month_list):
            rakuten_tv_table_data.append([f'{date_data.strftime("%m")}月', f"{rakuten_tv_sale_amount[i]}", f"{utils.format_currency_value(rakuten_tv_sale_price[i])}"])
            rakuten_table_data_height -= 10

        rakuten_tv_table_data.append(["前期繰越分（税抜き）", "", f"{utils.format_currency_value(rakuten_tv_carryover_total)}"])
        rakuten_tv_table_data.append(["合計", "", f"{utils.format_currency_value(rakuten_tv_total)}"])

        self.kwargs = {
            "height": height,
            "rakuten_tv_table_data": rakuten_tv_table_data,
            "rakuten_table_data_height": rakuten_table_data_height,
            "rakuten_tv_sale_amount": rakuten_tv_sale_amount,
            "rakuten_tv_sale_price": rakuten_tv_sale_price,
            "rakuten_tv_carryover_total": rakuten_tv_carryover_total,
            "rakuten_tv_total": rakuten_tv_total,
        }
        print(f"self.kwargs: {self.kwargs}")