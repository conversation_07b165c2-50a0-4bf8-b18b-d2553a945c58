import json
from django import forms
from django.db import transaction
from django.db import models
from django.db.models.functions import ExtractYear, ExtractMonth  # Import ExtractYear from django.db.models.functions
from django.db.models import Q, Sum, F
from dateutil.rrule import rrule, MONTHLY  # Import rrule and MONTHL<PERSON>
from datetime import timedelta  # Import timedelta from datetime
from django_q.tasks import async_task, async_chain  # Import async_task from django_q.tasks

from app.common import utils
from app.goods import usecase
from app.goods.models import CommitteeReport, CommitteeReportCategory, GachaInformation, ProductDetail, ReportArchiveStreaming, ReportRakutenStreamService, SaleGoodDetail, SaleGoodReport
from django.utils.translation import gettext as _

from app.master_data.models import MstSaleRoute


class ProductDetailForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        for field in self.Meta.required:
            self.fields[field].required = True

    class Meta:
        model = ProductDetail
        fields = '__all__'
        required = ['segment','product_id','product_name','artist','distribution_pattern_group','price','tax','holding_company','deduction_rate']

    def clean(self):
        cleaned_data = super().clean()
        if not self.data.get('logged_user').is_authenticated:
            raise forms.ValidationError(_('You are not logged in.'))

        if not self.instance.pk and cleaned_data.get('product_id') and ProductDetail.objects.filter(product_id=cleaned_data.get('product_id')).exists():
            raise forms.ValidationError({'product_id': [_("商品コードが既に登録されています!"),]})

        # if cleaned_data.get('product_category') and not utils.valid_enum(cleaned_data.get('product_category'), ProductDetail.PRODUCT_CATEGORY_SELECTION):
        #     raise forms.ValidationError({'product_category': [_("Invalid category!"),]})

        return cleaned_data

    def save(self, commit=True):
        model = super().save(commit=False)
        model.user = self.data.get('logged_user')
        with transaction.atomic():
            if commit:
                model.save()

        return model

class CommitteeReportForm(forms.ModelForm):
    yutaka_price_1 = forms.IntegerField(required=False)
    yutaka_price_2 = forms.IntegerField(required=False)
    yutaka_price_3 = forms.IntegerField(required=False)
    yutaka_price_4 = forms.IntegerField(required=False)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        for field in self.Meta.required:
            self.fields[field].required = True

    class Meta:
        model = CommitteeReport
        fields = ('category','artist','title','period_start','period_end', 'report_date', 'pay_date', 'payslip_number', 'royalty_use_fee', 'archive_description')
        required = ['category','artist','title','period_start','period_end',]

    def clean(self):
        cleaned_data = super().clean()
        if not self.data.get('logged_user').is_authenticated:
            raise forms.ValidationError(_('You are not logged in.'))

        is_admin = self.data.get('logged_user').groups_id == 1 or self.data.get('logged_user').groups_id == 2
        if not is_admin:
            raise forms.ValidationError(_('You are not allowed to operate this function!'))

        if cleaned_data.get('category') and not utils.valid_enum(cleaned_data.get('category'), CommitteeReportCategory.REPORT_CATEGORY):
            raise forms.ValidationError({'category': [_("Invalid category!"),]})

        #check if the period_start is greater than period_end
        if cleaned_data.get('period_start') > cleaned_data.get('period_end'):
            raise forms.ValidationError({'period_start': [_("Period start must be less than period end!"),]})

        #check period_start and period_end is in 3 months
        if cleaned_data.get('period_start') and cleaned_data.get('period_end'):
            if cleaned_data.get('period_end') - cleaned_data.get('period_start') > timedelta(days=90):
                raise forms.ValidationError({'period_start': [_("The period must be within 3 months!"),]})

        return cleaned_data

    def save(self, commit=True):
        model = super().save(commit=False)
        model.yutaka_price = json.dumps({
            'yutaka_price_1': self.cleaned_data.get('yutaka_price_1') or 0,
            'yutaka_price_2': self.cleaned_data.get('yutaka_price_2') or 0,
            'yutaka_price_3': self.cleaned_data.get('yutaka_price_3') or 0,
            'yutaka_price_4': self.cleaned_data.get('yutaka_price_4') or 0,
        })
        model.calculation_status = 'in_progress'

        if not model.payslip_number:
            payslip_number = utils.generate_random_string(10)
            while CommitteeReport.objects.filter(payslip_number=payslip_number).exists():
                payslip_number = utils.generate_random_string(10)

            model.payslip_number = payslip_number

        with transaction.atomic():
            if commit:
                model.save()

            # usecase.calculate_sale_good_report(model.id, self.cleaned_data.get('period_start'), self.cleaned_data.get('period_end'))

            async_task(usecase.calculate_sale_good_report, model.id, self.cleaned_data.get('period_start'), self.cleaned_data.get('period_end'))
            # async_chain([
            #     ('usecase.calculate_sale_good_report', (model.id, self.cleaned_data.get('period_start'), self.cleaned_data.get('period_end'))),
            #     ('usecase.generate_committee_report', (model.id,)),
            # ])

        return model

class ReportArchiveStreamingForm(forms.ModelForm):

    class Meta:
        model = ReportArchiveStreaming
        fields = '__all__'
        required = ['product_detail','committee_report','category','month','amount','sales','rate','total_payment',]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        for field in self.Meta.required:
            self.fields[field].required = True

    def clean(self):
        cleaned_data = super().clean()
        if not self.data.get('logged_user').is_authenticated:
            raise forms.ValidationError(_('You are not logged in.'))

        if cleaned_data.get('category') and not utils.valid_enum(cleaned_data.get('category'), ReportArchiveStreaming.ARCHIVE_CATEGORY):
            raise forms.ValidationError({'category': [_("Invalid category!"),]})

        if cleaned_data.get('rate') and (cleaned_data.get('rate') < 0 or cleaned_data.get('rate') > 100):
            raise forms.ValidationError({'rate': [_("Rate must be greater than or equal to 0 or more than 100!"),]})

        return cleaned_data

class ReportRakutenStreamServiceForm(forms.ModelForm):

    class Meta:
        model = ReportRakutenStreamService
        fields = '__all__'
        required = ['category',]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        for field in self.Meta.required:
            self.fields[field].required = True

class GachaInformationForm(forms.ModelForm):

    class Meta:
        model = GachaInformation
        fields = '__all__'
        required = []

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        for field in self.Meta.required:
            self.fields[field].required = True


