import graphene
from graphene_django import DjangoObjectType
from app.common.models import Guideline
from django.db.models import Float<PERSON>ield
from django.db.models.functions import Cast


class GuidelineNode(DjangoObjectType):
    pk = graphene.Int(source='pk')
    version_float = graphene.Float()

    class Meta:
        model = Guideline
        fields = '__all__'
        interfaces = (graphene.relay.Node, )

    def resolve_version_float(self, info):
        try:
            return float(self.version_float)
        except (ValueError, TypeError):
            return 1.0

    @classmethod
    def get_queryset(cls, queryset, info):
        return queryset.annotate(
            version_float=Cast('version', FloatField())
        ).order_by('version_float')