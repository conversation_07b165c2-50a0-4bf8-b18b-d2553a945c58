from graphene_django.forms.mutation import DjangoModelFormMutation
from graphql import GraphQLError
from django.db import transaction

class BulkDjangoModelFormMutation(DjangoModelFormMutation):
    class Meta:
        abstract = True

    @classmethod
    @transaction.atomic
    def mutate_and_get_payload(cls, root, info, **input):
        items = input.pop('items', [])
        if not isinstance(items, list):
            raise GraphQLError("Items must be a list")

        results = []
        with transaction.atomic():
            for item_data in items:
                # Get instance if ID is provided
                instance = None
                if 'id' in item_data:
                    instance = cls._meta.model.objects.get(pk=item_data['id'])

                # Create form with instance if it exists
                form_kwargs = cls.get_form_kwargs(root, info, **item_data)
                if instance:
                    form_kwargs['instance'] = instance

                form = cls.get_form(root, info, **form_kwargs)

                if form.is_valid():
                    result = cls.perform_mutate(form, info)
                    results.append(result)
                else:
                    raise GraphQLError(f"Validation failed: {form.errors}")

        return cls(results=results)