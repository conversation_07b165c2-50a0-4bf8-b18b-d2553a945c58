from datetime import datetime, timezone
import pathlib
from django.conf import settings
from django.http import JsonResponse
from django.shortcuts import render
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from django.views.decorators.csrf import csrf_exempt

from app.common import utils

# Create your views her
@csrf_exempt
def upload_image(request):
    # validate request
    if request.method == "POST":
        # file object
        file_obj = request.FILES["file"]

        # check extension
        file_name_suffix = pathlib.Path(file_obj.name).suffix

        if file_name_suffix not in [
            ".jpg",
            ".png",
            ".gif",
            ".jpeg",
            ".webp",
        ]:
            return JsonResponse({"message": "Wrong file format"})

        # create file name and path
        file_name = utils.generate_filename(file_obj)
        upload_time = datetime.now()
        path = f"{settings.UPLOAD_PATH}/{upload_time.year}/{upload_time.month}/{upload_time.day}"

        # full file path with name
        file_path = f"{path}/{file_name}"

        # check if file exists
        if default_storage.exists(file_path):
            file_url = default_storage.url(file_path)
            return JsonResponse(
                {"message": "File already exists", "location": file_url}
            )

        # save file using default storage
        file_path_saved = default_storage.save(file_path, ContentFile(file_obj.read()))
        file_url = default_storage.url(file_path_saved)

        # return success
        return JsonResponse(
            {"message": "Image uploaded successfully", "location": file_url}
        )

    # return generic request error
    return JsonResponse({"message": "Wrong request"})