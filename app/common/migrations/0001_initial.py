# Generated by Django 4.2.13 on 2025-01-14 10:14

from django.db import migrations, models
import tinymce.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Guideline',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_delete', models.BooleanField(default=False)),
                ('title', models.CharField(max_length=255)),
                ('content', tinymce.models.HTMLField()),
                ('version', models.CharField(max_length=255)),
            ],
            options={
                'verbose_name': 'Guideline',
                'verbose_name_plural': 'Guidelines',
                'ordering': ['-created_at'],
            },
        ),
    ]
