import calendar
from datetime import datetime, timedelta
from functools import reduce
from io import BytesIO
import locale
import math
import pathlib
import random
import re
import uuid
from django import forms
from django.db import models
from django.forms import ValidationError
from django.http import HttpResponse
import graphene
from django.utils.translation import gettext as _
from django.template.loader import get_template
from xhtml2pdf import pisa
import base64
from graphql_jwt.utils import get_credentials, get_payload, get_user_by_payload
from graphql_jwt.exceptions import JSONWebTokenError
from django.forms import model_to_dict
import traceback
from babel.numbers import format_currency
import yaml


class DeletedManager(models.Manager):
    """
    Object manager used to filter queries that are not deleted.
    """

    def get_queryset(self):
        """
        Returns the queryset with the filter applied to exclude deleted objects.
        """
        return super(DeletedManager, self).get_queryset().filter(is_delete=False)

    def get_total_data(self):
        """
        Returns the total count of non-deleted objects in the queryset.
        """
        return super(Deleted<PERSON>ana<PERSON>, self).get_queryset().count()

class TimeStampedModel(models.Model):
    """
    A base model class that provides timestamp fields for created and updated times.

    Attributes:
        created_at (DateTimeField): The timestamp for when the model instance was created.
        updated_at (DateTimeField): The timestamp for when the model instance was last updated.
        is_delete (BooleanField): A flag indicating whether the model instance is deleted or not.

    Managers:
        objects (Manager): The default manager for the model.

    Meta:
        abstract (bool): Specifies that this model is an abstract base class and cannot be instantiated directly.
    """

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_delete = models.BooleanField(default=False)

    objects = models.Manager()

    class Meta:
        abstract = True

class Responser(graphene.ObjectType):
    """
    A class used for general response in GraphQL mutation.
    """

    result = graphene.String()

    def resolve_result(self, info, **kwargs):
        """
        Resolves the 'result' field of the response.

        Args:
            info: The GraphQL ResolveInfo object.
            kwargs: Additional keyword arguments.

        Returns:
            The value of the 'result' field.
        """
        return self.result

class CustomPasswordValidator():
    """
    CustomPasswordValidator class is used to validate passwords based on certain criteria.

    Attributes:
        min_length (int): The minimum length required for a valid password.

    Methods:
        validate(password, user=None): Validates the given password based on the specified criteria.
        get_help_text(): Returns the help text for the password validator.
    """

    def __init__(self, min_length=1):
        self.min_length = min_length

    def validate(self, password, user=None):
        """
        Validates the given password based on the specified criteria.

        Args:
            password (str): The password to be validated.
            user (User, optional): The user object associated with the password (default: None).

        Raises:
            ValidationError: If the password does not meet the specified criteria.
        """
        special_characters = "[~\!@#\$%\^&\*\(\)_\+{}\":;'\[\]]"
        if not any(char.isupper() for char in password):
            raise ValidationError(_('Password must contain uppercase at least %(min_length)d digit.') % {'min_length': self.min_length})
        if not any(char.isdigit() for char in password):
            raise ValidationError(_('Password must contain at least %(min_length)d digit.') % {'min_length': self.min_length})
        if not any(char.isalpha() for char in password):
            raise ValidationError(_('Password must contain at least %(min_length)d letter.') % {'min_length': self.min_length})
        if not any(char in special_characters for char in password):
            raise ValidationError(_('Password must contain at least %(min_length)d special character.') % {'min_length': self.min_length})

    def get_help_text(self):
        """
        Returns the help text for the password validator.

        Returns:
            str: The help text for the password validator.
        """
        return ""

def generate_random_string(length=6):
    """
    Generate a random string with a specified length.

    Parameters:
        length (int): The length of the random string to generate. Default is 6.

    Returns:
        str: A random string of the specified length.

    """
    import random
    import string
    return ''.join(random.choices(string.ascii_uppercase + string.digits, k=length))

def check_key_in_objects(key, objects):
    """
    Check if a given key exists in a list of objects.

    Args:
        key (str): The key to check for existence.
        objects (list): The list of objects to search for the key.

    Returns:
        bool: True if the key exists in all objects, False otherwise.
    """
    for obj in objects:
        if key not in obj:
            return False
    return True

#check if value exists in object list
def check_value_in_objects(value, objects):
    """
    Check if a given value exists in a list of objects.

    Args:
        value (str): The value to check for existence.
        objects (list): The list of objects to search for the value.

    Returns:
        bool: True if the value exists in any of the objects, False otherwise.
    """
    for obj in objects:
        if value in obj.values():
            return True
    return False

def to_int(value: str):
    """
    Converts a string value to an integer.

    Args:
        value (str): The string value to be converted.

    Returns:
        int: The converted integer value. If the conversion fails, returns 0.
    """
    try:
        return int(value)
    except Exception:
        return 0

def to_float(value: str):
    """
    Converts a string value to a float.

    Args:
        value (str): The string value to be converted.

    Returns:
        float: The converted float value. If the conversion fails, returns 0.0.
    """
    try:
        return float(value)
    except Exception:
        return 0.0

def to_str(value):
    """
    Converts the given value into a string.

    Args:
        value: The value to be converted.

    Returns:
        str: The string representation of the value.

    """
    try:
        return str(value)
    except Exception:
        return ""

class CommaSeparatedField(forms.CharField):
    """
    A custom form field that converts comma-separated input into a list.

    This field is used to convert input from a form field that accepts comma-separated values
    into a Python list. It overrides the `to_python` and `prepare_value` methods of the
    `forms.CharField` class to perform the conversion.

    Example usage:
    ```
    class MyForm(forms.Form):
        my_field = CommaSeparatedField()
    ```

    Attributes:
        strip (bool): If True, leading and trailing whitespace will be stripped from each value
            in the resulting list. Default is False.
    """

    def to_python(self, value):
        """
        Converts the comma-separated input value into a Python list.

        Args:
            value (str): The input value to be converted.

        Returns:
            list: The converted list of values.

        Raises:
            None.

        """
        if value in self.empty_values:
            return self.empty_value
        value = str(value).split(',')
        if self.strip:
            value = [s.strip() for s in value]
        return value

    def prepare_value(self, value):
        """
        Converts the Python list value into a comma-separated string.

        Args:
            value (list): The Python list value to be converted.

        Returns:
            str: The converted comma-separated string.

        Raises:
            None.

        """
        if value is None:
            return None
        return ', '.join([str(s) for s in value])

def is_valid_date(date_string, format='%Y-%m-%d'):
    """
    Check if a given date string is valid according to the specified format.

    Args:
        date_string (str): The date string to be validated.
        format (str, optional): The format of the date string. Defaults to '%Y-%m-%d'.

    Returns:
        bool: True if the date string is valid, False otherwise.
    """
    try:
        datetime.strptime(date_string, format)
        return True
    except Exception as e:
        import traceback; traceback.print_exc();
        return False

import re

def is_number(s):
    """
    Checks if a given string represents a number.

    Args:
        s (str): The string to be checked.

    Returns:
        bool: True if the string represents a number, False otherwise.
    """
    numeric_pattern = r'^[+-]?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$'
    try:
        return bool(re.match(numeric_pattern, s))
    except Exception:
        import traceback; traceback.print_exc();
        return False

def render_to_document(template_src, context_dict={}, plain_text=True):
    """
    Renders a template to a document (PDF or plain text) using the provided context.

    Args:
        template_src (str): The path to the template source file.
        context_dict (dict, optional): The context dictionary to be used when rendering the template. Defaults to an empty dictionary.
        plain_text (bool, optional): Specifies whether to return the result as plain text or PDF. Defaults to True.

    Returns:
        str or HttpResponse: If `plain_text` is True, returns the result as a base64 encoded string. If `plain_text` is False, returns an `HttpResponse` object with the PDF content.

    Raises:
        None

    Examples:
        # Render a template to a PDF document
        pdf_doc = render_to_document('path/to/template.html', {'name': 'John Doe'}, plain_text=False)

        # Render a template to a plain text document
        text_doc = render_to_document('path/to/template.txt', {'name': 'John Doe'}, plain_text=True)
    """

    template = get_template(template_src)
    html = template.render(context_dict)
    result = BytesIO()
    pdf = pisa.pisaDocument(BytesIO(html.encode("UTF-8")), result, encoding='UTF-8')
    if pdf.err:
        return None

    if plain_text:
        return base64.b64encode(result.getvalue()).decode('utf-8')
    return HttpResponse(result.getvalue(), content_type='application/pdf')

def has_duplicate_elements(input_list):
    """
    Checks if a list contains any duplicate elements.

    Args:
        input_list (list): The list to check for duplicate elements.

    Returns:
        bool: True if the list contains duplicate elements, False otherwise.
    """
    seen = set()
    for item in input_list:
        if item in seen:
            return True
        seen.add(item)
    return False

def get_auth_user(token):
    """
    Retrieves the user model from the access token.

    Args:
        token (str): The access token.

    Returns:
        User: The user model if the token is valid, otherwise None.
    """
    try:
        payload = get_payload(token)
        return get_user_by_payload(payload)
    except JSONWebTokenError as e:
        return None
    except Exception as e:
        print(e)
        return None

def roundup(value):
    """
    Rounds a given value always up.

    Args:
        value (float): The value to be rounded.

    Returns:
        int: The rounded integer value.
    """
    return math.ceil(float(value))

def roundoff(value):
    """
    Rounds a given value to the nearest integer using custom rounding logic.

    Args:
        value (float): The value to be rounded.

    Returns:
        int: The rounded integer value.

    Example:
        >>> roundup(3.2)
        4
        >>> roundup(4.8)
        5
    """
    integer_part = math.floor(float(value))
    decimal_part = float(value) - integer_part

    if decimal_part < 0.5:
        return integer_part
    else:
        return integer_part + 1

def roundup_int(value, base=100):
    """Round up to the nearest hundred"""
    return math.ceil(value / base) * base

def rounddown(value):
    """
    remove decimal part of a float number

    Args:
        value (float): The value to be rounded.

    Returns:
        int: The rounded integer value.

    Example:
        >>> rounddown(3.2)
        3
        >>> rounddown(4.8)
        4
    """
    return math.floor(float(value))

class CaseInsensitiveDict(dict):
    def __init__(self, data=None, **kwargs):
        super().__init__()
        if data is None:
            data = {}
        for key, value in {**data, **kwargs}.items():
            self[key.lower()] = value

    def __getitem__(self, key):
        return super().__getitem__(key.lower())

    def __setitem__(self, key, value):
        super().__setitem__(key.lower(), value)

    def __contains__(self, key):
        return super().__contains__(key.lower())

    def get(self, key, default=None):
        return super().get(key.lower(), default)

    def pop(self, key, default=None):
        return super().pop(key.lower(), default)

def valid_enum(item, enums):
    enum_dict = {key: value for key, value in enums}
    return enum_dict.get(item) is not None

def generate_epoch_time():
    """
    Generate the current epoch time.

    Returns:
        int: The current epoch time.
    """
    import time
    return int(time.time())

def conv_model_to_dict(model):
    try:
        return model_to_dict(instance=model, fields=[field.name for field in model._meta.fields])
    except Exception as ex:
        traceback.print_stack()
        return model

def convert_to_date(source: str, format: str = '%Y-%m-%d'):
    try:
        return datetime.strptime(source, format)
    except ValueError as ve1:
        return None

def convert_to_str(source: datetime, format: str = '%Y-%m-%d'):
    try:
        return source.strftime(format)
    except ValueError as ve1:
        return None

def convert_to_epochs(source: datetime):
    try:
        return source.strftime('%s')
    except ValueError as ve1:
        return None

def convert_date_format(source: str, source_format: str = '%Y-%m-%d', to_format: str = '%Y-%m-%d'):
    try:
        return datetime.strptime(source, source_format).strftime(to_format)
    except ValueError as ve1:
        return None

def concat_date_time(date: datetime, time):
    try:
        return datetime.combine(date, time)
    except ValueError as ve1:
        return None

def compare_datetime(datetime1: datetime, datetime2: datetime):
    try:
        difference = datetime2 - datetime1
    except ValueError as ve1:
        return None

def format_currency_value(value: int, currency: str = 'JPY', locale: str = 'ja_JP'):
    try:
        return format_currency(to_float(value), currency, locale=locale)
    except ValueError as ve1:
        return 0

def format_amount(value: int):
    try:
        return "{:,.0f}".format(value)
    except ValueError as ve1:
        return 0

def get_last_date(value, format = '%Y年%m月%d日'):
    try:
        date_str = value
        if isinstance(value, str):
            date_str = convert_to_date(value)
        year, month = date_str.year, date_str.month
        _, last_day = calendar.monthrange(year, month)
        end_date = datetime(year, month, last_day)
        return end_date.date().strftime(format)
    except ValueError as ve1:
        return None

def has_index(lst, index):
    return 0 <= index < len(lst)

def format_percentage(value: float):
    try:
        return "{:.2f}".format(value) + "%"
    except ValueError as ve1:
        return None

def trim_text(value, length=10):
    """
    Trims the text to the specified length and adds an ellipsis if the text is longer.
    """
    if len(value) > length:
        return value[:length] + ' [...]'
    return value

def abbreviate(stng):
    words = re.findall(r'\b\w', stng)
    acronym = reduce(lambda x, y: x + y, [word[0].upper() for word in words])
    return acronym

def load_yaml(file_path):
    with open(file_path, 'r') as file:
        return yaml.safe_load(file)

def generate_filename(file_obj):
    file_name_suffix = pathlib.Path(file_obj.name).suffix
    return str(uuid.uuid4()) + file_name_suffix.lower()

def append_error(ex: Exception):
    return (
                f"Error: {str(ex)}\n"
                f"Traceback:\n"
                f"{''.join(traceback.format_tb(ex.__traceback__))}"
            )