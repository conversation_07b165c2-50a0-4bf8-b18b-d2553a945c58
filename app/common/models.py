from django.db import models
from tinymce.models import HTMLField
from django.utils.translation import gettext as _

from app.common.utils import TimeStampedModel

# Create your models here.
class Guideline(TimeStampedModel):
    title = models.CharField(max_length=255)
    content = HTMLField()
    version = models.CharField(max_length=255)

    def __str__(self):
        return self.title

    class Meta:
        verbose_name = _('Guideline')
        verbose_name_plural = _('Guidelines')
        ordering = ['-created_at']