from django.http import JsonResponse
from promise import is_thenable
from functools import partial
import logging
import sys
import json
import os

from app.common.utils import get_auth_user
logging.basicConfig(stream=sys.stdout, level=logging.DEBUG)

class JWTGrapheneAuthMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Code to be executed for each request before
        # the view (and later middleware) are called.

        response = self.get_response(request)

        # Code to be executed for each request/response after
        # the view is called.

        return response

    def process_view(self, request, view_func, view_args, view_kwargs):
        # Apply middleware logic only for a specific URL
        if os.getenv('IS_DEV', False):
            return None
        whitelisted_paths = [
            '/payslip_statement/',
            '/export-sale-data/',
            '/project/export-data/',
            '/project/register-project/',
            '/project/register-product/',
            '/master-data/register-dist-data/',
            '/master-data/register-recipient/'
        ]
        if request.path in whitelisted_paths:
            token_value = request.headers.get('Authorization', None)
            if not token_value:
                token_value = request.GET.get('accessToken', None)

            if not token_value:
                return JsonResponse({'error': 'Unauthorized'}, status=401)

            try:
                user = get_auth_user(token_value)
                if user is None:
                    return JsonResponse({"error": "Login required"}, status=401)
            except Exception as e:
                return JsonResponse({"error": "Login required"}, status=401)

        # For other paths, just return None to continue processing other middlewares
        return None

def log_request_body(info):
    try:
        body = info.context._body.decode('utf-8')
        json_body = json.loads(body)
        logging.error(' User: %s \n Action: %s \n Variables: %s \n Body: %s',
                      info.context.user,
                      json_body['operationName'],
                      json_body['variables'],
                      json_body['query'])
    except:
        logging.error(body)

class DebugMiddleware(object):
    def on_error(self, error ,info):
        log_request_body(info)

    def resolve(self, next, root, info, **args):

        result = next(root, info, **args)
        if is_thenable(result):
            result.catch(partial(self.on_error, info=info))
        return result