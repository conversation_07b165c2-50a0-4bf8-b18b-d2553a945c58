import graphene
from graphene_django import DjangoConnectionField
from graphql_jwt.decorators import login_required

from app.common.graphql.types import GuidelineNode
from app.common.models import Guideline


class Query(graphene.ObjectType):
    all_guideline = DjangoConnectionField(GuidelineNode)
    guideline_detail = graphene.Field(GuidelineNode, id=graphene.Int(required=True))

    @login_required
    def resolve_all_guideline(self, info, **kwargs):
        return Guideline.objects.all()

    @login_required
    def resolve_guideline_detail(self, info, **kwargs):
        return Guideline.objects.get(pk=kwargs.get('id'))
