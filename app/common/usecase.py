import logging
from django.core.files.base import ContentFile
from django.core.mail import EmailMessage, send_mail
from django.conf import settings

from app.user.models import User
logger = logging.getLogger(__name__)

def send_email_with_attachment(to: list, subject: str, message: str, attachment: ContentFile, content_type: str):
    email_to = to
    if settings.DEBUG:
        email_to += settings.DEFAULT_EMAIL_RECEIVER
    email = EmailMessage(subject, message, settings.EMAIL_HOST_USER, email_to)
    email.attach(attachment.name, attachment.read(), content_type)
    send_status = email.send()
    if send_status:
        logger.info(f"Email sent to {','.join(email_to)} with attachment")
    else:
        logger.error(f"Email not sent to {','.join(email_to)} with attachment")

    return send_status

def send_email_to_admin(subject: str, message: str):
    user_email = User.objects.filter(groups__name__in=['super_admin', 'admin']).values_list('email', flat=True)
    send_status = send_mail(subject, message, settings.EMAIL_HOST_USER, user_email)
    if send_status:
        logger.info(f"Email sent to {','.join(user_email)} with attachment")
    else:
        logger.error(f"Email not sent to {','.join(user_email)} with attachment")

    return send_status

def send_email_to_user(to: list, subject: str, message: str):
    user_email = to
    if settings.DEBUG:
        user_email += settings.DEFAULT_EMAIL_RECEIVER
    send_status = send_mail(subject, message, settings.EMAIL_HOST_USER, user_email)
    if send_status:
        logger.info(f"Email sent to {','.join(user_email)} with attachment")
    else:
        logger.error(f"Email not sent to {','.join(user_email)} with attachment")

    return send_status