import re
from django.contrib import admin
from django import forms
from django.forms import ValidationError

from app.common.models import Guideline
from django.utils.translation import gettext as _

# Register your models here.
class GuidelineForm(forms.ModelForm):
    class Meta:
        model = Guideline
        fields = '__all__'

    def clean_version(self):
        version = self.cleaned_data['version']
        pattern = r'^\d+\.\d+$'
        if not re.match(pattern, str(version)):
            raise ValidationError(_('Version must be in format: number.number (e.g. 1.0, 2.1)'))
        return version

@admin.register(Guideline)
class GuidelineAdmin(admin.ModelAdmin):
    form = GuidelineForm
    list_display = ('version', 'title',)
    list_per_page = 10
    filter_horizontal = ()
    exclude = ('is_delete',)

    def get_fields(self, request, obj=None):
        fields = super().get_fields(request, obj)
        if 'is_delete' in fields:
            fields.remove('is_delete')
        return fields