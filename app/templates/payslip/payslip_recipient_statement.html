{% load template_filter %}

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
    <head>
        <title>{% if pdf_title %}{{ pdf_title }}{% else %}PDF{% endif %}</title>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta charset="UTF-8">
        <link href='https://fonts.googleapis.com/css?family=Roboto' rel='stylesheet'>
        <style type="text/css">
            body {
                {% comment %} font-family: Verdana, Arial, Helvetica, sans-serif; {% endcomment %}
                font-family: "HeiseiMin-W3", sans-serif;
                font-weight: 400;
                font-size: 10px;
            }
            @page { size: A4; margin-left: 1cm; margin-right: 1cm; margin-top: 1cm; margin-bottom: 0.5cm; }
            {% comment %}
            .header {
                font-size: 20px;
                font-weight: 100;
                text-align: center;
                color: #007cae;
            }{% endcomment %}
            .title {
                font-size: 22px;
                font-weight: 100;
                /* text-align: right;*/
                padding: 10px 20px 0px 20px;
            }
            .title span {
                color: #007cae;
            }
            .payslip-title {
                padding: 10px 20px 0px 0px;
                text-align: left !important;
                /*margin-left: 40%;*/
            }
            .payment-info {
                padding: 10px 0px 0px 20px;
                text-align: right !important;
                /*margin-left: 40%;*/
            }
            .details {
                padding: 0px 20px 0px 0px;
                text-align: left !important;
                /*margin-left: 40%;*/
            }
            .hrItem {
                border: none;
                height: 1px;
                /* Set the hr color */
                color: #333; /* old IE */
                background-color: #fff; /* Modern Browsers */
            }
            table {
                border-collapse: collapse;
            }
            th, td
            {
                height: 25px;
                {% comment %} border-bottom-style: solid #9CA3AF;
                border-bottom-width: thin;
                border-left-style: solid #9CA3AF;
                border-left-width: thin;
                border-right-style: solid #9CA3AF;
                border-right-width: thin;
                border-top-style: solid #9CA3AF;
                border-top-width: thin; {% endcomment %}
            }

            th.col-first, td.col-first
            {
                width: 25%;
            }

            th.col-second, td.col-second
            {
                width: 40%;
            }

            th.col-third, td.col-third
            {
                width: 15%;
            }

            th.col-fourth, td.col-fourth
            {
                width: 20%;
            }

            #payslipTable th, td
            {
                border: 1px solid #9CA3AF;
                line-height: auto;
                text-align: left !important;
                vertical-align: middle;
                -pdf-word-wrap: CJK;
                margin: 15px 5px 15px 5px;
            }

            #additionalPayslipTable th, td
            {
                border: 1px solid #9CA3AF;
                line-height: auto;
                text-align: left !important;
                vertical-align: middle;
                -pdf-word-wrap: CJK;
                margin: 15px 5px 15px 5px;
            }
        </style>
    </head>
    <body>
        <div class='payslip-title'>
            お支払明細書<hr/>
        </div>
        <!--<div class='wrapper'>-->
        <!--    <div class='header'>-->
        <!--        <p class='title'>納品確認書</p>-->
        <!--    </div>-->
        <!--</div>-->
        <div class='details'>
            {{ billing_name }}<br/>
            《{{ billing_title }}：{{ registration_number }}》 <br/>
            <br/>
            <br/>
            対象期間：{{ payslip_period }}入金分 <br/>
        </div>
        <table id="payslipTable">
            <tbody>
                {% if payslip_live_per_segment %}

                {% for segment_key, segment_data in payslip_live_per_segment.items %}
                <tr>
                    {% if segment_data.segment.segment_host > 1 %}
                    <td class="col-first"><p>《{{ segment_data.segment.segment_host|default:"" }}人》</p></td>
                    {% else %}
                    <td class="col-first"><p>《個人》</p></td>
                    {% endif %}
                    <td class="col-second"><p>{{ segment_data.segment_type|default:"" }}</p></td>
                    <td class="col-third"><p>金額（税別）</p></td>
                    <td class="col-fourth"><p>備考</p></td>
                </tr>
                    {% for project_data in segment_data.project_detail %}
                <tr>
                    <td class="col-first"><p>{{ payslip_period|default:"" }}</p></td>
                    <td class="col-second"><p style="font-size: 8px;">{{ project_data.project.project_name|default:""|linebreaksbr }}</p></td>
                    <td class="col-third"><p>{{ project_data.reward.amount|default:"0"|roundup|format_currency }}</p></td>
                    <td class="col-fourth"><p>{{ project_data.reward.note|default:""|linebreaksbr }}</p></td>
                </tr>
                    {% endfor %}
                {% endfor %}
                {% endif %}


                {% if payslip_other_data or payslip_sale_data %}
                <tr>
                    <td class="col-first"></td>
                    <td class="col-second"><p>その他の活動</p></td>
                    <td class="col-third"><p>金額（税別）</p></td>
                    <td class="col-fourth"><p>備考</p></td>
                </tr>
                {% endif %}

                {% for payslip_data in payslip_other_data %}
                <tr>
                    <td class="col-first"><p>{{ payslip_period|default:"" }}</p></td>
                    <td class="col-second"><p style="font-size: 6px;">{{ payslip_data.project.project_name|default:""|linebreaksbr }}</p></td>
                    <td class="col-third"><p>{{ payslip_data.reward.amount|default:"0"|roundup|format_currency }}</p></td>
                    <td class="col-fourth"><p>{{ payslip_data.reward.note|default:""|linebreaksbr }}</p></td>
                </tr>
                {% endfor %}
                {% for payslip_data in payslip_sale_data %}
                <tr>
                    <td class="col-first"><p>{{ payslip_period|default:"" }}</p></td>
                    <td class="col-second"><p>{{ payslip_data.sale_data.product_detail.segment.segment_name|default:""|linebreaksbr }}</p></td>
                    <td class="col-third"><p>{{ payslip_data.reward.amount|default:"0"|roundup|format_currency }}</p></td>
                    <td class="col-fourth"><p>{{ payslip_data.reward.note|default:""|linebreaksbr }}</p></td>
                </tr>
                {% endfor %}
                {% for payslip_data in payslip_carry_over_data %}
                <tr>
                    <td class="col-first"><p>{{ payslip_period|default:"" }}</p></td>
                    <td class="col-second"><p>{{ payslip_data.reward.billing_title|default:""|linebreaksbr }}</p></td>
                    <td class="col-third"><p>{{ payslip_data.reward.amount|default:"0"|roundup|format_currency }}</p></td>
                    <td class="col-fourth"><p>{{ payslip_data.reward.note|default:""|linebreaksbr }}</p></td>
                </tr>
                {% endfor %}
                <tr style="border-top: 2.5px solid #9CA3AF;">
                    <td class="col-first"><p>合計　10％対象</p></td>
                    <td class="col-second"><p>(税別）</p></td>
                    <td class="col-third"><p>{{ payslip_total_reward|default:"0"|roundup|format_currency }}</p></td>
                    <td class="col-fourth"></td>
                </tr>
                <tr>
                    <td class="col-first">
                        <p style="margin:0;padding: 0;">【控除項目】</p> <br/>
                        <p style="margin:0;padding: 0;">所得税及び復興等別所得税</p>
                    </td>
                    <td class="col-second"></td>
                    <td class="col-third"><p>{{ reward_tax|default:"0"|roundup|format_currency }}</p></td>
                    <td class="col-fourth"></td>
                </tr>
                <tr>
                    <td class="col-first"><p>消費税</p></td>
                    <td class="col-second"><p>10%</p></td>
                    <td class="col-third"><p>{{ consumption_tax|default:"0"|roundup|format_currency }}</p></td>
                    <td class="col-fourth"></td>
                </tr>
                <tr>
                    <td class="col-first"><p>お支払額</p></td>
                    <td class="col-second"></td>
                    <td class="col-third"><p>{{ total_reward|default:"0"|roundup|format_currency }}</p></td>
                    <td class="col-fourth"></td>
                </tr>
            </tbody>

        </table>
        <br/>
        <table id="additionalPayslipTable">
            <tbody>
                <tr>
                    <td class="col-first"><p>加算項目</p></td>
                    <td class="col-second"></td>
                    <td class="col-third"><p>{{ total_positive_amount_acquired|default:"0"|roundup|format_currency }}</p></td>
                    <td class="col-fourth"></td>
                </tr>
                <tr>
                    <td class="col-first"><p>控除項目（金額マイナス表示）</p></td>
                    <td class="col-second"><p>内容＆請求書番号・アドバンス相殺　など</p></td>
                    <td class="col-third"><p>{{ total_negative_amount_acquired|default:"0"|roundup|format_currency }}</p></td>
                    <td class="col-fourth"></td>
                </tr>
            </tbody>
        </table>
        <br/>
        <table id="totalPayslipTable">
            <tbody>
                <tr>
                    <td style="width: 50%;"><p>お振込金額</p></td>
                    <td style="width: 50%;"><p>{{ total_payslip_amount|default:"0"|roundup|format_currency }}</p></td>
                </tr>
            </tbody>
        </table>
        <br>
        <div class='payment-info'>
            <p>支払方法：{{ pay_method|default:"" }}<br/>
            振込日：{{ paid_date|date:"Y/m/d"|default:"" }}</p>
        </div>
        <hr>
        <div class='payment-info'>
            <p>株式会社ユークリッド・エージェンシー</p>
        </div>
    </body>
</html>
