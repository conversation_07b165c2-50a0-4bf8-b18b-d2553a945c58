#!/bin/bash

echo "check logging directory..."
if [ ! -d /var/log/dcs-app ]; then
  sudo mkdir -p /var/log/dcs-app
  sudo touch /var/log/dcs-app/gunicorn-access.log
#   sudo chown $USER:$USER /var/log/dcs-app/gunicorn-access.log
  sudo touch /var/log/dcs-app/gunicorn-error.log
#   sudo chown $USER:$USER /var/log/dcs-app/gunicorn-error.log
  sudo chmod -R 777 /var/log/dcs-app/
fi

echo "check virtualenv directory..."
if [ ! -d ~/dcs-app/venv ]; then
  ~/dcs-app/venv/bin/python manage.py migrate --settings=config.settings.local
  sudo ~/dcs-app/venv/bin/python manage.py collectstatic --settings=config.settings.local --noinput
fi

echo "check gunicorn process..."
if pgrep -x "gunicorn" > /dev/null
then
  echo "restart gunicorn..."
  sudo pkill gunicorn
	DJANGO_SETTINGS_MODULE=config.settings.local ~/dcs-app/venv/bin/gunicorn --config gunicorn_cfg.py config.wsgi --daemon --reload
  echo "restart gunicorn finish..."
else
  echo "start gunicorn..."
  DJANGO_SETTINGS_MODULE=config.settings.local ~/dcs-app/venv/bin/gunicorn --config gunicorn_cfg.py config.wsgi --daemon --reload
  echo "start gunicorn finish..."
fi


echo "deployment finish..."