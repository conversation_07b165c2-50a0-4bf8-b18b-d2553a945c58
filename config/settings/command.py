from .base import *

GRAPHQL_EXPLORE = True
SECRET_KEY = env("SECRET_KEY", default='bakery-app-django-insecure-4x=3#9f_566cm9y0v@*^!-sf#!mt@4ya53$qga)beot!-*c_xp')
ALLOWED_HOSTS = ["*"]
DEBUG = True
MEDIA_ROOT = "/var/www/dcs_app/media"
STATIC_ROOT = "/var/www/dcs_app/static"

LOGGING = {
    "version": 1,
    "filters": {
        "require_debug_true": {
            "()": "django.utils.log.RequireDebugTrue",
        }
    },
	"formatters": {
        "production": {
            "format": "%(asctime)s [%(levelname)s] %(process)d %(thread)d "
            "%(pathname)s:%(lineno)d %(message)s"
        },
        "simple_app": {
            "format": "=========================================================== \
            \n[{levelname}] {pathname}:{lineno} \n[MESSAGE] {message}\n",
            "style": "{",
        },
        "simple_django": {
            "format": "=========================================================== \
            \n[{levelname}] {pathname}:{lineno} \n[MESSAGE] {message}\n",
            "style": "{",
        },
    },
    "handlers": {
        "console": {
            "level": "DEBUG",
            "filters": ["require_debug_true"],
            "class": "logging.StreamHandler",
			"formatter": "simple_django",
        }
    },
    "loggers": {
        "apps": {
            "handlers": ["console"],
            "level": "ERROR",
            "propagate": False,
        },
        "django": {
            "handlers": ["console"],
            "level": "INFO",
            "propagate": False,
        },
		"graphene_django": {
            "handlers": ["console"],
            "level": "DEBUG",
        }
    },
}

# DEBUG_PROPAGATE_EXCEPTIONS = True