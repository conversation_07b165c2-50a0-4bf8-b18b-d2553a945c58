from .base import *

GRAPHQL_EXPLORE = False
ALLOWED_HOSTS = ["*"]
DEBUG = False
MEDIA_ROOT = "/var/www/dcs_app/media"
STATIC_ROOT = "/var/www/dcs_app/static"
DEFAULT_EMAIL_RECEIVER = ['<EMAIL>','<EMAIL>']

EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = "sera-global.sakura.ne.jp"
EMAIL_PORT = 587
EMAIL_HOST_USER = env("EMAIL_HOST_USER")
EMAIL_HOST_PASSWORD = env("EMAIL_HOST_PASSWORD")
EMAIL_USE_TLS = True
DEFAULT_FROM_EMAIL = env("EMAIL_HOST_USER")

LOGGING = {
    "version": 1,
    "filters": {
        "require_debug_true": {
            "()": "django.utils.log.RequireDebugTrue",
        }
    },
	"formatters": {
        "production": {
            "format": "%(asctime)s================================================= \
            \n [%(levelname)s] %(process)d %(thread)d "
            "%(pathname)s:%(lineno)d \n[MESSAGE]%(message)s\n%(exc_text)s",
            "exc_info": True,
        },
        "simple_app": {
            "format": "=========================================================== \
            \n[{levelname}] {pathname}:{lineno} \n[MESSAGE] {message}\n",
            "style": "{",
        },
        "simple_django": {
            "format": "=========================================================== \
            \n[{levelname}] {pathname}:{lineno} \n[MESSAGE] {message}\n",
            "style": "{",
        },
    },
    "handlers": {
		'file': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': "./log/app.log",
            'formatter': 'production',
			'maxBytes': 15728640,  # 1024 * 1024 * 15B = 15MB
            'backupCount': 5,
        },
        "console": {
            "level": "DEBUG",
            "filters": ["require_debug_true"],
            "class": "logging.StreamHandler",
			"formatter": "simple_django",
        }
    },
    "loggers": {
        "app.master_data": {
            "handlers": ["console", "file"],
            "level": "INFO",
            "propagate": False,
        },
		"app.project": {
            "handlers": ["console", "file"],
            "level": "DEBUG",
            "propagate": False,
        },
		"app.user": {
            "handlers": ["console", "file"],
            "level": "INFO",
            "propagate": False,
        },
        "app.goods": {
            "handlers": ["console", "file"],
            "level": "DEBUG",
            "propagate": False,
        },
        "app.common": {
            "handlers": ["console", "file"],
            "level": "DEBUG",
            "propagate": False,
        },
        "app.rewards": {
            "handlers": ["console", "file"],
            "level": "DEBUG",
            "propagate": False,
        },
        "django": {
            "handlers": ["console", "file"],
            "level": "INFO",
            "propagate": False,
        },
        "graphene_django": {
            "handlers": ["console", "file"],
            "level": "DEBUG",
        },
        'django.db.backends': {
            'handlers': ["console", "file"],
            'level': 'ERROR',
            'propagate': False,
        },
    },
}
