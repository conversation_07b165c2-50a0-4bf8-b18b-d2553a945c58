"""
Django settings for config project.

Generated by 'django-admin startproject' using Django 4.2.13.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

from datetime import timedelta
import os
from pathlib import Path
import environ
env = environ.Env()
environ.Env.read_env(env_file=".env")

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-5z^@z(w9*453p$=n%@3p&gg6@c1l(x38=dn0cap@$x=jvlh-kr'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ["*"]


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
	"graphene_django",
	"corsheaders",
	'django_q',
	"app.common",
	"app.master_data",
	"app.user",
	"app.project",
	"app.goods",
	"app.rewards",
	"tinymce",
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.locale.LocaleMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    # 'app.common.middlewares.DebugMiddleware',
    # 'app.common.middlewares.JWTGrapheneAuthMiddleware',
]

if not DEBUG:
    MIDDLEWARE += ['app.common.middlewares.JWTGrapheneAuthMiddleware',]

GRAPHQL_JWT = {
    "JWT_VERIFY_EXPIRATION": True,
    "JWT_EXPIRATION_DELTA": timedelta(minutes=60),
}

GRAPHENE = {
   'SCHEMA': 'config.schema.schema',
   "MIDDLEWARE": [
        "graphql_jwt.middleware.JSONWebTokenMiddleware",
    ],
    "RELAY_CONNECTION_MAX_LIMIT": 2000,
	"ATOMIC_MUTATIONS": True,
}

GRAPHQL_JWT = {
    "JWT_ALLOW_ARGUMENT": True,
}

AUTHENTICATION_BACKENDS = {
    "graphql_jwt.backends.JSONWebTokenBackend",
    "django.contrib.auth.backends.ModelBackend",
}

ROOT_URLCONF = 'config.urls'

TEMPLATE_DIR = os.path.join(BASE_DIR.parent, "app/templates")
TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [TEMPLATE_DIR],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'config.wsgi.application'


# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql_psycopg2',
        'NAME': env("DB_NAME", default='dcs-app-dev'),
        'USER': env("DB_USER", default='postgres'),
        'PASSWORD': env("DB_PASSWORD", default='seragreen123'),
        'HOST': env("DB_HOST", default='localhost'),
        'PORT': env("DB_PORT", default='5432'),
    }
}


# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
    {
        'NAME': 'app.common.utils.CustomPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True
USE_L10N = True

USE_TZ = True
LOCALE_PATHS = [os.path.join(BASE_DIR.parent, 'locale')]
LANGUAGES = [
    ('ja', 'Japanese'),
    ('en', 'English'),
]


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_ROOT = os.path.join(BASE_DIR.parent, "staticfiles")
STATIC_URL = "/static/"
STATICFILES_DIRS = [os.path.join(BASE_DIR.parent, "app/static")]

MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR.parent, 'media')

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'
AUTH_USER_MODEL = 'user.User'
CORS_ALLOW_ALL_ORIGINS = True

Q_CLUSTER = {
    'name': 'DjangORM',
    'workers': 2,
    'timeout': 3600,
    'retry': 3601,
    'queue_limit': 5,
    'bulk': 3,
    'orm': 'default',
    'save_limit': 0,
    'ack_failures': True,
    'max_attempts': 1,
    'attempt_count': 1
}
APPEND_SLASH = False

# EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
EMAIL_HOST = "smtp.gmail.com"
EMAIL_PORT = 587
EMAIL_HOST_USER = "<EMAIL>"
EMAIL_HOST_PASSWORD = "ncpfgjoknwqekvvu"
# EMAIL_HOST_PASSWORD = 'ema211001'
EMAIL_USE_TLS = True
DEFAULT_FROM_EMAIL = "<EMAIL>"

TINYMCE_DEFAULT_CONFIG = {
    "height": "400px",
    "width": "100%",
    "menubar": True,
    "plugins": "advlist autolink lists link image charmap preview anchor searchreplace visualblocks code fullscreen insertdatetime media table code help wordcount",
    "toolbar": "undo redo | bold italic underline strikethrough | fontselect fontsizeselect formatselect | alignleft aligncenter alignright alignjustify | outdent indent | numlist bullist checklist | forecolor backcolor casechange permanentpen formatpainter removeformat | pagebreak | insertfile image media pageembed template link anchor codesample | charmap emoticons | fullscreen preview save print | a11ycheck ltr rtl | showcomments addcomment code",
    "custom_undo_redo_levels": 10,
    "images_upload_url": "/upload-image/",
    "relative_urls": False,
    "remove_script_host": False,
    "convert_urls": True,
    "valid_elements": "*[*]",
}

UPLOAD_PATH = "uploads"