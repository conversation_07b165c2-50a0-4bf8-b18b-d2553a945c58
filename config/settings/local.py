from .base import *

GRAPHQL_EXPLORE = True
SECRET_KEY = env("SECRET_KEY", default='bakery-app-django-insecure-4x=3#9f_566cm9y0v@*^!-sf#!mt@4ya53$qga)beot!-*c_xp')
ALLOWED_HOSTS = ["*"]
DEBUG = True
MEDIA_ROOT = env('MEDIA_ROOT', default="/var/www/dcs_app/media")
STATIC_ROOT = env('STATIC_ROOT', default="/var/www/dcs_app/static")
# MEDIA_ROOT = "/mnt/e/python/dcs-app/media"
# STATIC_ROOT = "/mnt/e/python/dcs-app/static"

LOGGING = {
    "version": 1,
	"disable_existing_loggers": False,
    "filters": {
        "require_debug_true": {
            "()": "django.utils.log.RequireDebugTrue",
        }
    },
	"formatters": {
        "production": {
            "format": "%(asctime)s================================================= \
            \n [%(levelname)s] %(process)d %(thread)d "
            "%(pathname)s:%(lineno)d \n[MESSAGE]%(message)s\n"
        },
        "simple_app": {
            "format": "=========================================================== \
            \n[{levelname}] {pathname}:{lineno} \n[MESSAGE] {message}\n",
            "style": "{",
        },
        "simple_django": {
            "format": "=========================================================== \
            \n[{levelname}] {pathname}:{lineno} \n[MESSAGE] {message}\n",
            "style": "{",
        },
    },
    "handlers": {
        "console": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
			"formatter": "simple_django",
        },
		"django_console": {
            "level": "INFO",
            "filters": ["require_debug_true"],
            "class": "logging.StreamHandler",
            "formatter": "simple_django",
        },
        'file': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': "./log/app.log",
            'formatter': 'production',
			'maxBytes': 15728640,  # 1024 * 1024 * 15B = 15MB
            'backupCount': 5,
        },
    },
    "loggers": {
        "app.master_data": {
            "handlers": ["console", "file"],
            "level": "DEBUG",
            "propagate": False,
        },
		"app.project": {
            "handlers": ["console", "file"],
            "level": "DEBUG",
            "propagate": False,
        },
		"app.user": {
            "handlers": ["console", "file"],
            "level": "DEBUG",
            "propagate": False,
        },
		"app.goods": {
            "handlers": ["console", "file"],
            "level": "INFO",
            "propagate": False,
        },
		"app.common": {
            "handlers": ["console", "file"],
            "level": "DEBUG",
            "propagate": False,
        },
        "app.rewards": {
            "handlers": ["console", "file"],
            "level": "DEBUG",
            "propagate": False,
        },
        "django": {
            "handlers": ["console", "file"],
            "level": "INFO",
            "propagate": False,
        },
		"graphene_django": {
            "handlers": ["console", "file"],
            "level": "INFO",
        },
        "django.db.backends": {
            "level": "DEBUG",
            "handlers": ["console"],
        }
    },
}

# DEBUG_PROPAGATE_EXCEPTIONS = True
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
DEFAULT_EMAIL_RECEIVER = ['<EMAIL>','<EMAIL>']