import graphene
import app.common.schema
import app.project.schema
import app.rewards
import app.rewards.schema
import app.user.schema
import app.master_data.schema
import app.goods.schema

class Query(
    app.user.schema.Query,
    app.master_data.schema.Query,
    app.project.schema.Query,
    app.goods.schema.Query,
    app.common.schema.Query,
    app.rewards.schema.Query,
    graphene.ObjectType
):
    ping = graphene.String(default_value="pong!")
    pass

class Mutation(
    app.user.schema.Mutation,
    app.master_data.schema.Mutation,
    app.project.schema.Mutation,
    app.goods.schema.Mutation,
    app.rewards.schema.Mutation,
    graphene.ObjectType
):
    pass
schema = graphene.Schema(query=Query, mutation=Mutation)