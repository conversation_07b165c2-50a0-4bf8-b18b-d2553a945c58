"""
URL configuration for config project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import include, path
from django.conf.urls.static import static
from django.views.decorators.csrf import csrf_exempt
from graphene_file_upload.django import FileUploadGraphQLView
from django.conf import settings

from app.common import views

urlpatterns = [
    path('admin/', admin.site.urls),
	path(
        "graphql",
        csrf_exempt(FileUploadGraphQLView.as_view(graphiql=settings.GRAPHQL_EXPLORE)),
    ),
	path('', include('app.project.urls')),
	path('', include('app.master_data.urls')),
	path('', include('app.rewards.urls')),
	path('tinymce/', include('tinymce.urls')),
	path("upload-image/", views.upload_image),
]

urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
